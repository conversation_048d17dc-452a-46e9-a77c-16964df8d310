# Copyright (c) 2021, Syskron GmbH. All rights reserved.

FROM python:3.13 as base

ENV SHELL /bin/bash
ENV PS1='🐳  \[\033[1;36m\]\u@\h \[\033[1;34m\]\w\[\033[0;35m\]\n  \[\033[1;36m\]# \[\033[0m\]'
ENV PYTHONUNBUFFERED=1
# install dependencies
RUN apt-get update \
    && pip install awscli poetry==2.0.* \
    && apt-get install -y postgresql-client zip curl

# node setup for openapi-generator
ENV NODE_MAJOR=18
RUN curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
    && apt-get update && apt-get install -t nodistro nodejs -y \
    && npm install -g npm@10

# install
RUN npm -g i \
    @openapitools/openapi-generator-cli \
    @semantic-release/exec@6 \
    @semantic-release/git@10 \
    conventional-changelog-conventionalcommits@7 \
    semantic-release@19

RUN curl -fsSL https://apt.corretto.aws/corretto.key | gpg --dearmor -o /etc/apt/keyrings/corretto-keyring.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/corretto-keyring.gpg] https://apt.corretto.aws stable main" | tee /etc/apt/sources.list.d/corretto.list \
    && apt-get update && apt-get install -y java-17-amazon-corretto-jdk

RUN mkdir /root/.ssh
COPY .jenkins/known_hosts /root/.ssh/known_hosts

USER root

WORKDIR /app
COPY ./pyproject.toml /app
COPY ./poetry.lock /app

ARG PYPI_USER
ARG PYPI_PASS

RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry self add poetry-plugin-export
RUN poetry export -f requirements.txt --output requirements.txt
RUN pip install -r requirements.txt

# Create image for local tests
FROM base as test-checkmat-curve-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./checkmat-curve-service/pyproject.toml /app
COPY ./checkmat-curve-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-comments-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./comments-service/pyproject.toml /app
COPY ./comments-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-custom-database-user-creator

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./custom-database-user-creator/pyproject.toml /app
COPY ./custom-database-user-creator/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-customer-settings-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./customer-settings-service/pyproject.toml /app
COPY ./customer-settings-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-performance-analytics-kpi-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./performance-analytics-kpi-service/pyproject.toml /app
COPY ./performance-analytics-kpi-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-performance-analytics-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./performance-analytics-service/pyproject.toml /app
COPY ./performance-analytics-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-performance-analytics-service-config-generator

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./performance-analytics-service-config-generator/pyproject.toml /app
COPY ./performance-analytics-service-config-generator/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-performance-analytics-units-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./performance-analytics-units-service/pyproject.toml /app
COPY ./performance-analytics-units-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-products-speeds-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./products-speeds-service/pyproject.toml /app
COPY ./products-speeds-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install

# Create image for local tests
FROM base as test-threshold-service

ARG PYPI_USER
ARG PYPI_PASS

WORKDIR /app
COPY ./threshold-service/pyproject.toml /app
COPY ./threshold-service/poetry.lock /app

RUN	poetry config virtualenvs.in-project false
RUN poetry config http-basic.syskron-jfrog $PYPI_USER $PYPI_PASS
RUN poetry install
