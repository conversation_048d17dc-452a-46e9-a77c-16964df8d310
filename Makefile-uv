_TEST_FILES_OR_DIRS := $(strip $(subst ",,$(TEST_FILES_OR_DIRS)))

# Path definitions for lambda-layer
## Install path of dependencies relative to repository root-dir
LAMBDA_LAYER_PYTHON_LIBS := build/libs/python
## Location of shared sources relative to repository root-dir
LAMBDA_LAYER_SHARED_LIBS := $(LAMBDA_LAYER_PYTHON_LIBS)/shared
## Location of shared sources relative to repository root-dir
SHARED_SRC_PATH := $(SRC_DIR)/shared

## Location of dist artefacts relative to repository root-dir
BUNDLE_DIR := dist
ZIP_FILE ?= $(SERVICE)-lambda-layer.zip

###############################
#######    Install    #########
###############################

.PHONY: install
install: install-uv install-pre-commit

.PHONY: install-ci
install-ci: install-uv configure-ssh

.PHONY: install-uv
install-uv:
	@echo "███ Running uv install..."
	@uv sync --default-index https://$(PYPI_USER):$(PYPI_PASS)@syskronx.jfrog.io/syskronx/api/pypi/pypi/simple
	@echo

.PHONY: install-pre-commit
install-pre-commit:
	@echo "███ Running pre-commit sync..."
	uv run pre-commit install --install-hooks
	@echo

.PHONY: update
update: lock install-uv

.PHONY: lock
lock:
	@echo "███ Running uv lock"
	uv lock
	@echo


#################################
#########    Format    ##########
#################################

.PHONY: format
format:
	@echo "███ Running format..."
	uv run ruff format
	@echo

###############################
#########    Lint    ##########
###############################

.PHONY: lint
lint: typecheck
	@echo "███ Running lint..."
	uv run ruff check
	@echo

.PHONY: lint-fix
lint-fix:
	@echo "███ Running lint with fixes..."
	uv run ruff check --fix
	@echo

.PHONY: lint-unsafe-fix
lint-unsafe-fix:
	@echo "███ Running lint with unsafe fixes..."
	uv run ruff check --fix --unsafe-fixes
	@echo

####################################
#########    Typecheck    ##########
####################################

.PHONY: typecheck
typecheck:
	@echo "███ Running typecheck ..."
	uv run python -c "import sys; import toml; import re; config = toml.load('pyproject.toml'); exclude = tuple(config.get('tool', {}).get('mypy', {}).get('exclude', [])); files = [f for f in sys.argv[1:] if not any(re.match(pattern, f) for pattern in exclude)]; print(' '.join(files));" $$(git ls-files -- '*.py') | xargs uv run mypy;\
	RESULT=$$?;\
	echo;\
	exit $$RESULT

################################
#########   Clean    ###########
################################

.PHONY: clean
clean:
	@echo "███ Running clean..."
	rm -rf ${LAMBDA_LAYER_PYTHON_LIBS}
	rm -rf ${BUNDLE_DIR}
	rm -rf cdk.out/
	rm -f requirements.txt
	@echo

###############################
#########    Test    ##########
###############################

.PHONY: test
test:
	@echo "███ Running test..."
	uv run pytest --cov-report term-missing --cov=. ${_TEST_FILES_OR_DIRS}
	uv run coverage xml
	@echo

################################
#########   Bundle   ###########
################################

.PHONY: bundle-lambda-layers
bundle-lambda-layers: clean
	@echo "███ Running bundle-lambda-layers ..."
	mkdir -p ${LAMBDA_LAYER_SHARED_LIBS} ${BUNDLE_DIR}
	uv pip compile pyproject.toml -o requirements.txt --quiet
ifneq ($(PLATFORM),)
	@echo "Running with platform ${PLATFORM}"
	uv pip install --no-deps --requirement requirements.txt --target ${LAMBDA_LAYER_PYTHON_LIBS} --python-platform $(PLATFORM) --default-index https://$(PYPI_USER):$(PYPI_PASS)@syskronx.jfrog.io/syskronx/api/pypi/pypi/simple
else
	uv pip install --no-deps --requirement requirements.txt --target ${LAMBDA_LAYER_PYTHON_LIBS} --default-index https://$(PYPI_USER):$(PYPI_PASS)@syskronx.jfrog.io/syskronx/api/pypi/pypi/simple
endif
ifneq ($(wildcard ${SHARED_SRC_PATH}/.*),)
	cp -R ${SHARED_SRC_PATH}/* ${LAMBDA_LAYER_SHARED_LIBS}
else
	@echo "Folder does not exist in src: ${SHARED_SRC_PATH}. Skipping copy."
endif
	cd build/libs/; zip -qr ../../$(BUNDLE_DIR)/$(ZIP_FILE) python/ ; cd ../../
	rm requirements.txt
	@echo

################################
#########   Publish   ##########
################################

.PHONY: publish
publish:
	@echo "███ Running uv publish ..."
	@uv publish --publish-url https://syskronx.jfrog.io/artifactory/api/pypi/pypi-local --username $(PYPI_USER) --password $(PYPI_PASS)
	@echo

################################################################
#########    Fall back to targets in Makefile     ##############
################################################################

.PHONY: %
%: .configs/Makefile-uv
	@$(MAKE) -e -f .configs/Makefile $@

.PHONY: .configs/Makefile-uv
.configs/Makefile-uv: ;
