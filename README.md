[![pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://github.com/pre-commit/pre-commit) [![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

| Master                                                                                                                                                                                                                                            | Test                                                                                                                                                                                                                                          | Prod                                                                                                                                                                                                                                          |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [![Build Status](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/master/badge/icon)](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/master/) | [![Build Status](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/test/badge/icon)](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/test/) | [![Build Status](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/prod/badge/icon)](https://s2a.jenkins.syskron.com/job/S2A.Service.Performance/job/performance-analytics-service/job/prod/) |

# Performance Analytics Service

Refer to the [concept in confluence](https://pd.confluence.syskron.com/pages/viewpage.action?spaceKey=BPI&title=Concept%3A+Performance+Analytics).

## Development Setup

### Run tests in PyCharm or Visual Studio Code (new workflow)

An in-depth documentation, also for PyCharm and VsCode, can be found here:
https://confluence.syskron.com/display/STTS/Docker+for+development

### Visual Studio Code (old workflow)

To work with this repository it is necessary to open it as a VSCode workspace.

**File** &rarr; **Open Workspace...** &rarr; Select the **Performance.code-workspace** file in the root folder of this repository

#### poetry Initialization

To work with a service **Terminal** &rarr; **New Terminal** &rarr; Select the service you want to work with

`make install`

If you want to do it for all services once you can run the `make install` command also in the **root** folder. It will recursively install the dependencies for all services.

## Linting

`make lint` Validate PEP8 compliance for Python source files. It uses pylint, flake8 and mypy.

## Testing

### Run Tests Locally in VSCode

Open the Test Explorer in VSCode and run the test you like. There are some integration tests which can not be run with VSCode. Please refer to [Run Tests Locally with Docker](#test-docker)

The other way is to run it in the shell.

`make test`

### Run Tests Locally with Docker <a id="test-docker"></a> (new workflow)
Set up your local test environment using a multi-stage dockerfile to create docker images and containers. 

The test stages are used to create local docker images and containers to finally run unit tests in a predefined environment.
Use
```
make docker-build
```
to create individual docker images for all sub-services containing all dependencies of the sub-service or
```
make docker-build service=<service_name>
```
to create an image for a specified service.

Finally use
```
make docker-shell service=<service_name>
```
to run an interactive docker container session to perform tests for the individual service in the container or run
```
make docker-test
```
to run all tests for all sub-services.

In some cases it could be useful to start a container in background (detached). In this case use
```
make docker-shell-detached service=<servie_name>
```

Once the development and testing of the service is done use
```
make docker-clean
```
to remove also images/containers directly related to the project. Note that additional artifacts, e.g., created by
the IDE or docker, have to be removed manually using
`docker container prune && docker image prune && docker builder prune`.

##### Important: Rebuild this container when dependencies change using `make docker-build`!

####Dependencies:

Docker has to be installed on the host system. Further
```
$PYPI_USER
$PYPI_PASS
```
have to be set as environment variables.
If code changes contain the introduction of further dependencies the docker image has to be re-build to execute
the individual make install commands. If further services are introduced it is also necessary to extend the test stage of the Dockerfile.

####Additional resources:

An in-depth documentation, also for PyCharm and VsCode, can be found here:
https://confluence.syskron.com/display/STTS/Docker+for+development

### Run Tests Locally with Docker <a id="test-docker"></a> (old workflow)

#### Initializing Docker

Tests should be executed inside docker-compose. Use these commands to setup the docker environment:

```
make start
make shell
```

You will end up in a bash command line inside the docker. In here run:
`make docker-init`
This will setup the postgres data-base and tables for the integration tests. It will also add a configuration for pip so that it is retrieving packages from our JFrog Artifactory. It will retrieve credentials from the bash environemnt. See next step.

#### Setting up your pip credentials

For installing you need credentials for Syskron's JFrog Artifactory. You can export your credentials manually in the container:

```
export PYPI_USER=<username>
export PYPI_PASS=<password>
```

Alternatively docker-compose will try to retrieve your credentials from your terminal by forwarding environment variables (PYPI_PASS and PYPI_USER) to the docker-container.

#### Running tests

The different packages have conflicting dependencies. There are commands to install and test all packages at once (`make install` & `make test`) but this is not recommended. If you want to test a package then navigate to it (`cd <package_name>`) and run `make install` and ` make test` here.

## Overview

### Argument

```json
{
  "requestContext": {
    "httpMethod": "POST",
    "resourcePath": "performance-analytics/stoppage-analysis/overview"
  },
  "authorizerContext": {
    "accountId": "Dev",
    "scopes": ["performance-analytics"]
  },
  "payload": {
    "machines": ["first-machine-id", "second-machine-id"],
    "time": {
      "start_time": "2019-08-01T04:00:00Z",
      "end_time": "2019-08-02T04:00:00Z"
    },
    "language": "de",
    "limit": 10,
    "max_duration": 5000,
    "min_duration": 300000,
    "filter_categories": ["breakdown", "external"]
  }
}
```

## Failure Details

### Argument

```json
{
    "requestContext": {
        "httpMethod": "POST",
        "resourcePath": "performance-analytics/stoppage-analysis/machine-details"
    },
    "authorizerContext":{
        "accountId": "Dev",
        "scopes": ["performance-analytics"]
    },
    "payload": {
        "machine": "filler_uuid",
        "failure_mode": 8089,
        "time": {
            "start_time": "2019-08-01T04:00:00Z",
            "end_time": "2019-08-02T04:00:00Z"
        },
        "max_duration": 5000,
        "min_duration": 300000,
        "filter_categories": [
            "breakdown",
            "external"
        ]
}
```

## Threshold Service

### POST

```json
{
  "line": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
  "machines": [
    "63dbe09b-d422-4a56-8867-7c04e286a1a6",
    "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
  ]
}
```

### PUT

```json
{
  "line": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
  "config": {
    "duration": {
      "machines": {
        "63dbe09b-d422-4a56-8867-7c04e286a1a6": {
          "failure_modes": {
            "default": {
              "error_level": 1800000,
              "warning_level": 900000
            }
          }
        },
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73": {
          "failure_modes": {
            "default": {
              "error_level": 1800000,
              "warning_level": 900000
            }
          }
        }
      }
    },
    "quantity": {
      "machines": {
        "63dbe09b-d422-4a56-8867-7c04e286a1a6": {
          "failure_modes": {
            "default": {
              "error_level": 10,
              "warning_level": 3
            }
          }
        },
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73": {
          "failure_modes": {
            "default": {
              "error_level": 10,
              "warning_level": 3
            }
          }
        }
      }
    }
  }
}
```

## Customer Settings Service

### GET

https://api.rk.share2act-dev.io/v1/performance-analytics/customer-settings?line=5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22

### Response

```json
{
  "account": "readykit-replay",
  "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
  "kpi_model": "opi",
  "settings": {},
  "created_at": 1,
  "updated_at": 1
}
```

### POST

https://api.rk.share2act-dev.io/v1/performance-analytics/customer-settings

### Body

```json
{
  "line": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
  "kpi_model": "opi"
}
```

## Units Report Service

### GET

https://api.rk.share2act-dev.io/v1/performance-analytics/units-report/5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22/c5cbe18b-be13-4554-b9b4-defd62e671c0?time_from=*************&time_to=*************

### Response

```json
{
  "customer": "readykit-replay",
  "eq_id": "c5cbe18b-be13-4554-b9b4-defd62e671c0",
  "time_from": *************,
  "time_to": *************,
  "units_produced": 33,
  "units_defect": 0,
  "units_total": 33,
  "sender": {
    "name": "performance_analytics",
    "version": "1.3.7"
  }
}
```

### Jenkins Dockerfile is saved in AWS ECR

docker repo: https://pd.bitbucket.syskron.com/projects/S2A_PERF/repos/docker/browse

pull locally: `aws ecr get-login-password --profile shared_services --region eu-central-1 | docker login --username AWS --password-stdin 031655394050.dkr.ecr.eu-central-1.amazonaws.com`
