#POETRY

[tool.poetry]
name = "checkmat-curve-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
lib-performance-analytics = "^32"
ft2-cloud-sdk = "^16"
fastapi = "^0"
mangum = "^0"
click = "^8"
wrapt = "^1"
requests = "*"
Secweb = "*"


[tool.poetry.group.dev.dependencies]
pre-commit = "*"
pylint = "^3"
boto3 = "^1.35"
pytest = "^8"
pytest-cov = "*"
mock = "^5"
moto = "^4"
black = "^24"
flake8 = "^7"
httpx = "*"
uvicorn = "^0"  # pin to that version due to "h11" not beeing installed otherwise

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

#BLACK

[tool.black]
line-length = 100

#PYTEST

[tool.pytest.ini_options]
addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = "./src"

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 82
