SHELL:=/bin/bash

.DEFAULT_GOAL := help

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) |  awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

GIT_COMMIT = $(shell git rev-parse HEAD)
BRANCH_NAME = $(shell git symbolic-ref --short HEAD)

CWD := $(shell pwd)
SERVICE_PATH = $(shell cd ../; pwd)
SERVICE_NAME ?= $(shell basename $(SERVICE_PATH))
FUNCTION_NAME ?= $(shell basename $(CWD))

JUNIT_XML ?= $(SERVICE_PATH)/test-reports/report/$(FUNCTION_NAME).xml
COV_REPORT_XML ?= $(SERVICE_PATH)/test-reports/coverage/$(FUNCTION_NAME).xml

RK_BUCKET ?= rk-deploy-jenkins-dev
S3_PATH = $(SERVICE_NAME)/$(BRANCH_NAME)
ZIP_FILE ?= $(FUNCTION_NAME)-$(GIT_COMMIT).zip

.PHONY: init
init:   ## init poetry
	poetry config virtualenvs.in-project true
	poetry config http-basic.syskron-jfrog $(PYPI_USER) $(PYPI_PASS)

.PHONY: install
install:
	poetry install --no-root

.PHONY: lock
lock: 	## lock the dependencies
	poetry lock

.PHONY: lint
lint: black flake8 pylint ## Validate PEP8 compliance for Python source files.

.PHONY: pylint
pylint:
	poetry run pylint src/ --rcfile=$(SERVICE_PATH)/.pylintrc

.PHONY: flake8
flake8:
	git ls-files -- '*.py' -x ':!:test/' | poetry run xargs pre-commit run flake8 --files

.PHONY: black
black:
	git ls-files -- '*.py' -x ':!:test/' | poetry run xargs pre-commit run black --files

.PHONY: test
test: ## run test and generate reports
	AWS_XRAY_CONTEXT_MISSING=LOG_ERROR \
	AWS_XRAY_DEBUG_MODE=TRUE \
	poetry run pytest test/ \
		--junitxml=$(JUNIT_XML) \
		--cov=src;
	poetry run coverage xml -o $(COV_REPORT_XML)
	sed -i -e 's/\(package.* name="\)\(.*\)/\1$(FUNCTION_NAME).\2/' $(COV_REPORT_XML)
	sed -i -e 's/\(filename="\)\(.*\)/\1$(FUNCTION_NAME)\/\2/' $(COV_REPORT_XML)

.PHONY: bundle
bundle: clean ## bundles the service
	mkdir -p build/libs/ build/distributions/
	poetry export --output requirements.txt --with-credentials
	poetry run pip install --no-deps -r requirements.txt -t build/libs
	cd build/libs/; zip -qr ../../build/distributions/$(ZIP_FILE) . ; cd ../../
	cd src/; zip -qr ../build/distributions/$(ZIP_FILE) . ; cd ../

.PHONY: clean
clean:
	rm -rf build/
	rm -rf dist/
	rm -f requirements.txt

####################################################
# update this specific lambda function manually
update-fkn: upload update-lambda-code

upload:
	aws s3 cp build/distributions/$(ZIP_FILE) s3://$(RK_BUCKET)/$(S3_PATH)/

update-lambda-code:
	aws lambda update-function-code --function-name $(FUNCTION_NAME) --s3-bucket $(RK_BUCKET) --s3-key $(S3_PATH)/$(ZIP_FILE) --no-cli-pager
