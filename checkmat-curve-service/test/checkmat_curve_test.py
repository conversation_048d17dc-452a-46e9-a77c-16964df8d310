# Copyright (c) 2020, Syskron GmbH. All rights reserved.

# pylint: disable=import-outside-toplevel

# THIS SERVICE HAS NO INTEGRATION TEST. WE SHOULD ADD THEM OTHERWISE YOU CAN CHANGE SOMETHING AND ALL THE TESTS STILL WORK.

import json
import pytest

from fastapi import status

from models.checkmat_curve import CheckmatCurveResponse

BEGIN = 1 * 60 * 60 * 1000
END = 11 * 60 * 60 * 1000


def _read_json_file(file_name: str) -> dict:
    with open(file_name) as file_obj:
        file_data = file_obj.read()
    return json.loads(file_data)


@pytest.fixture
def document_constant_speed():
    return {
        "time_from": BEGIN,
        "time_to": END,
        "units_produced": 90000,
        "units_defect": 10000,
        "speeds": [
            {
                "start": BEGIN,
                "end": END,
                "current_speed": 10000,
                "units_produced": 90000,
                "units_defect": 10000,
                "units_total": 100000,
            },
        ],
    }


@pytest.fixture
def document_changing_speed():
    return {
        "time_from": BEGIN,
        "time_to": END,
        "speeds": [
            {
                "start": BEGIN,
                "end": 4 * 60 * 60 * 1000,
                "current_speed": 7000,
                "units_produced": 22500,
                "units_defect": 2500,
                "units_total": 2500,
            },
            {
                "start": 4 * 60 * 60 * 1000,
                "end": 6 * 60 * 60 * 1000,
                "current_speed": 0,
                "units_produced": 0,
                "units_defect": 0,
                "units_total": 0,
            },
            {
                "start": 6 * 60 * 60 * 1000,
                "end": 10 * 60 * 60 * 1000,
                "current_speed": 10000,
                "units_produced": 30000,
                "units_defect": 3000,
                "units_total": 33000,
            },
            {
                "start": 10 * 60 * 60 * 1000,
                "end": END,
                "current_speed": 8000,
                "units_produced": 27500,
                "units_defect": 2700,
                "units_total": 30200,
            },
        ],
    }


@pytest.mark.parametrize(
    "title,num_buckets,expected_checkmat_curve",
    [
        (
            "One bucket",
            1,
            [CheckmatCurveResponse.model_validate({"time": END, "average_speed": 9000.0})],
        ),
        (
            "Two buckets",
            2,
            [
                CheckmatCurveResponse.model_validate(
                    {
                        "time": BEGIN + (END - BEGIN) / 2,
                        "average_speed": 9000.0,
                    }
                ),
                CheckmatCurveResponse.model_validate(
                    {
                        "time": END,
                        "average_speed": 9000.0,
                    }
                ),
            ],
        ),
        (
            "Four buckets",
            4,
            [
                CheckmatCurveResponse.model_validate(
                    {
                        "time": BEGIN + (END - BEGIN) / 4,
                        "average_speed": 9000.0,
                    }
                ),
                CheckmatCurveResponse.model_validate(
                    {
                        "time": BEGIN + 2 * (END - BEGIN) / 4,
                        "average_speed": 9000.0,
                    }
                ),
                CheckmatCurveResponse.model_validate(
                    {
                        "time": BEGIN + 3 * (END - BEGIN) / 4,
                        "average_speed": 9000.0,
                    }
                ),
                CheckmatCurveResponse.model_validate(
                    {
                        "time": END,
                        "average_speed": 9000.0,
                    }
                ),
            ],
        ),
    ],
)
def test_calculate_checkmat_curve_constant_speed(
    document_constant_speed, title, num_buckets, expected_checkmat_curve
):
    from api.endpoints.checkmat_curve_calculation import CheckmatCurve

    checkmat_curve = CheckmatCurve(document_constant_speed, num_buckets)
    calculated_checkmat_curve = checkmat_curve.calculate(
        "readykit-replay", "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    )

    assert calculated_checkmat_curve == expected_checkmat_curve, title


def test_calculate_checkmat_curve_changing_speed(document_changing_speed):
    from api.endpoints.checkmat_curve_calculation import CheckmatCurve

    from models.checkmat_curve import CheckmatCurveResponse

    checkmat_curve = CheckmatCurve(document_changing_speed, 5)
    calculated_checkmat_curve = checkmat_curve.calculate(
        "readykit-replay", "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    )

    assert calculated_checkmat_curve == [
        CheckmatCurveResponse(
            **{
                "time": 3 * 60 * 60 * 1000,
                "average_speed": 7500.0,
            }
        ),
        CheckmatCurveResponse(
            **{
                "time": 5 * 60 * 60 * 1000,
                "average_speed": 5625.0,
            }
        ),
        CheckmatCurveResponse(
            **{
                "time": 7 * 60 * 60 * 1000,
                "average_speed": 5000.0,
            }
        ),
        CheckmatCurveResponse(
            **{
                "time": 9 * 60 * 60 * 1000,
                "average_speed": 5625.0,
            }
        ),
        CheckmatCurveResponse(
            **{
                "time": 11 * 60 * 60 * 1000,
                "average_speed": 8000.0,
            }
        ),
    ]


def test_calculate_checkmat_with_real_data() -> None:
    from api.endpoints.checkmat_curve_calculation import CheckmatCurve

    from models.checkmat_curve import CheckmatCurveResponse

    document = _read_json_file("test/res/expected_response_body.json")

    checkmat_curve = CheckmatCurve(document, 10)
    calculated_checkmat_curve = checkmat_curve.calculate(
        "readykit-replay", "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    )

    expected_checkmat_curve = _read_json_file("test/res/expected_checkmat_curve.json")
    expected_w_model = []
    for expected in expected_checkmat_curve:
        expected_w_model.append(CheckmatCurveResponse(**expected))

    assert calculated_checkmat_curve == expected_w_model


def test_get_404(dynamo_db_tables_mock):
    from fastapi.testclient import TestClient

    from main import app

    # Prepare URL
    base_path = "/v1/performance-analytics/checkmat-curve"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=1621339200000&timeTo=1621368000000"
    url = f"{base_path}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)

    assert response.status_code == 404
    assert response.text == '{"detail":"Speeds array is empty for the given arguments"}'


def test_get_checkmat_curve_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from main import app

    # Prepare URL
    base_path = "/v1/performance-analytics/checkmat-curve"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=1710028800000&timeTo=1710892800000"
    url = f"{base_path}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}
