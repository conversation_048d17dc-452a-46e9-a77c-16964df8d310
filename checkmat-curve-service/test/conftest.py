# System import
import json
import os
import site

import boto3

# Library import
import pytest
from fastapi import Request
from unittest.mock import patch
from moto import mock_dynamodb

# we set our site dir to src to have proper package names
MODULE_DIR_PATH = os.path.dirname(os.path.realpath(__file__))
source_dir = os.path.join(MODULE_DIR_PATH, "..", "src")
site.addsitedir(source_dir)


@pytest.fixture
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["SQS_QUEUENAME"] = "testing"
    os.environ["SQS_URL"] = "https://eu-central-1.queue.amazonaws.com/testing"
    os.environ["AWS_STAGE"] = "testing"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"


_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["scope-1", "scope-2"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps(
            {
                "userId": "some-user-id",
                "username": "some-user-name",
                "login": "some-login",
                "groups": ["group-1", "site-manager"],
            }
        ),
        "account": json.dumps({"accountId": "readykit-replay", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture
def aws_request_mock():
    event = {
        "aws.event": {"requestContext": _request_context},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    yield request


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch(
        "performance_analytics.utility.s2a_request_wrapper.CommonShare2ActProperties"
    ) as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def dynamo_db_tables_mock(aws_credentials):
    with mock_dynamodb():
        db_client = boto3.resource("dynamodb", region_name="eu-central-1")
        rk_performance_customer_settings = db_client.create_table(
            TableName="rk-performance-customer-settings",
            KeySchema=[
                {"AttributeName": "account", "KeyType": "HASH"},
                {
                    "AttributeName": "line_id",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "account", "AttributeType": "S"},
                {"AttributeName": "line_id", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        rk_downtime_categories = db_client.create_table(
            TableName="rk-downtime-categories",
            KeySchema=[{"AttributeName": "kpi_model", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "kpi_model", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        performance_data = db_client.create_table(
            TableName="performance-data",
            KeySchema=[
                {"AttributeName": "rk_eq_id", "KeyType": "HASH"},
                {
                    "AttributeName": "time_from",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "rk_eq_id", "AttributeType": "S"},
                {"AttributeName": "time_from", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        rk_kpi_fault = db_client.create_table(
            TableName="rk-kpi-fault",
            KeySchema=[
                {"AttributeName": "rk_eq_id", "KeyType": "HASH"},
                {
                    "AttributeName": "time_from",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "rk_eq_id", "AttributeType": "S"},
                {"AttributeName": "time_from", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )

        yield {
            "rk_performance_customer_settings": rk_performance_customer_settings,
            "rk_downtime_categories": rk_downtime_categories,
            "performance_data": performance_data,
            "rk_kpi_fault": rk_kpi_fault,
        }
