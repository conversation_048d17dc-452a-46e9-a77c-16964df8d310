openapi: 3.0.2
info:
  title: FastAPI
  version: 0.1.0
paths:
  /v1/performance-analytics/checkmat-curve/{equipment_id}:
    get:
      tags:
        - checkmat-curve
      summary: Get Checkmat Curve
      description: |-
        Get all checkmat curve data within the specified timerange for a specific equipment_id:

        - **timeFrom**: required to specify the start of the timerange
        - **timeTo**: required to specify the end of the timerange
      operationId: get_checkmat_curve_v1_performance_analytics_checkmat_curve__equipment_id__get
      parameters:
        - required: true
          schema:
            title: Equipment Id
            type: string
          name: equipment_id
          in: path
        - required: true
          schema:
            title: Timefrom
            type: integer
          name: timeFrom
          in: query
        - required: true
          schema:
            title: Timeto
            type: integer
          name: timeTo
          in: query
        - required: false
          schema:
            title: Numbuckets
            type: integer
            default: 240
          name: numBuckets
          in: query
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                title: Response Get Checkmat Curve V1 Performance Analytics Checkmat Curve  Equipment Id  Get
                type: array
                items:
                  $ref: '#/components/schemas/CheckmatCurveResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    CheckmatCurveResponse:
      title: CheckmatCurveResponse
      required:
        - time
        - average_speed
      type: object
      properties:
        time:
          title: Time
          type: integer
        average_speed:
          title: Average Speed
          type: number
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            type: string
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
tags:
  - name: checkmat-curve
    description: API for getting the checkmat curve
