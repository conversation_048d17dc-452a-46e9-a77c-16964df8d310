from aws_lambda_powertools import Logger
from fastapi import APIRouter, HTTPException, Query, Request, Depends
from machine_data_query.query_speeds import query_speeds
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties

from models.checkmat_curve import CheckmatCurveResponse

from .checkmat_curve_calculation import CheckmatCurve

from lib_cloud_sdk.util.common import validate_fast_api_timerange

router = APIRouter()

LOGGER = Logger()

DEFAULT_CHECKMAT_CURVE_NUM_BUCKETS = 8 * 30


@router.get(
    "/checkmat-curve/{equipment_id}",
    response_model=list[CheckmatCurveResponse],
    tags=["checkmat-curve"],
    dependencies=[Depends(validate_fast_api_timerange)],
)
def get_checkmat_curve(
    request: Request,
    equipment_id: str,
    properties: Share2ActProperties,
    time_from: int = Query(..., alias="timeFrom"),
    time_to: int = Query(..., alias="timeTo"),
    num_buckets: int = Query(DEFAULT_CHECKMAT_CURVE_NUM_BUCKETS, alias="numBuckets"),
) -> list[CheckmatCurveResponse]:
    """
    Get all checkmat curve data within the specified timerange for a specific equipment_id:

    - **timeFrom**: required to specify the start of the timerange
    - **timeTo**: required to specify the end of the timerange
    """

    try:
        speeds, _, _ = query_speeds(
            time_from=time_from,
            time_to=time_to,
            account=properties.account,
            machine_id=equipment_id,
        )

        if not speeds:
            LOGGER.warning(
                "No data received for account=%s, equipment_id=%s "
                + "and time_from=%i, request=%s",
                properties.account,
                equipment_id,
                time_from,
                request,
            )
            raise HTTPException(
                status_code=404,
                detail="No speeds found for the given arguments.",
            )
        if not speeds.items:
            LOGGER.warning(
                "Speeds array is empty for account=%s, "
                + "equipment_id=%s and time_from=%i, request=%s",
                properties.account,
                equipment_id,
                time_from,
                request,
            )
            raise HTTPException(
                status_code=404,
                detail="Speeds array is empty for the given arguments",
            )

        speeds_document = {
            "time_from": speeds.time_from,
            "time_to": speeds.time_to,
            "speeds": [item.dict(exclude_none=True) for item in speeds.items],
        }

        checkmat_curve = CheckmatCurve(speeds_document, num_buckets)

        response = checkmat_curve.calculate(properties.account, equipment_id)
    except HTTPException as http_excep:
        LOGGER.warning(
            "HTTPException get_checkmat_curve failed: %s, request=%s",
            http_excep,
            request,
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "Exception get_checkmat_curve failed: %s, request=%s",
            excep,
            request,
            exc_info=True,
        )
        raise excep

    return response
