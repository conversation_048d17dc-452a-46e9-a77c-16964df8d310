# Copyright (c) 2020, Syskron GmbH. All rights reserved.

"""Calculate the checkmat curve from a given speeds list."""

# System import
from typing import Any, Iterable

# Library import
from aws_lambda_powertools import Logger
from machine_data_split.split_document import split_document

from models.checkmat_curve import CheckMatCurve, CheckmatCurveResponse

Config = dict[str, Any]
Document = dict[str, Any]

LOGGER = Logger()

DEFAULT_NUM_BUCKETS = 8 * 30
ROUND_DIGITS = 6


class CheckmatCurve:
    def __init__(self, speeds: Document, checkmat_curve_num_buckets: int, config: Config = None):
        self._speeds = speeds
        self._checkmat_curve_num_buckets = checkmat_curve_num_buckets
        self._checkmat_curve_config = config

    def calculate(self, account: str, machine_id: str) -> list[CheckmatCurveResponse]:
        """
        Calculate checkmat curve if requested by client.
        """
        checkmat_result = []
        checkmat_result = self._calculate_checkmat_curve(
            self._speeds, self._checkmat_curve_num_buckets
        )

        checkmat_curve_responses = []
        for item in checkmat_result:
            response = CheckmatCurveResponse(
                time=item.get("end"), average_speed=item.get("average_units_per_time")
            )
            checkmat_curve_responses.append(response)

        return checkmat_curve_responses

    @staticmethod
    def _calculate_checkmat_curve(speeds: Document, num_buckets: int) -> list[CheckMatCurve]:
        begin = int(speeds["time_from"])
        end = int(speeds["time_to"])
        interval_length = end - begin
        bucket_length = round(interval_length / num_buckets)
        last_speed_end = int(speeds["speeds"][-1]["end"])
        end = min(end, last_speed_end)
        timestamps = [min(end, t) for t in range(begin, end + bucket_length, bucket_length)]
        speed_documents = split_document(speeds, timestamps)

        return list(CheckmatCurve._process_checkmat_curve(speed_documents, begin))

    @staticmethod
    def _process_checkmat_curve(
        speed_documents: Iterable[Document], begin: int
    ) -> Iterable[CheckMatCurve]:
        cumulated_counter = 0.0
        for speed_document in speed_documents:
            speed_document["start"] = speed_document["time_from"]
            del speed_document["time_from"]
            speed_document["end"] = speed_document["time_to"]
            del speed_document["time_to"]
            processed_speed_document = CheckmatCurve._process_speed_document(speed_document)
            cumulated_counter += processed_speed_document["units_counter"]
            cumulated_time = processed_speed_document["end"] - begin
            speed_document["cumulated_units_counter"] = round(cumulated_counter, ROUND_DIGITS)
            speed_document["average_units_per_time"] = round(
                60 * 60 * 1000 * cumulated_counter / cumulated_time, ROUND_DIGITS
            )
            yield speed_document

    @staticmethod
    def _process_speed_document(speed_document: Document) -> dict[str, Any]:
        current_document_units_produced = 0.0
        for speed_item in speed_document["speeds"]:
            current_document_units_produced += float(speed_item.get("units_produced") or 0.0)
        del speed_document["speeds"]
        return {
            "start": speed_document["start"],
            "end": speed_document["end"],
            "units_counter": current_document_units_produced,
        }
