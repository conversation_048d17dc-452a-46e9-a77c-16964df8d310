# Copyright (c) 2019, Syskron GmbH. All rights reserved.

# System import
import json
from http import HTTPStatus

import pytest

# Library import
from mock import Mock, patch

GET_CUSTOMER_SETTINGS = Mock()
CREATE_CUSTOMER_SETTINGS = Mock()


def setup_module(module):
    module.patcher = patch.dict('sys.modules', {'get': GET_CUSTOMER_SETTINGS, 'create': CREATE_CUSTOMER_SETTINGS})
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture(autouse=True)
def get_customer_settings():
    GET_CUSTOMER_SETTINGS.get_configuration.return_value = {'test': 'configuration'}
    GET_CUSTOMER_SETTINGS.reset_mock()
    return GET_CUSTOMER_SETTINGS


@pytest.fixture(autouse=True)
def create_customer_settings():
    CREATE_CUSTOMER_SETTINGS.create_configuration.return_value = {'created': 'configuration'}
    CREATE_CUSTOMER_SETTINGS.reset_mock()
    return CREATE_CUSTOMER_SETTINGS


@pytest.fixture
def event():
    return {
        'requestContext': {
            'httpMethod': 'GET',
            'resourcePath': 'performance-analytics/customer-settings',
            'authorizer': {
                'claims': '{"scopes": ["performance-analytics"]}',
                'account': '{"accountId":"test-account"}',
                'user': '{"userId": "test user id", "groups": []}',
            },
        },
        'queryStringParameters': {'line': 'test-line'},
        'path': 'performance-analytics/customer-settings',
        'body': '{ "line": "test-line", "kpi_model": "test_model"}',
    }


@pytest.fixture
def lambda_handler():
    """
    Mock for lambda_function module.
    It just imports and returns the lambda handler.
    """
    from src.lambda_function import lambda_handler

    return lambda_handler


def test_lambda_returns_configuration(lambda_handler, event):
    """Test whether the expected configuration is returned."""
    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.OK
    assert result['body'] == '{"test": "configuration"}'


def test_lambda_returns_not_found(lambda_handler, event):
    """Test whether Lambda responds with NOT FOUND """
    """if no configuration is available in DynamoDB for the entered account and line."""
    # Arrange
    GET_CUSTOMER_SETTINGS.get_configuration.return_value = None

    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.NOT_FOUND
    assert 'errorMessage' in json.loads(result['body'])


def test_lambda_returns_bad_request_for_get(lambda_handler, event):
    """Test whether Lambda responds with BAD REQUEST if request does not contain the line."""
    # Arrange
    del event['queryStringParameters']['line']

    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.BAD_REQUEST
    assert 'errorMessage' in json.loads(result['body'])


def test_lambda_creates_customer_configuration(lambda_handler, event):
    """Test whether Lambda creates customer configuration successfuly."""
    # Arrange
    event['requestContext']['httpMethod'] = 'POST'

    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.OK
    assert result['body'] == '{"created": "configuration"}'


def test_lambda_returns_bad_request_for_post(lambda_handler, event):
    """Test whether Lambda responds with BAD REQUEST if request does not contain the line and kpi_model."""
    # Arrange
    event['requestContext']['httpMethod'] = 'POST'
    del event['body']

    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.BAD_REQUEST
    assert 'errorMessage' in json.loads(result['body'])


def test_lambda_returns_not_implemented(lambda_handler, event):
    """Test whether Lambda responds correctly to HTTP methods other than GET and POST."""
    # Arrange
    event['requestContext']['httpMethod'] = 'NOT SUPPORTED'

    # Act
    result = lambda_handler(event, 'not evaluated')

    # Assert
    assert result['statusCode'] == HTTPStatus.NOT_IMPLEMENTED
    assert 'message' in json.loads(result['body'])
