# Copyright (c) 2019, Syskron GmbH. All rights reserved.

# library import
import pytest
from mock import <PERSON><PERSON>ock, call, patch

BOTO3_MOCK = MagicMock()


def setup_module(module):
    module.patcher = patch.dict('sys.modules', {'boto3': BOTO3_MOCK})
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture
def event():
    return {'account': 'test-account', 'line': 'test-line'}


@pytest.fixture
def boto3():
    BOTO3_MOCK.reset_mock()
    BOTO3_MOCK.resource().Table().get_item.return_value = {
        'Item': {'account': 'test-account', 'line': 'test-line', 'kpi_model': 'test_model'}
    }
    return BOTO3_MOCK


@pytest.fixture
def get_configuration():
    from src.get import get_configuration

    return get_configuration


def test_get_configuration(get_configuration, event, boto3):
    # Act
    configuration = get_configuration(event['account'], event['line'])

    # Assert
    assert configuration == {'account': 'test-account', 'line': 'test-line', 'kpi_model': 'test_model'}

    assert boto3.resource().Table().get_item.call_count == 1
    assert boto3.resource().Table().get_item.call_args_list[0] == call(
        Key={'account': 'test-account', 'line_id': 'test-line'}
    )
