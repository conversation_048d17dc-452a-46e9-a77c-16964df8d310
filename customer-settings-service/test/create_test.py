# Copyright (c) 2019, Syskron GmbH. All rights reserved.

# library import
import pytest
from mock import MagicMock, call, patch

BOTO3_MOCK = MagicMock()
DATETIME_MOCK = MagicMock()


def setup_module(module):
    module.patcher = patch.dict('sys.modules', {'boto3': BOTO3_MOCK, 'datetime': DATETIME_MOCK})
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture
def event():
    return {'account': 'test-account', 'line': 'test-line', 'kpi_model': 'test-model'}


@pytest.fixture
def expected_result():
    return {
        'account': 'test-account',
        'line_id': 'test-line',
        'kpi_model': 'test-model',
        'settings': {},
        'created_at': 2,
        'updated_at': 2,
    }


@pytest.fixture
def boto3():
    BOTO3_MOCK.reset_mock()
    BOTO3_MOCK.resource().Table().put_item.return_value = {'Item': {'kpi_model': 'test_model'}}
    return BOTO3_MOCK


@pytest.fixture(autouse=True)
def datetime():
    DATETIME_MOCK.reset_mock()
    DATETIME_MOCK.datetime.utcnow().timestamp.return_value = 0.002
    return DATETIME_MOCK


@pytest.fixture
def create_configuration():
    from src.create import create_configuration

    return create_configuration


def test_create_customer_configuration(create_configuration, event, expected_result, boto3):
    # Act
    configuration = create_configuration(event['account'], event['line'], event['kpi_model'])

    # Assert
    assert configuration == expected_result

    assert boto3.resource().Table().put_item.call_count == 1
    assert boto3.resource().Table().put_item.call_args_list[0] == call(Item=expected_result)
