# customer-settings-service example

## Dynamo table
``` rk-performance-customer-settings ```

```
{
  "account": "readykit-replay",
  "created_at": *************,
  "kpi_model": "opi",
  "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
  "settings": {
      "minor_stop_config": 5
  },
  "updated_at": *************
}
```

# create
```

```
``` possible args
minor_stop_config == <time in minutes (int)>
```