# Copyright (c) 2020, Syskron GmbH. All rights reserved.

# System import
from http import HTTPStatus

# Library import
from aws_lambda_powertools import Logger
from aws_xray_sdk.core import patch_all
from lib_cloud_sdk.api_gateway.requests import contains, parse_event
from lib_cloud_sdk.api_gateway.responses import build_error, build_response
from lib_cloud_sdk.util.sentry.init_lambda import init_sentry
from sysxds_common.aws.rest.error import BadRequest, NotFound
from sysxds_common.aws.rest.error_handling_decorator import handle_errors

# Application import
from create import create_configuration
from get import get_configuration

init_sentry()

patch_all()

LOGGER = Logger()


@handle_errors()
def lambda_handler(event, context):  # pylint: disable=unused-argument
    LOGGER.info("Customer settings service received event: %s", event)

    args = parse_event(event)

    if args["http_method"] == "GET" and args["path_parts"][-1] == "customer-settings":
        required_keys = ["line"]
        if not contains(args["query_string_parameters"], required_keys):
            raise BadRequest(f'One of the keys ({", ".join(required_keys)}) is missing.')

        account = args["account"]
        line = args["query_string_parameters"]["line"]
        configuration = get_configuration(account, line)

        if not configuration:
            raise NotFound(f'No configuration found for account="{account}" and line="{line}".')

        return build_response(HTTPStatus.OK, configuration)

    if args["http_method"] == "POST" and args["path_parts"][-1] == "customer-settings":
        required_keys = ["line", "kpi_model"]
        if not contains(args["body"], required_keys):
            raise BadRequest(f'One of the keys ({", ".join(required_keys)}) is missing.')

        account = args["account"]
        line = args["body"]["line"]
        kpi_model = args["body"]["kpi_model"]
        settings = args["body"].get("settings")
        item = create_configuration(account, line, kpi_model, settings)

        return build_response(HTTPStatus.OK, item)

    LOGGER.error('This HTTP method is not implemented yet. Event: "%s"', event)
    return build_error(
        HTTPStatus.NOT_IMPLEMENTED,
        f'Method="{args["http_method"]}" for resource="{"/".join(args["path_parts"])}"'
        + " is not supported.",
    )
