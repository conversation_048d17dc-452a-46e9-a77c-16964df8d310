openapi: "3.0.1"
info:
  title: "rk-v1"
  version: "2022-07-06T09:05:32Z"
servers:
- url: "https://api.rk.share2act.io/{basePath}"
  variables:
    basePath:
      default: "/v1"
paths:
  /performance-analytics/customer-settings:
    get:
      description: Get configurations for a line.
      parameters:
        - $ref: '#/components/parameters/LineParameter'
      responses:
        '200':
          $ref: '#/components/responses/CustomerSettingsGetResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "404":
          $ref: '#/components/responses/NotFoundResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
      security:
      - S2aIamAuthorizer: []
    post:
      description: Store configurations for a line.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostBody'
      responses:
        '200':
          $ref: '#/components/responses/CustomerSettingsGetResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
      security:
      - S2aIamAuthorizer: []
components:
  responses:
    CustomerSettingsGetResponse:
      description: Successful operation
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CustomerSettingsGet"
    BadRequestResponse:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/BadRequest"
    UnauthorizedResponse:
      description: Unauthorized operation
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Unauthorized"
    NotFoundResponse:
      description: Not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/NotFound"
    InternalServerErrorResponse:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/InternalServerError"
  schemas:
    CustomerSettingsGet:
      type: object
      required:
        - account
        - line_id
        - kpi_model
        - created_at
        - updated_at
        - settings
      properties:
        account:
          type: string
          example: "readykit"
        line_id:
          type: string
          example: "178bb9e2-93c5-44f1-ae2d-154bb2a3bf91"
        kpi_model:
          type: string
          example: "din_8743"
        created_at:
          type: integer
          description: The creation time as an epoch timestamp.
          example: *************
        updated_at:
          type: integer
          description: The update time as an epoch timestamp.
          example: *************
        settings:
          description: Different settings stored for the customer (like minor_stop_config).
          type: object
          nullable: true
    PostBody:
      type: object
      required:
        - line_id
        - kpi_model
        - settings
      properties:
        line_id:
          type: string
          example: "178bb9e2-93c5-44f1-ae2d-154bb2a3bf91"
        kpi_model:
          type: string
          example: "din_8743"
        settings:
          description: Different settings stored for the customer (like minor_stop_config).
          type: object
          nullable: true
    BadRequest:
      type: object
      required:
        - errorMessage
        - errorType
      properties:
        errorMessage:
          type: string
          example: "One of the keys (line) is missing."
        errorType:
          type: string
          example: "BadRequest"
    Unauthorized:
      type: object
      required:
        - message
      properties:
        message:
          type: string
          example: "Unauthorized"
    NotFound:
      type: object
      required:
        - errorMessage
        - errorType
      properties:
        errorMessage:
          type: string
          example: "No configuration found for account=x and line=y."
        errorType:
          type: string
          example: "NotFound"
    InternalServerError:
      type: object
      required:
        - errorMessage
        - errorType
      properties:
        errorMessage:
          type: string
          example: "Encountered an unhandled error. Refer to error ERROR-x in any communication"
        errorType:
          type: string
          example: "InternalServerError"
  securitySchemes:
    S2aIamAuthorizer:
      type: "apiKey"
      name: "Authorization"
      in: "header"
      x-amazon-apigateway-authtype: "custom"
    DefaultAuthorizer:
      type: "apiKey"
      name: "Authorization"
      in: "header"
      x-amazon-apigateway-authtype: "cognito_user_pools"
  parameters:
    LineParameter:
      name: line
      in: query
      description: The id of the line.
      required: true
      schema:
        title: Line id
        type: string
        example: "178bb9e2-93c5-44f1-ae2d-154bb2a3bf91"
