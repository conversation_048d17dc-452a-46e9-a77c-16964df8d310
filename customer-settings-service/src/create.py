# Copyright (c) 2019, Syskron GmbH. All rights reserved.

from datetime import datetime

import boto3

DYNAMODB_TABLE = boto3.resource("dynamodb").Table("rk-performance-customer-settings")


def create_configuration(account, line, kpi_model, settings=None):
    timestamp = int(datetime.utcnow().timestamp() * 1000)

    if not settings:
        settings = {}

    item = {
        "line_id": line,
        "account": account,
        "kpi_model": kpi_model,
        "settings": settings,
        "created_at": timestamp,
        "updated_at": timestamp,
    }

    DYNAMODB_TABLE.put_item(Item=item)

    return item
