from aws_lambda_powertools import Logger
import boto3
from botocore.exceptions import ClientError

LOGGER = Logger()

DYNAMODB_TABLE = boto3.resource("dynamodb").Table("rk-performance-customer-settings")


def get_configuration(account, line):
    configuration = None
    try:
        response = DYNAMODB_TABLE.get_item(Key={"account": account, "line_id": line})
    except ClientError as error:
        LOGGER.warning(
            'GetItem failed for account="%s" and line_id="%s" with message="%s".',
            account,
            line,
            error.response["Error"]["Message"],
        )
    else:
        configuration = response.get("Item")

    return configuration
