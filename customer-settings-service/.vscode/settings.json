{
  "cSpell.words": [
    "boto",
    "dynamodb",
    "GREENGRASS",
    "Syskron",
    "greengrass",
    "greengrasssdk",
    "parametrize",
    "pylint",
    "pytest",
    "uncategorized",
    "zait"
  ],
  "files.associations": {
    ".cfnlintrc": "yaml"
  },
  "files.trimFinalNewlines": true,
  "python.testing.unittestEnabled": false,
  "python.testing.nosetestsEnabled": false,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["test"],
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.linting.pylintArgs": ["--rcfile=../.pylintrc"],
  "python.linting.banditEnabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.flake8Args": [
    "--max-line-length=120",
    "--ignore=E203, W503",
    "--verbose"
  ],
  "python.analysis.useImportHeuristic": true, // This is necessary to detect absolute imports
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": [
    "--skip-string-normalization",
    "--line-length",
    "120"
  ],
  "python.pythonPath": ".venv/bin/python"
}
