# Copyright (c) 2021, Syskron GmbH. All rights reserved.

# System import
from http import H<PERSON><PERSON>tatus
from json import JSONDecodeError

from aws_lambda_powertools import Logger
from aws_xray_sdk.core import patch_all

# library imports
from lib_cloud_sdk.api_gateway.requests import parse_event
from lib_cloud_sdk.api_gateway.responses import build_error
from lib_cloud_sdk.util.sentry.init_lambda import init_sentry
from lib_cloud_sdk.util.common import validate_timeranges

# application imports
from sysxds_common.aws.rest.error import BadRequest
from sysxds_common.aws.rest.error_handling_decorator import handle_errors

from units_report import get as units_reports_get

init_sentry()

patch_all()

LOGGER = Logger()


@handle_errors()
def lambda_handler(event, context):  # pylint: disable=unused-argument,too-many-return-statements
    LOGGER.info('Start lambda with event="%s".', event)

    # Parse the arguments
    try:
        args = parse_event(
            event,
            ["path_parts", "http_method", "feature_flags", "account", "caller", "user_groups"],
        )
        time_from = args.get("query_string_parameters", {}).get("time_from")
        time_to = args.get("query_string_parameters", {}).get("time_to")
        validate_timeranges(start=int(time_from), end=int(time_to))
    except TypeError as error:
        LOGGER.exception("Cannot parse event: %s", event, extra={"event": event})
        raise BadRequest("Time range needs to be specified in the request.") from error
    except JSONDecodeError as decode_error:
        LOGGER.error('Cannot parse event due to JSONDecodeError="%s".', str(decode_error))
        LOGGER.error("with event=%s", event)
        raise BadRequest("The request body must be in valid JSON format.") from decode_error
    except ValueError as value_error:
        LOGGER.error('Cannot parse event due to ValueError="%s".', str(value_error))
        LOGGER.error("with event=%s", event)
        raise BadRequest("Request property missing.") from value_error

    # Call correct handler
    if args["path_parts"][-3] == "units-report" and args["http_method"] == "GET":
        LOGGER.debug("Handle units-report GET request.")
        return units_reports_get(args)

    LOGGER.error('This HTTP method is not implemented yet. Event: "%s"', event)
    return build_error(
        HTTPStatus.NOT_IMPLEMENTED,
        f'Method="{args["http_method"]}" for resource="{args["resource_path"]}" is not supported.',
    )
