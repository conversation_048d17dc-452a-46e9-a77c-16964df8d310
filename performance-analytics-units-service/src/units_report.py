# Copyright (c) 2021, Syskron GmbH. All rights reserved.
import json
from http import H<PERSON><PERSON>tatus
from typing import Any

from aws_lambda_powertools import Logger
from fastapi import HTT<PERSON>Exception
from lib_cloud_sdk.api_gateway.responses import build_error, build_response
from performance_analytics.utility.block_counters import get_block_level_counters, query_counters
from performance_analytics.models.block_configuration import BlockCounterStatus

Arguments = dict[str, Any]
Response = dict[str, Any]

LOGGER = Logger()


def get(args: Arguments) -> Response:
    """
    Handle HTTP GET requests on the units-report resource.
    """
    # Validate timestamps
    time_from = validate_timestamp("time_from", args["query_string_parameters"].get("time_from"))
    time_to = validate_timestamp(
        "time_to", args["query_string_parameters"].get("time_to"), is_optional=True
    )
    if not isinstance(time_from, int):
        return time_from
    if time_to is not None and not isinstance(time_to, int):
        return time_to

    machine_id = args["path_parts"][-1]
    line_id = args["path_parts"][-2]
    account = args["account"]
    line_kpi = args["query_string_parameters"].get("line_kpi", 0)

    return get_units_report(
        time_from=time_from,
        time_to=time_to,
        machine_id=machine_id,
        line_id=line_id,
        account=account,
        line_kpi=line_kpi,
    )


# pylint: disable=too-many-positional-arguments
def get_units_report(
    time_from: int, time_to: int, machine_id: str, line_id: str, account: str, line_kpi: int
) -> Response:
    """Get units report based on line_kpi parameter."""
    base_response = {
        "customer": account,
        "eq_id": machine_id,
        "time_from": time_from,
        "time_to": time_to,
    }

    if int(line_kpi) == 1:
        return _get_block_counter_report(
            base_response, line_id, machine_id, account, time_from, time_to
        )
    return _get_machine_counter_report(base_response, machine_id, account, time_from, time_to)


def _get_block_counter_report(
    base_response: dict, line_id: str, machine_id: str, account: str, time_from: int, time_to: int
) -> Response:
    """Get units report using block counters."""
    block_counter_config = get_block_level_counters(
        account=account, line_id=line_id, time_from=time_from, time_to=time_to
    )

    response = base_response.copy()

    if block_counter_config.status in (
        BlockCounterStatus.SUCCESS,
        BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER,
    ):
        start_time = time_from
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            start_time = block_counter_config.block_valid_from
        units_map = query_counters(
            account=account,
            machine_id=machine_id,
            time_from=start_time,
            time_to=time_to,
            calculate_block_counters=True,
        )
        for unit_type in ["units_produced", "units_defect", "units_total"]:
            response[unit_type] = units_map.get(unit_type)
        if (
            block_counter_config.status
            == BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER
        ):
            response["time_from"] = start_time
            response["eq_id"] = line_id
    else:
        # For all other status flags, use machine counters as fallback
        machine_response = _get_machine_counter_report(
            base_response, response["eq_id"], account, time_from, time_to
        )
        machine_body = json.loads(machine_response["body"])
        response.update(machine_body)

    return build_response(HTTPStatus.OK, response)


def _get_machine_counter_report(
    base_response: dict, machine_id: str, account: str, time_from: int, time_to: int
) -> Response:
    """Get units report using machine counters."""
    response = base_response.copy()

    try:
        # Call the query_counters function which returns a dictionary
        units_map = query_counters(account, machine_id, time_from, time_to)

        # Update the response with the units data
        response.update(
            {
                "units_produced": round(units_map["units_produced"]),
                "units_defect": round(units_map["units_defect"]),
                "units_total": round(units_map["units_total"]),
            }
        )

        # Add station_counters if they exist
        if "station_counters" in units_map:
            response["station_counters"] = units_map["station_counters"]

        return build_response(HTTPStatus.OK, response)

    except HTTPException as e:

        LOGGER.debug("HTTP error getting counter data for machine_id=%s: %s", machine_id, str(e))

        # Set default values for units in case of error

        response.update({"units_produced": 0, "units_defect": 0, "units_total": 0})
        return build_response(HTTPStatus.OK, response)


def validate_timestamp(
    param_name: str, value: str | None, is_optional: bool = False
) -> int | None | Response:
    """Validate timestamp parameter.

    Args:
        param_name: Name of the parameter being validated
        value: The timestamp value to validate
        is_optional: Whether the parameter is optional

    Returns:
        int: Validated timestamp as integer
        None: If parameter is optional and not provided
        Response: Error response if validation fails
    """
    if not value:
        if is_optional:
            return None
        LOGGER.error('Abort get of units-report! Missing query_parameter "%s"', param_name)
        return build_error(HTTPStatus.BAD_REQUEST, f'Missing query_parameter "{param_name}"')

    if not isvalid_timestamp(value):
        LOGGER.error('invalid timestamp for %s="%s"', param_name, value)
        return build_error(HTTPStatus.BAD_REQUEST, f'Invalid timestamp for "{param_name}"')

    return int(value)


def isvalid_timestamp(timestamp: str) -> bool:
    try:
        int(timestamp)
        return True
    except ValueError:
        return False
