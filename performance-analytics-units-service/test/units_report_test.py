# Copyright (c) 2021, Syskron GmbH. All rights reserved.

# system import
import json
from http import HTTPStatus
from typing import Any
from unittest.mock import Mock, patch

# library import
import pytest
from machine_data_query.models.speeds import (
    DESIGN_SPEEDS,
    SET_SPEEDS,
    SpeedCounterItem,
    SpeedItem,
    Speeds,
)

SPEEDS_MOCK = Mock()
VERSION_MOCK = Mock()


def _read_json_file(file_name: str) -> dict:
    with open(file_name) as file_obj:
        file_data = file_obj.read()
    return json.loads(file_data)


def _create_speeds_from_json_file(file_name: str) -> tuple[Speeds, Speeds, Speeds]:
    data = _read_json_file(file_name)
    machine = data["test-machine-id"]
    time_from = machine["time_from"]
    time_to = machine["time_to"]
    speeds_items = [
        SpeedCounterItem(
            start=item["start"],
            end=item["end"],
            current_speed=item["current_speed"],
            units_produced=item["units_produced"],
            units_defect=item["units_defect"],
            units_total=item.get("units_total"),
            station_counters=item.get("station_counters"),
        )
        for item in machine["speeds"]
    ]
    design_speeds_items = [
        SpeedItem(start=item["start"], end=item["end"], type=DESIGN_SPEEDS, speed=item["speed"])
        for item in machine["design_speeds"]
    ]
    set_speeds_items = [
        SpeedItem(start=item["start"], end=item["end"], type=SET_SPEEDS, speed=item["speed"])
        for item in machine["set_speeds"]
    ]
    speeds = Speeds(time_from=time_from, time_to=time_to, items=speeds_items)
    design_speeds = Speeds(time_from=time_from, time_to=time_to, items=design_speeds_items)
    set_speeds = Speeds(time_from=time_from, time_to=time_to, items=set_speeds_items)
    return (speeds, design_speeds, set_speeds)


def setup_module(module):
    module.patcher = patch.dict(
        "sys.modules",
        {
            "machine_data_query.query_speeds": SPEEDS_MOCK,
            "lib_cloud_sdk.version": VERSION_MOCK,
        },
    )
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture
def args() -> Any:
    return {
        "account": "test-account-id",
        "path_parts": ["performance-analytics", "units-report", "test-line-id", "test-machine-id"],
        "query_string_parameters": {
            "time_from": "*************",
            "time_to": "*************",
            "line_kpi": 0,
        },
    }


@pytest.fixture
def args_with_line_kpi() -> Any:
    return {
        "account": "test-account-id",
        "path_parts": ["performance-analytics", "units-report", "test-line-id", "test-machine-id"],
        "query_string_parameters": {
            "time_from": "*************",
            "time_to": "*************",
            "line_kpi": 1,
        },
    }


@pytest.fixture
def args_missing_time_from() -> Any:
    return {
        "account": "test-account-id",
        "path_parts": ["performance-analytics", "units-report", "test-line-id", "test-machine-id"],
        "query_string_parameters": {"time_to": "*************", "line_kpi": 0},
    }


@pytest.fixture
def args_invalid_time_from() -> Any:
    return {
        "account": "test-account-id",
        "path_parts": ["performance-analytics", "units-report", "test-line-id", "test-machine-id"],
        "query_string_parameters": {
            "time_from": "invalid",
            "time_to": "*************",
            "line_kpi": 0,
        },
    }


@pytest.fixture(autouse=True)
def version():
    VERSION_MOCK.reset_mock()
    VERSION_MOCK.get_version.return_value = "test-version"
    return VERSION_MOCK


@pytest.fixture
def query_speeds_with_station_counters():
    SPEEDS_MOCK.reset_mock()
    SPEEDS_MOCK.query_speeds.return_value = _create_speeds_from_json_file(
        "test/res/dynamo_db_machine_data_with_station_counters.json"
    )
    return SPEEDS_MOCK


@pytest.fixture
def query_speeds_with_station_counters_2():
    # units not matching fully
    SPEEDS_MOCK.reset_mock()
    SPEEDS_MOCK.query_speeds.return_value = _create_speeds_from_json_file(
        "test/res/dynamo_db_machine_data_with_station_counters_2.json"
    )
    return SPEEDS_MOCK


@pytest.fixture
def query_speeds():
    SPEEDS_MOCK.reset_mock()
    SPEEDS_MOCK.query_speeds.return_value = _create_speeds_from_json_file(
        "test/res/dynamo_db_machine_data.json"
    )
    return SPEEDS_MOCK


@pytest.fixture
def query_speeds_wo_totals():
    SPEEDS_MOCK.query_speeds.return_value = _create_speeds_from_json_file(
        "test/res/dynamo_db_machine_data_wo_totals.json"
    )
    SPEEDS_MOCK.reset_mock()
    return SPEEDS_MOCK


def test_get_units_report(args, query_speeds_with_station_counters):
    """Test getting units report with station counters."""
    from src.units_report import get

    actual_result = get(args)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert body == {
        "customer": "test-account-id",
        "eq_id": "test-machine-id",
        "time_from": *************,
        "time_to": *************,
        "units_produced": 33,
        "units_defect": 0,
        "units_total": 33,
        "station_counters": {
            "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
            "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
            "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
        },
    }


def test_get_units_report_missing_time_from(args_missing_time_from):
    """Test error handling when time_from parameter is missing."""
    from src.units_report import get

    actual_result = get(args_missing_time_from)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.BAD_REQUEST
    assert body["message"] == 'Missing query_parameter "time_from"'


def test_get_units_report_invalid_time_from(args_invalid_time_from):
    """Test error handling when time_from parameter is invalid."""
    from src.units_report import get

    actual_result = get(args_invalid_time_from)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.BAD_REQUEST
    assert body["message"] == 'Invalid timestamp for "time_from"'


def test_get_units_report_no_speeds_data(args):
    """Test handling when no speeds data is found."""
    from src.units_report import get

    SPEEDS_MOCK.reset_mock()
    SPEEDS_MOCK.query_speeds.return_value = (None, None, None)

    actual_result = get(args)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert body == {
        "customer": "test-account-id",
        "eq_id": "test-machine-id",
        "time_from": *************,
        "time_to": *************,
        "units_produced": 0,
        "units_defect": 0,
        "units_total": 0,
    }


def test_isvalid_timestamp():
    """Test timestamp validation function."""
    from src.units_report import isvalid_timestamp

    assert isvalid_timestamp("*************") is True
    assert isvalid_timestamp("invalid") is False
    assert isvalid_timestamp("123abc") is False
    assert isvalid_timestamp("") is False
    assert isvalid_timestamp("0") is True
    assert isvalid_timestamp("-*************") is True


def test_get_units_report_not_matching_units(args, query_speeds_with_station_counters_2):
    """Test getting units report with non-matching units."""
    from src.units_report import get

    actual_result = get(args)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert body == {
        "customer": "test-account-id",
        "eq_id": "test-machine-id",
        "time_from": *************,
        "time_to": *************,
        "units_produced": 33,
        "units_defect": 5,
        "units_total": 35,
        "station_counters": {
            "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
            "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
            "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
        },
    }


def test_get_units_report_missing_time_to(args):
    """Test error handling when time_to parameter is missing."""
    from src.units_report import get

    args["query_string_parameters"].pop("time_to")
    actual_result = get(args)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert "time_from" in body
    assert "time_to" in body
    assert body["time_from"] == *************


def test_get_units_report_overlapping_timerange(args_with_line_kpi, mocker):
    """Test getting units report with overlapping timerange status."""
    from src.units_report import get

    from performance_analytics.models.block_configuration import BlockCounterStatus

    mock_block_config = mocker.patch("src.units_report.get_block_level_counters")
    mock_block_config.return_value = mocker.Mock(
        status=BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER,
        units_map={"units_produced": 100, "units_defect": 5, "units_total": 105},
        block_valid_from=*************,
    )

    actual_result = get(args_with_line_kpi)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert body["time_from"] == *************


def test_get_units_report_failed_status(args_with_line_kpi, mocker):
    """Test getting units report with failed status."""
    from src.units_report import get

    from performance_analytics.models.block_configuration import BlockCounterStatus

    mock_block_config = mocker.patch("src.units_report.get_block_level_counters")
    mock_block_config.return_value = mocker.Mock(
        status=BlockCounterStatus.INVALID_BLOCK_CONFIG, units_map={}
    )

    actual_result = get(args_with_line_kpi)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert "units_produced" in body
    assert "units_defect" in body
    assert "units_total" in body


def test_get_units_report_with_station_counters_none(args):
    """Test getting units report when station counters are None."""
    from src.units_report import get

    SPEEDS_MOCK.reset_mock()
    speeds_data = _create_speeds_from_json_file("test/res/dynamo_db_machine_data.json")
    # Modify the speeds data to include None station counters
    speeds_data[0].items[0].station_counters = None
    SPEEDS_MOCK.query_speeds.return_value = speeds_data

    actual_result = get(args)
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert "station_counters" not in body
