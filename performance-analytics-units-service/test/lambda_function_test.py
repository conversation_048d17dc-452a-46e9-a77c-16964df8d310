import pytest
from unittest.mock import Mock, patch

UNITS_REPORT = Mock()


def setup_module(module):
    module.patcher = patch.dict(
        "sys.modules",
        {
            "units_report": UNITS_REPORT,
        },
    )
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture
def units_report_performance():
    UNITS_REPORT.get.return_value = {"units_report": "units-report-response"}
    UNITS_REPORT.reset_mock()
    return UNITS_REPORT


@pytest.fixture
def units_report_event():
    def _units_report_event(request):
        return {
            "path": "/performance-analytics/units-report/test-line-id/test-machine-id",
            "headers": {},
            "queryStringParameters": {"time_from": request[0], "time_to": request[1]},
            "requestContext": {
                "httpMethod": "GET",
                "resourcePath": "/performance-analytics/units-report/{proxy+}",
                "authorizer": {
                    "claims": '{ "scopes": ["performance-analytics"]}',
                    "user": '{"userId": "test-user-id", "groups": []}',
                    "account": '{"accountId": "test-account-id"}',
                },
            },
        }

    return _units_report_event


@pytest.fixture
def lambda_handler():
    """
    Unit under test
    """
    from src.lambda_function import lambda_handler

    return lambda_handler


def test_lambda_handler_call_units_report(
    lambda_handler, units_report_event, units_report_performance
):
    """Test whether units-report is called as well as its result is returned."""
    report_event = units_report_event(["*************", "*************"])
    actual_result = lambda_handler(report_event, "context")

    assert actual_result == {"units_report": "units-report-response"}
    units_report_performance.get.assert_called_once_with(
        {
            "http_method": "GET",
            "feature_flags": ["performance-analytics"],
            "account": "test-account-id",
            "caller": "test-user-id",
            "user_groups": [],
            "resource_path": "/performance-analytics/units-report/{proxy+}",
            "path_parts": [
                "",
                "performance-analytics",
                "units-report",
                "test-line-id",
                "test-machine-id",
            ],
            "query_string_parameters": {"time_from": "*************", "time_to": "*************"},
            "multi_value_query_string_parameters": {},
            "headers": {},
            "body": {},
        }
    )


def test_lambda_handler_call_wrong_query_param(
    lambda_handler, units_report_event, units_report_performance
):
    report_event = units_report_event([None, None])
    lambda_handler(report_event, "context")
    actual_result = lambda_handler(report_event, "context")

    assert (
        actual_result["body"]
        == '{"errorMessage": "Time range needs to be specified in the request.", "errorType": "BadRequest"}'
    )


@pytest.fixture
def wrong_args():
    return {
        "path": "/performance-analytics/units-report/test-line-id/test-machine-id",
        "headers": {},
        "queryStringParameters": {"time_from": "123123abc"},
        "requestContext": {
            "httpMethod": "GET",
            "resourcePath": "/performance-analytics/units-report/{proxy+}",
            "authorizer": {
                "claims": '{ "scopes": ["performance-analytics"]}',
                "user": '{"userId": "test-user-id", "groups": []}',
                "account": '{"accountId": "test-account-id"}',
            },
        },
    }


def test_catch_invalid_query_parameter(lambda_handler, wrong_args):
    from http import HTTPStatus

    actual_result = lambda_handler(wrong_args, "context")

    assert (
        actual_result["statusCode"] == HTTPStatus.BAD_REQUEST
    ), 'Invalid timestamp for "time_from"'
