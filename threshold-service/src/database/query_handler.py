# System import
import logging
from datetime import datetime

# Library import
from typing import Any

import boto3
from botocore.exceptions import ClientError

# Application import
from models.threshold_model import ThresholdConfig

LOGGER = logging.getLogger(__name__)


class QueryHandler:
    duration_default = {
        "failure_modes": {
            "default": {
                "error_level": 1800000,
                "warning_level": 900000,
            }  # value in ms = 30 min  # value in ms = 15 min
        }
    }
    quantity_default = {"failure_modes": {"default": {"error_level": 10, "warning_level": 3}}}

    def __init__(self) -> None:
        self.dynamo_client = boto3.resource("dynamodb").Table("rk-threshold-config")

    def fetch_item(self, line_id: str, machines: [str]) -> ThresholdConfig:
        """
        Query DynamoDB for the threshold config.
        """
        response = None
        try:
            response = self.dynamo_client.get_item(Key={"line_id": line_id})
        except ClientError as error:
            LOGGER.warning(
                'GetI<PERSON> failed for line="%s" with message="%s".',
                line_id,
                error.response["Error"]["Message"],
            )
        finally:
            if not response or "Item" not in response:
                config = self.add_initial_configuration(line_id, machines)

            else:
                # if config is available in db
                config = response["Item"]["config"]
                current_machines = config["duration"]["machines"].keys()
                if len(current_machines) != len(machines):
                    default_config = self.build_default_config(machines, config)
                    updated_response = self.update_config(line_id, default_config)
                    config = updated_response["Attributes"]["config"]
        return ThresholdConfig(**config)

    def update_config(self, line_id: str, config: ThresholdConfig) -> dict[str, Any]:
        """
        Updates the threshold config into dynamoDb.
        """
        timestamp = int(datetime.utcnow().timestamp() * 1000)
        response = self.dynamo_client.update_item(
            Key={"line_id": line_id},
            UpdateExpression="set config=:config, updated_at=:updated_at",
            ExpressionAttributeValues={":config": config, ":updated_at": timestamp},
            ReturnValues="ALL_NEW",
        )
        LOGGER.debug(f"UpdateItem succeeded for line={line_id}.")
        return response

    def build_default_config(
        self, machines: [str], default_config: ThresholdConfig = None
    ) -> ThresholdConfig:
        """
        Creates config for the missing machine in the input, builds default config
        """
        if default_config is None:
            default_config = {"duration": {"machines": {}}, "quantity": {"machines": {}}}

        for machine in machines:
            if machine not in default_config["duration"]["machines"]:
                default_config["duration"]["machines"][machine] = self.duration_default
            if machine not in default_config["quantity"]["machines"]:
                default_config["quantity"]["machines"][machine] = self.quantity_default
        return default_config

    def add_initial_configuration(self, line_id: str, machines: [str]) -> ThresholdConfig:
        """
        Returns the config for fresh line id, if config is missing in db.
        """
        config = self.build_default_config(machines)
        timestamp = int(datetime.utcnow().timestamp() * 1000)
        item = {
            "line_id": line_id,
            "config": config,
            "created_at": timestamp,
            "updated_at": timestamp,
        }
        self.dynamo_client.put_item(Item=item)
        return config
