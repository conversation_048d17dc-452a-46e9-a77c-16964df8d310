# pylint: disable=line-too-long, too-many-arguments, too-many-locals, broad-except

# System import
from typing import Any

# Library import
from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, Request

from database.query_handler import QueryHandler

# Application import
from models.threshold_model import BodyGet, BodyPut, ThresholdConfig

LOGGER = Logger()
router = APIRouter()

TRACER = Tracer()


@router.post(
    "/stoppage-analysis/threshold", response_model=ThresholdConfig, response_model_exclude_none=True
)
@TRACER.capture_method(capture_response=False)
def get_threshold_config(request: Request, params: BodyGet):
    """
    Get the threshold config for all the machines.
    """
    LOGGER.info('Calling threshold endpoint with event: "%s".', request.scope.get("aws.event"))

    q_handler = QueryHandler()
    return q_handler.fetch_item(params.line, params.machines)


@router.put(
    "/stoppage-analysis/threshold", response_model=dict[str, Any], response_model_exclude_none=True
)
@TRACER.capture_method(capture_response=False)
def put_threshold_config(request: Request, params: BodyPut) -> dict[str, Any]:
    """
    Update the threshold config for the given line.
    """
    LOGGER.info('Calling threshold endpoint with event: "%s".', request.scope.get("aws.event"))

    q_handler = QueryHandler()
    dynamo_res = q_handler.update_config(params.line, params.config)
    dynamo_res["Attributes"]["config"] = ThresholdConfig(**dynamo_res["Attributes"]["config"])
    return dynamo_res
