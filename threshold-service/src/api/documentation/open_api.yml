openapi: 3.0.2
info:
  title: FastAPI
  version: 0.1.0
paths:
  /v1/performance-analytics/stoppage-analysis/threshold:
    put:
      summary: Put Threshold Config
      description: Update the threshold config for the given line.
      operationId: >-
        put_threshold_config_v1_performance_analytics_stoppage_analysis_threshold_put
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BodyPut'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                title: >-
                  Response Put Threshold Config V1 Performance Analytics
                  Stoppage Analysis Threshold Put
                type: object
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      summary: Get Threshold Config
      description: Get the threshold config for all the machines.
      operationId: >-
        get_threshold_config_v1_performance_analytics_stoppage_analysis_threshold_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BodyGet'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThresholdConfig'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    BodyGet:
      title: BodyGet
      required:
        - line
        - machines
      type: object
      properties:
        line:
          title: Line
          type: string
        machines:
          title: Machines
          type: array
          items:
            type: string
    BodyPut:
      title: BodyPut
      required:
        - line
        - config
      type: object
      properties:
        line:
          title: Line
          type: string
        config:
          title: Config
          type: object
    ConfigGroupedByMachines:
      title: ConfigGroupedByMachines
      required:
        - machines
      type: object
      properties:
        machines:
          title: Machines
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Machines'
    FailureMode:
      title: FailureMode
      required:
        - default
      type: object
      properties:
        default:
          $ref: '#/components/schemas/ThresholdLevel'
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    Machines:
      title: Machines
      required:
        - failure_modes
      type: object
      properties:
        failure_modes:
          $ref: '#/components/schemas/FailureMode'
    ThresholdConfig:
      title: ThresholdConfig
      required:
        - duration
        - quantity
      type: object
      properties:
        duration:
          $ref: '#/components/schemas/ConfigGroupedByMachines'
        quantity:
          $ref: '#/components/schemas/ConfigGroupedByMachines'
    ThresholdLevel:
      title: ThresholdLevel
      required:
        - warning_level
        - error_level
      type: object
      properties:
        warning_level:
          title: Warning Level
          type: integer
        error_level:
          title: Error Level
          type: integer
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            type: string
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
