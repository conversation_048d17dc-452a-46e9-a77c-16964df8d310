from typing import Any
from uuid import UUID

from pydantic import BaseModel


class BodyGet(BaseModel):
    line: str
    machines: list[str]


class ThresholdLevel(BaseModel):
    warning_level: int
    error_level: int


class FailureMode(BaseModel):
    default: ThresholdLevel


class Machines(BaseModel):
    failure_modes: FailureMode


class ConfigGroupedByMachines(BaseModel):
    machines: dict[str, Machines]


class ThresholdConfig(BaseModel):
    duration: ConfigGroupedByMachines
    quantity: ConfigGroupedByMachines


class ThresholdConfigDocument(BaseModel):
    line_id: UUID
    updated_at: int
    created_at: int
    config: ThresholdConfig


class BodyPut(BaseModel):
    line: str
    config: dict[str, Any]


if __name__ == "__main__":
    data = {
        "line_id": "1d4db519-e9c3-4e83-b64d-dc52148382ed",
        "updated_at": 1631806294583,
        "created_at": 1593436216260,
        "config": {
            "duration": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {
                            "default": {"warning_level": 900000, "error_level": 1800000}
                        }
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {
                            "default": {"warning_level": 900000, "error_level": 1800000}
                        }
                    },
                }
            },
            "quantity": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {"default": {"warning_level": 3, "error_level": 10}}
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {"default": {"warning_level": 3, "error_level": 10}}
                    },
                }
            },
        },
    }

    x = ThresholdConfigDocument(**data)
