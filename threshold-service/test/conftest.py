# System import
import os
import site

import boto3

# Library import
import pytest
from moto import mock_aws

# we set our site dir to src to have proper package names
MODULE_DIR_PATH = os.path.dirname(os.path.realpath(__file__))
source_dir = os.path.join(MODULE_DIR_PATH, "..", "src")
site.addsitedir(source_dir)


@pytest.fixture
def threshold_config_dynamodb_client():
    with mock_aws():
        os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
        db_client = boto3.resource("dynamodb", region_name="eu-central-1")
        threshold_config_client = db_client.create_table(
            TableName="rk-threshold-config",
            KeySchema=[{"AttributeName": "line_id", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "line_id", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        # tb = db_client.Table('rk-threshold-config')
        yield threshold_config_client


@pytest.fixture
def input_data_threshold():
    return {
        "line_id": "1d4db519-e9c3-4e83-b64d-dc52148382ee",
        "updated_at": 1631806294583,
        "created_at": 1593436216260,
        "config": {
            "duration": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {
                            "default": {"warning_level": 900000, "error_level": 1800000}
                        }
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {
                            "default": {"warning_level": 900000, "error_level": 1800000}
                        }
                    },
                }
            },
            "quantity": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {"default": {"warning_level": 3, "error_level": 10}}
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {"default": {"warning_level": 3, "error_level": 10}}
                    },
                }
            },
        },
    }


@pytest.fixture
def input_updated_data_threshold():
    return {
        "line_id": "1d4db519-e9c3-4e83-b64d-dc52148382ee",
        "updated_at": 1631806294583,
        "created_at": 1593436216260,
        "config": {
            "duration": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {
                            "default": {"warning_level": 900030, "error_level": 1800030}
                        }
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {
                            "default": {"warning_level": 900060, "error_level": 1800060}
                        }
                    },
                }
            },
            "quantity": {
                "machines": {
                    "6d230509-acb6-4728-8acd-3fcc60c278a3": {
                        "failure_modes": {"default": {"warning_level": 4, "error_level": 11}}
                    },
                    "3581f625-40ee-44da-8902-a8eee452a874": {
                        "failure_modes": {"default": {"warning_level": 4, "error_level": 11}}
                    },
                }
            },
        },
    }


@pytest.fixture
def url():
    return "v1/performance-analytics/stoppage-analysis/threshold"
