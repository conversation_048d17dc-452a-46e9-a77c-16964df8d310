import json
import logging

import pydantic
from fastapi.testclient import TestClient

from src.database.query_handler import QueryHandler

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(logging.DEBUG)


def test_post(threshold_config_dynamodb_client, input_data_threshold, url):
    """
    verify Get config for given line id and machines in body params.
    """
    # Arrange

    from src.lambda_function import app

    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ee"
    body = {
        "line": line_id,
        "machines": [
            "6d230509-acb6-4728-8acd-3fcc60c278a3",
            "3581f625-40ee-44da-8902-a8eee452a874",
        ],
    }

    # Read input data
    expected_result = input_data_threshold["config"]
    # "Store" data into respective DynamoDB tables
    threshold_config_dynamodb_client.put_item(
        TableName="rk-threshold-config", Item=input_data_threshold
    )

    # Act
    client = TestClient(app)
    response = client.post(url, json=body)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == expected_result


def test_post_mismatch_machines(threshold_config_dynamodb_client, input_data_threshold, url):
    """
    verify Get config for given line id and machines in body params.
    """
    # Arrange

    from src.lambda_function import app

    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ee"
    body = {
        "line": line_id,
        "machines": [
            "6d230509-acb6-4728-8acd-3fcc60c278a3",
            "3581f625-40ee-44da-8902-a8eee452a874",
            "7u630509-44da-4728-40ee-3fcc60c298y6",
        ],
    }

    # Read input data
    expected_result = input_data_threshold["config"]
    # "Store" data into respective DynamoDB tables
    threshold_config_dynamodb_client.put_item(
        TableName="rk-threshold-config", Item=input_data_threshold
    )

    expected_result["duration"]["machines"][
        "7u630509-44da-4728-40ee-3fcc60c298y6"
    ] = QueryHandler.duration_default
    expected_result["quantity"]["machines"][
        "7u630509-44da-4728-40ee-3fcc60c298y6"
    ] = QueryHandler.quantity_default

    # Act
    client = TestClient(app)
    response = client.post(url, json=body)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == expected_result


def test_get_default_values(threshold_config_dynamodb_client, input_data_threshold, url):
    """
    verify if the line id is not present and insert is happening for sure
    """
    # Arrange

    from src.lambda_function import app

    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382eh"
    body = {
        "line": line_id,
        "machines": [
            "6d230509-acb6-4728-8acd-3fcc60c278a3",
            "3581f625-40ee-44da-8902-a8eee452a874",
        ],
    }

    # Read input data
    expected_result = input_data_threshold["config"]

    # Act
    client = TestClient(app)
    response = client.post(url, json=body)
    actual_result = json.loads(response.text)

    # recheck if item has been inserted
    item = threshold_config_dynamodb_client.get_item(Key={"line_id": line_id})

    # Assert
    assert actual_result == expected_result
    assert item["Item"]["config"] == expected_result
    assert item["Item"]["line_id"] == line_id


def test_incomplete_post(url):
    """
    verify error response if a body param is missing.
    """
    # Arrange

    from src.lambda_function import app

    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ee"
    body = {
        "line": line_id,
        # missing list of machines.
    }

    # Read input data
    expected_result = {
        "detail": [
            {
                "input": {"line": "1d4db519-e9c3-4e83-b64d-dc52148382ee"},
                "loc": ["body", "machines"],
                "msg": "Field required",
                "type": "missing",
            }
        ]
    }

    # Act
    client = TestClient(app)
    response = client.post(url, json=body)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == expected_result


def test_incomplete_put(url):
    """
    verify any body params is missing from put command
    """
    # Arrange

    from src.lambda_function import app

    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ee"
    body = {
        "line": line_id,
        # missing config of machines.
    }

    # Read input data
    expected_result = {
        "detail": [
            {
                "input": {"line": "1d4db519-e9c3-4e83-b64d-dc52148382ee"},
                "loc": ["body", "machines"],
                "msg": "Field required",
                "type": "missing",
            }
        ]
    }
    # "Store" data into respective DynamoDB tables
    # threshold_config_dynamodb_client.put_item(TableName='rk-threshold-config', Item=input_data_threshold)

    # Act
    client = TestClient(app)
    response = client.post(url, json=body)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == expected_result


def test_put(
    threshold_config_dynamodb_client, input_data_threshold, input_updated_data_threshold, url
):
    """
    verify the put functionality of the API.
    """
    # Arrange

    from src.lambda_function import app

    # Prepare URL
    config = input_updated_data_threshold["config"]
    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ee"
    body = {"line": line_id, "config": config}

    # Read input data
    # "Store" data into respective DynamoDB tables
    threshold_config_dynamodb_client.put_item(
        TableName="rk-threshold-config", Item=input_data_threshold
    )
    expected_result = config

    # Act
    client = TestClient(app)
    response = client.put(url, json=body)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result["Attributes"]["line_id"] == line_id
    assert actual_result["Attributes"]["config"] == expected_result


def test_get(url):
    """
    negative test case to verify not implemented http method.
    """
    from src.lambda_function import app

    client = TestClient(app)
    response = client.get(url)

    assert response.status_code == 405
