[tool.poetry]
name = "performance-analytics-service"
version = "0.1.0"
description = "dummy project for pre-commit hooks"
authors = ["Syskron - Performance Team"]
package-mode = false

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"

[tool.poetry.group.dev.dependencies]
pre-commit = "^3"
toml = "^0"
ruff = "^0"
mypy = "^1"
boto3 = "^1.36.4"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

[tool.black]
target-version = ['py313']
line-length = 100
skip-string-normalization = true
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.pytest_cache
  | \.__pycache__
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
pythonpath = ["./performance-analytics-service/src", "./failure-mode-manager-service/src","./performance-analytics-kpi-service/src","./products-speeds-service/src","./performance-analytics-service-config-generator/src","./custom-database-user-creator/src","./comments-service/src","./performance-analytics-units-service/src"]
