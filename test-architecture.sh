#!/usr/bin/env bash

expected_architecture="aarch64" # for native dependencies if there are some

if [ $OSTYPE = "linux-gnu" ]; then
    # gnu find
    readarray array < <(find . -regextype posix-extended -regex '.*\.so(\..*)?' -not -path './.venv/*' -not -path './.pyenv/*')
else
    # bsd find 
    readarray array < <(find -E . -regex '.*\.so(\..*)?' -not -path './.venv/*' -not -path './.pyenv/*')
fi

if [ -z ${array+x} ]; then
    echo No native libraries found
    exit 0
fi

for i in "${array[@]}"
do
    my_dump="$(objdump -f $i | grep $expected_architecture)"
    if (( ${#my_dump} == 0 )); then
        echo "Native dependency does not have expected_architecture = $expected_architecture: $i"
        exit 1
    fi
done
exit 0