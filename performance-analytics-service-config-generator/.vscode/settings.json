{"files.trimFinalNewlines": true, "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "mypy-type-checker.ignorePatterns": ["**/test/*.py", "**/test/**/*.py"], "mypy-type-checker.args": ["--config-file=pyproject.toml"], "[python]": {"editor.rulers": [119], "editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "always"}}}