import os
from unittest.mock import patch

from lib_equipment_cache_common.models.equipment_models import EquipmentFromCache
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
import pytest

from config_generator import ConfigGenerator


@pytest.fixture
def expected_get_shift_payload():
    return {
        "authorizerContext": {
            "user": {
                "userId": "",
            },
            "account": {"accountId": "customer"},
        },
        "payload": {},
        "lineId": "uuid",
    }


@pytest.fixture
def expected_timezone_payload():
    return {
        "authorizerContext": {
            "user": {
                "userId": "",
            },
            "account": {"accountId": "customer"},
        },
        "payload": {"descendantId": "uuid"},
    }


@pytest.fixture
def expected_create_shift_payload():
    return {
        "authorizerContext": {
            "user": {
                "userId": "",
            },
            "account": {"accountId": "customer"},
        },
        "payload": {
            "localDailySchedule": {
                "duration": 8,
                "shifts": [
                    {"name": "Shift-1", "time": "00:00"},
                    {"name": "Shift-2", "time": "08:00"},
                    {"name": "Shift-3", "time": "16:00"},
                ],
            },
            "timezone": "America/New_York",
        },
        "lineId": "uuid",
    }


@pytest.fixture
def mock_kpiconfig_assignment_broadcast_event(mocker):
    """Fixture to mock the `KpiconfigAssignmentBroadcastEvent` class from lib."""
    mock_event_class = mocker.patch(
        "lib_kpi_config_client.helpers.s2a_event_helper.KpiconfigAssignmentBroadcastEvent", autospec=True
    )
    mock_instance = mock_event_class.return_value
    mock_instance.set_source = mocker.MagicMock()
    mock_instance.set_message = mocker.MagicMock()
    mock_instance.get_message = mocker.MagicMock()
    mock_instance.publish = mocker.MagicMock()

    return mock_instance


def get_oc_shifts_result():
    return {
        "localDailySchedule": {
            "duration": 8,
            "shifts": [
                {"name": "Shift-1", "time": "00:00"},
                {"name": "Shift-2", "time": "08:00"},
                {"name": "Shift-3", "time": "16:00"},
            ],
        },
        "timezone": "America/New_York",
    }


def get_readykit_line_ids():
    return ["uuid"]


def test_format_shifts_get_payload():
    generator = ConfigGenerator("customer", "uuid", {}, "module_name")

    response = generator._format_shifts_get_payload("uuid")

    assert response == {
        "authorizerContext": {
            "user": {
                "userId": "",
            },
            "account": {"accountId": "customer"},
        },
        "payload": {},
        "lineId": "uuid",
    }


@patch("lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipments_by_line_id")
def test_upload_config(
    get_equipments_by_line_id_mock, mocker, dynamodb_mock, mock_kpiconfig_assignment_broadcast_event
):
    get_all_machine_services_infos_mock = mocker.patch.object(ConfigGenerator, "get_all_machine_services_infos")
    get_all_machine_services_infos_mock.return_value = [
        {
            "customer": "customer",
            "readykit_uuid": "uuid",
            "machine_uuid": "machineId",
            "line_uuid": "uuid",
            "services": {"performance": {"properties": {"nominal speed": "1337"}}},
        }
    ]
    get_readykit_services_info_mock = mocker.patch.object(ConfigGenerator, "get_readykit_services_info")
    get_readykit_services_info_mock.return_value = {
        "id": "customer_uuid",
        "type": "readykit-services",
        "services": {"performance": {"options": ["greenfield"]}},
    }
    get_readykit_line_services_mock = mocker.patch.object(ConfigGenerator, "get_readykit_line_services")
    get_readykit_line_services_mock.return_value = [
        {
            "id": "customer",
            "type": "line-services_uuid",
            "uuid": "uuid",
            "services": {"performance": {"properties": {"kpi-model": "din_8743", "minor-stops": "5"}}},
        }
    ]

    customer_settings_mock = mocker.patch("config_generator.customer_settings")

    get_equipments_by_line_id_mock.return_value = [
        EquipmentFromCache(
            account="customer",
            equipment_id="equip1",
            created_at="2019-03-08T12:37:45.674Z",
            line_id="uuid",
            properties=[],
            version=1,
            tech_desc="some-tech-desc",
            level="some-level",
            icon_url="some-icon-url",
            description="some-description",
            updated_at="2021-09-01T00:00:00.000Z",
        )
    ]

    mocker.patch(
        "lib_kpi_config_client.api.api_client._get_name_by_id",
        return_value="NAME",
    )

    expected_settings = {
        "minor_stop_config": 5,
        "line_status_config": "mqtt",
        "machine_settings": {"machineId": {"nominal_speed": 1337}},
    }

    generator = ConfigGenerator("customer", "uuid", {}, "module_name")

    generator._upload_config()

    customer_settings_mock.create.create_configuration.assert_called_once_with(
        "customer", "uuid", "din_8743", "rk-performance-customer-settings", expected_settings
    )

    api_client = KpiConfigApiClient()
    assigned_kpi_models = api_client.get_kpi_models_assigned_at_time(account="customer", line_id="uuid", start=None)

    assert assigned_kpi_models is not None
    assert len(assigned_kpi_models) == 2
    assert assigned_kpi_models[0].kpi_model_id == "insight"
    assert assigned_kpi_models[0].start == *************
    assert assigned_kpi_models[1].kpi_model_id == "din_8743"
    assert assigned_kpi_models[1].start == *************


@patch("lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipments_by_line_id")
def test_upload_config_websocket(
    get_equipments_by_line_id_mock, mocker, dynamodb_mock, mock_kpiconfig_assignment_broadcast_event
):
    get_all_machine_services_infos_mock = mocker.patch.object(ConfigGenerator, "get_all_machine_services_infos")
    get_all_machine_services_infos_mock.return_value = [
        {
            "customer": "customer",
            "readykit_uuid": "uuid",
            "machine_uuid": "machineId",
            "line_uuid": "uuid",
            "services": {"performance": {"properties": {}}},
        }
    ]
    get_readykit_services_info_mock = mocker.patch.object(ConfigGenerator, "get_readykit_services_info")
    get_readykit_services_info_mock.return_value = {
        "id": "customer_uuid",
        "type": "readykit-services",
        "services": {"performance": {"options": ["greenfield-live"]}},
    }
    get_readykit_line_services_mock = mocker.patch.object(ConfigGenerator, "get_readykit_line_services")
    get_readykit_line_services_mock.return_value = [
        {
            "id": "customer",
            "type": "line-services_uuid",
            "uuid": "uuid",
            "services": {"performance": {"properties": {"kpi-model": "din_8743", "minor-stops": "5"}}},
        }
    ]

    customer_settings_mock = mocker.patch("config_generator.customer_settings")

    get_equipments_by_line_id_mock.return_value = [
        EquipmentFromCache(
            account="customer",
            equipment_id="equip1",
            created_at="2019-03-08T12:37:45.674Z",
            line_id="uuid",
            properties=[],
            version=1,
            tech_desc="some-tech-desc",
            level="some-level",
            icon_url="some-icon-url",
            description="some-description",
            updated_at="2021-09-01T00:00:00.000Z",
        )
    ]

    mocker.patch(
        "lib_kpi_config_client.api.api_client._get_name_by_id",
        return_value="NAME",
    )

    expected_settings = {
        "minor_stop_config": 5,
        "line_status_config": "websocket",
        "machine_settings": {"machineId": {"nominal_speed": 25000}},
    }

    generator = ConfigGenerator("customer", "uuid", {}, "module_name")

    generator._upload_config()

    customer_settings_mock.create.create_configuration.assert_called_once_with(
        "customer", "uuid", "din_8743", "rk-performance-customer-settings", expected_settings
    )

    api_client = KpiConfigApiClient()
    assigned_kpi_models = api_client.get_kpi_models_assigned_at_time(account="customer", line_id="uuid", start=None)

    assert assigned_kpi_models is not None
    assert len(assigned_kpi_models) == 2
    assert assigned_kpi_models[0].kpi_model_id == "insight"
    assert assigned_kpi_models[0].start == *************
    assert assigned_kpi_models[1].kpi_model_id == "din_8743"
    assert assigned_kpi_models[1].start == *************


@patch("lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipments_by_line_id")
def test_upload_config_no_options(
    get_equipments_by_line_id_mock, mocker, dynamodb_mock, mock_kpiconfig_assignment_broadcast_event
):
    get_all_machine_services_infos_mock = mocker.patch.object(ConfigGenerator, "get_all_machine_services_infos")
    get_all_machine_services_infos_mock.return_value = [
        {
            "customer": "customer",
            "readykit_uuid": "uuid",
            "machine_uuid": "machineId",
            "line_uuid": "uuid",
            "services": {"performance": {"properties": {}}},
        }
    ]
    get_readykit_services_info_mock = mocker.patch.object(ConfigGenerator, "get_readykit_services_info")
    get_readykit_services_info_mock.return_value = {
        "id": "customer_uuid",
        "type": "readykit-services",
        "services": {"performance": {}},
    }
    get_readykit_line_services_mock = mocker.patch.object(ConfigGenerator, "get_readykit_line_services")
    get_readykit_line_services_mock.return_value = [
        {
            "id": "customer",
            "type": "line-services_uuid",
            "uuid": "uuid",
            "services": {"performance": {"properties": {"kpi-model": "din_8743", "minor-stops": "5"}}},
        }
    ]

    customer_settings_mock = mocker.patch("config_generator.customer_settings")

    get_equipments_by_line_id_mock.return_value = [
        EquipmentFromCache(
            account="customer",
            equipment_id="equip1",
            created_at="2019-03-08T12:37:45.674Z",
            line_id="uuid",
            properties=[],
            version=1,
            tech_desc="some-tech-desc",
            level="some-level",
            icon_url="some-icon-url",
            description="some-description",
            updated_at="2021-09-01T00:00:00.000Z",
        )
    ]

    mocker.patch(
        "lib_kpi_config_client.api.api_client._get_name_by_id",
        return_value="NAME",
    )

    expected_settings = {
        "minor_stop_config": 5,
        "line_status_config": "mqtt",
        "machine_settings": {"machineId": {"nominal_speed": 25000}},
    }

    generator = ConfigGenerator("customer", "uuid", {}, "module_name")

    generator._upload_config()

    customer_settings_mock.create.create_configuration.assert_called_once_with(
        "customer", "uuid", "din_8743", "rk-performance-customer-settings", expected_settings
    )

    api_client = KpiConfigApiClient()
    assigned_kpi_models = api_client.get_kpi_models_assigned_at_time(account="customer", line_id="uuid", start=None)

    assert assigned_kpi_models is not None
    assert len(assigned_kpi_models) == 2
    assert assigned_kpi_models[0].kpi_model_id == "insight"
    assert assigned_kpi_models[0].start == *************
    assert assigned_kpi_models[1].kpi_model_id == "din_8743"
    assert assigned_kpi_models[1].start == *************


def get_environ_dict():
    return {
        "S2A_SHIFT_SERVICE_GET_LAMBDA_ARN": "get_shift_arn",
        "S2A_EQUIPMENTS_SERVICE_CROSS_ACCOUNT_ACCESS_ROLE_ARN": "equipments_role_arn",
        "S2A_SHIFT_SERVICE_CREATE_LAMBDA_ARN": "create_shift_arn",
        "S2A_SHIFT_SERVICE_UPDATE_LAMBDA_ARN": "update_shift_arn",
    }


@patch.dict(os.environ, get_environ_dict())
@patch("src.config_generator.utils.invoke_cross_account_lambda", side_effect=[{"FunctionError": "error"}, "", ""])
@patch.object(ConfigGenerator, "_upload_config")
@patch.object(ConfigGenerator, "_get_oc_shifts", return_value=get_oc_shifts_result())
@patch.object(ConfigGenerator, "get_readykit_line_ids", return_value=get_readykit_line_ids())
@patch("src.config_generator.utils.get_timezone", return_value="America/New_York")
def test_check_active_and_generate_create_shift(
    mocked_timezone_call,
    get_readykit_line_ids_mock,
    get_oc_shifts_mock,
    upload_config_mock,
    mocked_lambda_call,
    expected_get_shift_payload,
    expected_timezone_payload,
    expected_create_shift_payload,
):
    generator = ConfigGenerator("customer", "uuid", {"module_name": {}}, "module_name")

    generator.check_active_and_generate()

    # assert
    mocked_lambda_call.assert_any_call("get_shift_arn", expected_get_shift_payload, {})
    mocked_timezone_call.assert_called_with(expected_timezone_payload, {})
    mocked_lambda_call.assert_any_call("create_shift_arn", expected_create_shift_payload, {})


@patch.dict(os.environ, get_environ_dict())
@patch("src.config_generator.utils.invoke_cross_account_lambda", side_effect=[{}, "", ""])
@patch.object(ConfigGenerator, "_upload_config")
@patch.object(ConfigGenerator, "_get_oc_shifts", return_value=get_oc_shifts_result())
@patch.object(ConfigGenerator, "get_readykit_line_ids", return_value=get_readykit_line_ids())
@patch("src.config_generator.utils.get_timezone", return_value="America/New_York")
def test_check_active_and_generate_update_shift(
    mocked_timezone_call,
    get_readykit_line_ids_mock,
    get_oc_shifts_mock,
    upload_config_mock,
    mocked_lambda_call,
    expected_get_shift_payload,
    expected_timezone_payload,
    expected_create_shift_payload,
):
    generator = ConfigGenerator("customer", "uuid", {"module_name": {}}, "module_name")

    generator.check_active_and_generate()

    # assert
    mocked_lambda_call.assert_any_call("get_shift_arn", expected_get_shift_payload, {})
    mocked_timezone_call.assert_called_with(expected_timezone_payload, {})
    mocked_lambda_call.assert_any_call("update_shift_arn", expected_create_shift_payload, {})


def test_check_active_and_generate_inactive_module():
    generator = ConfigGenerator("customer", "uuid", {"other_module": {}}, "module_name")

    result = generator.check_active_and_generate()

    assert result is None
