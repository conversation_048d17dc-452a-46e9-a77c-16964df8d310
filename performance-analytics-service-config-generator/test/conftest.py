import os

import boto3
from lib_kpi_config_client.models.config_container import KpiModelConfigContainer
from lib_kpi_config_client.models.config_types import KpiModelNameConfig, KpiModelStatusConfig
from lib_kpi_config_client.models.enums import KpiModelConfigType, KpiModelStatus, KpiModelType
from lib_kpi_config_client.utils.constants import (
    KPI_MODEL_DB_CONFIG_TYPE,
    KPI_MODEL_DB_PARTITION_KEY,
    KPI_MODEL_DB_SORT_KEY,
    <PERSON><PERSON>_MODEL_DB_TABLE_NAME,
)
from moto import mock_aws
import pytest

os.environ["AWS_ACCESS_KEY_ID"] = "testing"
os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
os.environ["AWS_SECURITY_TOKEN"] = "testing"
os.environ["AWS_SESSION_TOKEN"] = "testing"
os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"


@pytest.fixture
def dynamodb_mock():
    with mock_aws():
        dynamodb = boto3.resource("dynamodb", "eu-central-1")
        dynamodb.create_table(
            TableName="rk-performance-customer-settings",
            KeySchema=[
                {"AttributeName": "account", "KeyType": "HASH"},
                {
                    "AttributeName": "line_id",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "account", "AttributeType": "S"},
                {"AttributeName": "line_id", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        dynamodb_table = dynamodb.create_table(
            TableName=KPI_MODEL_DB_TABLE_NAME,
            KeySchema=[
                {"AttributeName": KPI_MODEL_DB_PARTITION_KEY, "KeyType": "HASH"},
                {"AttributeName": KPI_MODEL_DB_SORT_KEY, "KeyType": "RANGE"},
            ],
            AttributeDefinitions=[
                {"AttributeName": KPI_MODEL_DB_PARTITION_KEY, "AttributeType": "S"},
                {"AttributeName": KPI_MODEL_DB_SORT_KEY, "AttributeType": "S"},
                {"AttributeName": KPI_MODEL_DB_CONFIG_TYPE, "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
            GlobalSecondaryIndexes=[
                {
                    "IndexName": "config_type",
                    "KeySchema": [
                        {"AttributeName": KPI_MODEL_DB_CONFIG_TYPE, "KeyType": "HASH"},
                        {"AttributeName": "pk", "KeyType": "RANGE"},
                    ],
                    "Projection": {
                        "ProjectionType": "ALL",
                    },
                }
            ],
        )
        dynamodb_table.put_item(
            Item=KpiModelConfigContainer(
                pk="global",
                sk="status#K#din_8743",
                config_type=KpiModelConfigType.STATUS,
                config=KpiModelStatusConfig(
                    kpi_model_id="din_8743", kpi_model_type=KpiModelType.GLOBAL, kpi_model_status=KpiModelStatus.ACTIVE
                ),
                created_at=1000,
                created_by="dummy",
                updated_at=1000,
                updated_by="dummy",
            ).model_dump(exclude_none=True)
        )
        dynamodb_table.put_item(
            Item=KpiModelConfigContainer(
                pk="global",
                sk="config#K#din_8743#T#name",
                config_type=KpiModelConfigType.NAME,
                config=KpiModelNameConfig(
                    kpi_model_id="din_8743", kpi_model_type=KpiModelType.GLOBAL, kpi_model_name="DIN 8743"
                ),
                updated_at=1730368535235,
            ).model_dump(exclude_none=True)
        )
        dynamodb_table.put_item(
            Item=KpiModelConfigContainer(
                pk="global",
                sk="status#K#insight",
                config_type=KpiModelConfigType.STATUS,
                config=KpiModelStatusConfig(
                    kpi_model_id="insight", kpi_model_type=KpiModelType.GLOBAL, kpi_model_status=KpiModelStatus.ACTIVE
                ),
                created_at=1000,
                created_by="dummy",
                updated_at=1000,
                updated_by="dummy",
            ).model_dump(exclude_none=True)
        )
        dynamodb_table.put_item(
            Item=KpiModelConfigContainer(
                pk="global",
                sk="config#K#insight#T#name",
                config_type=KpiModelConfigType.NAME,
                config=KpiModelNameConfig(
                    kpi_model_id="insight", kpi_model_type=KpiModelType.GLOBAL, kpi_model_name="Insight"
                ),
                updated_at=1730368535235,
            ).model_dump(exclude_none=True)
        )
        yield dynamodb
