
SHELL:=/bin/bash

.DEFAULT_GOAL := help

GIT_COMMIT = $(shell git rev-parse HEAD)
BRANCH_NAME = $(shell git symbolic-ref --short HEAD)

CWD := $(shell pwd)
SERVICE_PATH = $(shell cd ../; pwd)
SERVICE_NAME ?= $(shell basename $(SERVICE_PATH))
FUNCTION_NAME ?= $(shell basename $(CWD))

RK_BUCKET ?= rk-deploy-jenkins-dev
S3_PATH = $(SERVICE_NAME)/$(BRANCH_NAME)
ZIP_FILE ?= $(FUNCTION_NAME)-$(GIT_COMMIT).zip

# Export variables for sub-makefiles
export TEST_FILES_OR_DIRS := test
export BUNDLE_DIR := build

################################################################
######    Fall back to targets in shared Makefile     ##########
################################################################

# Hack to automatically update submodules
SUBMODULE := $(shell git submodule update --init --recursive)

.PHONY: %
%: Makefile
	@$(MAKE) -e -f ../.configs/Makefile $@

.PHONY: Makefile
Makefile: ;

.PHONY: init
init:
	@echo "Initialization already done with install. Skipping..."

.PHONY: bundle
bundle: clean ## bundles the service
	mkdir -p ${BUNDLE_DIR}/libs/ ${BUNDLE_DIR}/distributions/
	poetry export --without-hashes -f requirements.txt --output requirements.txt --with-credentials
	poetry run pip install -r requirements.txt -t ${BUNDLE_DIR}/libs/
	cd ${BUNDLE_DIR}/libs/; zip -qr ../../${BUNDLE_DIR}/distributions/$(ZIP_FILE) . ; cd ../../
	cd src; zip -qr ../${BUNDLE_DIR}/distributions/$(ZIP_FILE) . ; cd ../

####################################################
# update this specific lambda function manually
update-fkn: upload update-lambda-code

upload:
	aws s3 cp build/distributions/$(ZIP_FILE) s3://$(RK_BUCKET)/$(S3_PATH)/

update-lambda-code:
	aws lambda update-function-code --function-name $(FUNCTION_NAME) --s3-bucket $(RK_BUCKET) --s3-key $(S3_PATH)/$(ZIP_FILE) --no-cli-pager
