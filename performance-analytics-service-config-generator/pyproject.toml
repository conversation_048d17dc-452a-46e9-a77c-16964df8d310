#POETRY

[tool.poetry]
name = "performance-analytics-service-config-generator"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
readykit-lib-config-creator = "^4"
ft2-customer-settings = "^4"
boto3 = "^1.28"
lib-kpi-config-client = "^3.1"
lib-public-api-utilities = "^7"
lib-equipment-cache-common = "^2"
openapi_spec_validator = "*"
jsonschema = "^4"

[tool.poetry.group.dev.dependencies]
moto = { extras = ["awslambda", "cloudformation", "apigateway"], version = "*" }
mock = "*"
pytest = "^7"
pytest-cov = "^4"
pytest-mock = "^3"
pre-commit = "^3"
toml = "^0"
mypy = "^1"
ruff = "^0"
boto3-stubs = { extras= ["essential"], version="^1"}

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

#PYTEST

[tool.pytest.ini_options]
addopts = " -rf --strict --durations 10 --color yes --junitxml=../test-reports/report/performance-analytics-service-config-generator.xml"
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::ImportWarning",
    "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = ["src"]
testpaths = ["test"]

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 46

[tool.coverage.xml]
output = "../test-reports/coverage/performance-analytics-service-config-generator.xml"

#MYPY
[tool.mypy]
incremental = true
cache_dir = ".mypy_cache"
python_version = "3.13"
disallow_untyped_defs = true
follow_imports = "silent"
disallow_untyped_calls = true
disallow_incomplete_defs = true
exclude = ["test"]
mypy_path = ["src"]
namespace_packages =  true
explicit_package_bases = true

[[tool.mypy.overrides]]
module = ["config_creator_base.*"]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = ["customer_settings.*"]
ignore_missing_imports = true

#RUFF
[tool.ruff]
src = ["src", "test"]
target-version = 'py313'
extend = "../.configs/ruff.toml"

[tool.ruff.lint.isort]
known-local-folder = ["test"]
