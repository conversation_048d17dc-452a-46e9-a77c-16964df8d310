from datetime import datetime
import os
import sys
from typing import Any

from aws_lambda_powertools import Logger
from config_creator_base.config_creator_base import ConfigCreatorBase
import customer_settings.create
from ibt_crud.helpers import traverse
from lib_equipment_cache_common.clients.equipment_client import EquipmentClient
from lib_equipment_cache_common.exceptions.exceptions import EquipmentClientResponseError
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
from lib_kpi_config_client.database.time_validity_db_client import TimeValidityDatabaseClient
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelType
from lib_kpi_config_client.utils.constants import KPI_MODEL_INSIGHT

import utils

LOGGER = Logger()

CUSTOMER_SETTINGS_TABLE = "rk-performance-customer-settings"
SERVICES = "services"
PERFORMANCE = "performance"
SERVICE_OPTIONS = "options"
SERVICE_PROPERTIES = "properties"
STREAMING_OPTION = "greenfield-live"

time_validity_database_client = TimeValidityDatabaseClient()
kpi_config_api_client = KpiConfigApiClient()


class ConfigGenerator(ConfigCreatorBase):
    def generate_and_upload_config(self, _parameters=None) -> None:  # type: ignore[no-untyped-def]
        self._upload_config()

        for line_uuid in self.get_readykit_line_ids():
            cache: dict = {}
            payload = self._format_shifts_get_payload(line_uuid)
            function_name = os.environ["S2A_SHIFT_SERVICE_GET_LAMBDA_ARN"]
            response_get = utils.invoke_cross_account_lambda(function_name, payload, cache)

            timezone_payload = self._format_timezone_payload(line_uuid)
            timezone = utils.get_timezone(timezone_payload, cache)
            if not isinstance(timezone, str):
                continue

            if "FunctionError" in response_get:
                # Shift not found. Create it
                shifts = self._get_oc_shifts(timezone, line_uuid)
                shifts_payload = self._format_shifts_payload(shifts, line_uuid)
                function_name = os.environ["S2A_SHIFT_SERVICE_CREATE_LAMBDA_ARN"]
                utils.invoke_cross_account_lambda(function_name, shifts_payload, cache)
            else:
                # Shift found. Update it
                shifts = self._get_oc_shifts(timezone, line_uuid)
                shifts_payload = self._format_shifts_payload(shifts, line_uuid)
                function_name = os.environ["S2A_SHIFT_SERVICE_UPDATE_LAMBDA_ARN"]
                utils.invoke_cross_account_lambda(function_name, shifts_payload, cache)

    def _upload_config(self) -> None:
        readykit_services = self.get_readykit_services_info()

        service_options = traverse(readykit_services, SERVICES, PERFORMANCE, SERVICE_OPTIONS)
        line_status = "websocket" if service_options and STREAMING_OPTION in service_options else "mqtt"

        for line_services in self.get_readykit_line_services():
            kpi_model_id = traverse(line_services, SERVICES, PERFORMANCE, SERVICE_PROPERTIES, "kpi-model")
            minor_stops = traverse(line_services, SERVICES, PERFORMANCE, SERVICE_PROPERTIES, "minor-stops")

            machine_settings = {}
            for machine in self.get_all_machine_services_infos(
                line_uuid=line_services["uuid"], service_name=PERFORMANCE
            ):
                LOGGER.debug('extracting settings for machine "%s"', machine["machine_uuid"])

                performance_properties = traverse(machine, SERVICES, PERFORMANCE, SERVICE_PROPERTIES)
                LOGGER.debug(
                    'performance properties for machine "%s" are "%s"',
                    machine["machine_uuid"],
                    performance_properties,
                )

                nominal_speed = int(traverse(performance_properties, "nominal speed", default=25000))
                LOGGER.debug(
                    'extracted nominal speed for machine "%s" is "%s"',
                    machine["machine_uuid"],
                    nominal_speed,
                )

                machine_settings[machine["machine_uuid"]] = {"nominal_speed": nominal_speed}

            account = self.customer
            line = line_services["uuid"]

            table = CUSTOMER_SETTINGS_TABLE
            settings = {
                "minor_stop_config": int(minor_stops) if isinstance(minor_stops, str) else 0,
                "line_status_config": line_status,
                "machine_settings": machine_settings,
            }

            customer_settings.create.create_configuration(account, line, kpi_model_id, table, settings)

            if kpi_model_id is not None and kpi_model_id != "":
                if not kpi_config_api_client.get_kpi_model_by_id(kpi_model_id=kpi_model_id, account=account):
                    LOGGER.warning("Invalid kpi model id sent: kpi_model_id=%s, account=%s", kpi_model_id, account)
                    # should not let deployment fail
                    return

                equipment_client = EquipmentClient(account)

                try:
                    equipments = equipment_client.get_equipments_by_line_id(line_id=line)
                except EquipmentClientResponseError:
                    # should not let deployment fail
                    LOGGER.warning("Failed to get equipments for line %s", line)
                    return

                if equipments:
                    created_at = sys.maxsize
                    for equipment in equipments:
                        equipment_created_at = int(datetime.fromisoformat(equipment.created_at).timestamp() * 1000)
                        created_at = min(equipment_created_at, created_at)

                    kpi_models = [KpiModel(kpi_model_id=KPI_MODEL_INSIGHT, kpi_model_type=KpiModelType.GLOBAL)]
                    if kpi_model_id != KPI_MODEL_INSIGHT:
                        kpi_models.append(KpiModel(kpi_model_id=kpi_model_id, kpi_model_type=KpiModelType.GLOBAL))
                    time_validity_database_client.assign_kpi_models(
                        account=account,
                        line_id=line,
                        kpi_models=kpi_models,
                        start=created_at,
                        user_id="config_generator",
                    )
                else:
                    LOGGER.info("No equipments found for line %s", line)

    def _get_authorizercontext(self) -> dict[str, Any]:
        return {
            "user": {
                "userId": "",
            },
            "account": {"accountId": self.customer},
        }

    def _format_shifts_get_payload(self, line_uuid: str) -> dict[str, Any]:
        return {
            "authorizerContext": self._get_authorizercontext(),
            "payload": {},
            "lineId": line_uuid,
        }

    def _format_timezone_payload(self, line_uuid: str) -> dict[str, Any]:
        return {
            "authorizerContext": self._get_authorizercontext(),
            "payload": {"descendantId": line_uuid},
        }

    def _format_shifts_payload(self, shifts: Any, line_uuid: str) -> dict[str, Any]:
        return {
            "authorizerContext": self._get_authorizercontext(),
            "payload": shifts,
            "lineId": line_uuid,
        }

    def _get_oc_shifts(self, timezone: str, line_uuid: str) -> dict[str, Any]:
        shifts = self.get_shifts(line_uuid)
        duration, _ = self.get_shift_duration_and_offset(line_uuid)

        for index in range(len(shifts)):
            shifts[index].pop("end")
            shifts[index]["time"] = shifts[index].pop("start")
            shifts[index]["name"] = f"Shift-{index + 1}"
        request_payload = {
            "localDailySchedule": {"duration": duration, "shifts": shifts},
            "timezone": timezone,
        }

        return request_payload
