# Copyright (c) 2020, Syskron X GmbH. All rights reserved.
from aws_lambda_powertools import Logger

from config_generator import ConfigGenerator

LOGGER = Logger()


def lambda_handler(event, context) -> dict[str, str | None] | None:  # type: ignore[no-untyped-def] # noqa: ARG001
    LOGGER.info('Start performance-analytics-service-config-generator with event= "%s"', event)

    generator = ConfigGenerator(event["customer"], event["uuid"], event["modules"], event["moduleName"])

    return generator.check_active_and_generate()
