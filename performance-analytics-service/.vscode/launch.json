{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "justMyCode": false
    },
    {
      "name": "Debug Unit Test",
      "type": "python",
      "request": "test",
      "justMyCode": false
    },
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "justMyCode": false,
      "args": [
        "lambda_function:app",
        "--app-dir",
        "src",
        "--reload-dir",
        "src",
        "--reload",
        "--port",
        "8000"
      ],
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
