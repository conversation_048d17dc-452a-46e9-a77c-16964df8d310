#POETRY

[tool.poetry]
name = "performance-analytics-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]
package-mode = false

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
aws-xray-sdk = "^2"
pydantic = { extras = ["email"], version = "^2" }
aws-lambda-powertools = "^2"
lib-performance-analytics = "^33"
ft2-cloud-sdk = "^16"
lib-kpi-config-client = "^3.2"
lib-dtm-client = "^22"
fastapi = "^0"
mangum = "^0"
performance-message-texts = "^9"
lib_s2a_events_v3 = "^35.6"

[tool.poetry.group.dev.dependencies]
moto = { extras = ["awslambda"], version = "*" }
mock = "*"
pre-commit = "*"
pytest = "^7"
pytest-cov = "*"
httpx = "*"
toml = "^0"
mypy = "^1"
ruff = "^0"
types-pytz = "^2024"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

#PYTEST

[tool.pytest.ini_options]
addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes --junitxml=../test-reports/report/performance-analytics-service-cov.xml"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = ["src"]
testpaths = ["test"]

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 92

[tool.coverage.xml]
output = "../test-reports/coverage/performance-analytics-service-cov.xml"

#MYPY
[tool.mypy]
incremental = true
cache_dir = ".mypy_cache"
python_version = "3.13"
disallow_untyped_defs = true
follow_imports = "silent"
disallow_untyped_calls = true
disallow_incomplete_defs = true
exclude = ["test"]
mypy_path = ["src"]
namespace_packages =  true
explicit_package_bases = true
plugins = [
  "pydantic.mypy"
]

#RUFF
[tool.ruff]
src = ["src", "test"]
target-version = 'py313'
extend = "../.configs/ruff.toml"

[tool.ruff.lint.isort]
known-local-folder = ["test"]
