{"accountId": "readykit-replay", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "kpiModel": "din_8743", "downtimes": [{"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 331749, "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": *************, "end": *************, "duration": 331749, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "external_failure", "start": *************, "end": *************, "duration": 133209, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": *************, "raw_values": {"state": 16, "aggregated_state": 16, "mode": 8, "program": 1}, "children": [{"start": *************, "end": 1634875426341, "duration": 127199, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"state": 16, "aggregated_state": 16, "mode": 8, "program": 1}, "category": "external_failure", "classification": "tailback"}, {"start": 1634875426341, "end": *************, "duration": 6010, "raw_values": {"state": 4, "aggregated_state": 16, "mode": 8, "program": 1}, "category": "external_failure", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": 1634875862350, "duration": 429999, "last_update_at": 1652854206642, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": *************, "end": 1634875862350, "duration": 429999, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634875862350, "end": 1634875899426, "duration": 37076, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1652854207382, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634875862350, "end": 1634875874385, "duration": 12035, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634875874385, "end": 1634875899426, "duration": 25041, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634875899426, "end": 1634876025747, "duration": 126321, "last_update_at": 1652854207763, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634875899426, "end": 1634876025747, "duration": 126321, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634876025747, "end": 1634876117864, "duration": 92117, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1652854208502, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634876025747, "end": 1634876111858, "duration": 86111, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634876111858, "end": 1634876117864, "duration": 6006, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634876117864, "end": 1634876155949, "duration": 38085, "last_update_at": 1652854208891, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634876117864, "end": 1634876155949, "duration": 38085, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634876155949, "end": 1634876192026, "duration": 36077, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1652854209625, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634876155949, "end": 1634876173989, "duration": 18040, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634876173989, "end": 1634876192026, "duration": 18037, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634876192026, "end": 1634876231148, "duration": 39122, "last_update_at": 1652854209963, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634876192026, "end": 1634876231148, "duration": 39122, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634876231148, "end": 1634876281242, "duration": 50094, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1652854210716, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634876231148, "end": 1634876275230, "duration": 44082, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634876275230, "end": 1634876281242, "duration": 6012, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634876281242, "end": 1634876607988, "duration": 326746, "last_update_at": 1652854211098, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634876281242, "end": 1634876607988, "duration": 326746, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634876607988, "end": 1634876763248, "duration": 155260, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1652854211741, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634876607988, "end": 1634876757238, "duration": 149250, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634876757238, "end": 1634876763248, "duration": 6010, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634876763248, "end": 1634878158563, "duration": 1395315, "last_update_at": 1652854212100, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634876763248, "end": 1634878158563, "duration": 1395315, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "external_failure", "start": 1634878158563, "end": 1634878230680, "duration": 72117, "message": {"configId": "4152", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Inspector"}, "last_update_at": 1652854213469, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"start": 1634878158563, "end": 1634878171591, "duration": 13028, "message": {"configId": "4152", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Inspector"}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634878171591, "end": 1634878185607, "duration": 14016, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634878185607, "end": 1634878224684, "duration": 39077, "raw_values": {"state": 16, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}, {"start": 1634878224684, "end": 1634878230680, "duration": 5996, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "category": "external_failure", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1634878230680, "end": 1634878530680, "duration": 300000, "last_update_at": 1661345613017, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"start": 1634878230680, "end": 1634878530680, "duration": 300000, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "category": "productive", "classification": "productive"}]}, {"classification": "clean", "category": "scheduled_down_time", "start": 1634878530680, "end": 1634879049637, "duration": 518957, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661352468877, "raw_values": {"state": 16384, "aggregated_state": 1024, "mode": 8, "program": 8}, "children": [{"start": 1634878530680, "end": 1634878730680, "duration": 200000, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"state": 16384, "aggregated_state": 1024, "mode": 8, "program": 8}, "category": "scheduled_down_time", "classification": "clean"}, {"start": 1634878730680, "end": 1634879049637, "duration": 318957, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"state": 4, "aggregated_state": 1024, "mode": 8, "program": 16}, "category": "scheduled_down_time", "classification": "changeover"}]}]}