{"accountId": "readykit-replay", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "kpiModel": "insight", "downtimes": [{"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 331749, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 331749, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "tailback", "category": "tailback", "start": *************, "end": *************, "duration": 133209, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 16, "aggregated_state": 16, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 127199, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "classification": "tailback", "category": "tailback", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 16, "aggregated_state": 16, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6010, "classification": "tailback", "category": "tailback", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 16, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 429999, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 429999, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 37076, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 12035, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 25041, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 126321, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 126321, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 92117, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 86111, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6006, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 38085, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 38085, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 36077, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 18040, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 18037, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 39122, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 39122, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 50094, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 44082, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6012, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 326746, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 326746, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 155260, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 149250, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6010, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 1395315, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1395315, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "lack", "category": "lack", "start": *************, "end": *************, "duration": 72117, "message": {"configId": "4152", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Inspector"}, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 13028, "message": {"configId": "4152", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Inspector"}, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 8, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 14016, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 39077, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 16, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}, {"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 5996, "classification": "lack", "category": "lack", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 4, "aggregated_state": 8, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 818957, "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "children": [{"kpi_model": "insight", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 818957, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"state": 128, "aggregated_state": 128, "mode": 8, "program": 1}, "account_kpimodel_equipment": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_child"}]}]}