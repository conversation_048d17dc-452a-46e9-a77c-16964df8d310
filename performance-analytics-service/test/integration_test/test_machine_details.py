import json
from unittest.mock import patch

from fastapi import HTTPException
from fastapi.testclient import TestClient
from lib_cloud_sdk.util.file_io import read_json_file
from lib_dtm_common.models.api_models import GetDowntimeResponse, ResponseDowntime
import pytest

PATH = "v1/performance-analytics/stoppage-analysis/machine-details"


@pytest.mark.parametrize(
    (
        "url",
        "payload",
        "expected_result",
        "performance_kpi_model_items",
        "downtimes",
        "touch_messages",
        "failure_modes",
    ),
    [
        # no filters applied. kpi model = din_8743, messages = english,
        # no filtering by duration applied. no custom failure mode mapping available.
        (
            PATH,
            {
                "failure_mode": "4152.364",
                "filters": {"by_category": {}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T04:00:00.000Z",
                    "end_time": "2021-10-22T05:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file("test/integration_test/res/expected_results/expected_result_machine_details.json"),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by scheduled_down_time -> clean.
        # kpi model = din_8743, messages = english,
        # no filtering by duration applied. no custom failure mode mapping available.
        (
            PATH,
            {
                "failure_mode": "AM_clean",
                "filters": {"by_category": {"scheduled_down_time": ["clean"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T04:00:00.000Z",
                    "end_time": "2021-10-22T05:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            {
                "failure_mode": "AM_clean",
                "failure_mode_details": [
                    {
                        "duration": 200000,
                        "classification": "clean",
                        "category": "scheduled_down_time",
                        "start": "2021-10-22T04:55:30.680Z",
                        "end": "2021-10-22T04:58:50.680Z",
                        "message": {
                            "configId": "AM_clean",
                            "messageType": "artificial",
                        },
                        "downtimes": [
                            {
                                "classification": "clean",
                                "mds_state_code": 1024,
                                "mode": 8,
                                "program": 8,
                                "start": 1634878530680,
                                "ws_cur_state_code": 16384,
                                "end": 1634878730680,
                                "duration": 200000,
                                "category": "scheduled_down_time",
                            }
                        ],
                    }
                ],
            },
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file(
                "test/integration_test/res/input_data/downtimes_din_8743_with_children_without_message_clean.json"
            ),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by internal_failure (no match for 4152.364).
        # kpi model = din_8743, messages = english,
        # no filtering by duration applied.
        (
            PATH,
            {
                "failure_mode": "4152.364",
                "filters": {"by_category": {"internal_failure": []}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T04:00:00.000Z",
                    "end_time": "2021-10-22T05:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_machine_details_with_category_filters_no_match.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by scheduled_down_time. kpi model = din_8743, messages = english,
        # filter by duration 5000 < x < 600000 (minor stops).
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "failure_mode": "AM_clean",
                "filters": {"by_category": {"scheduled_down_time": []}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "max_duration": 600000,
                "min_duration": 5000,
                "time": {
                    "start_time": "2021-10-22T04:00:00.000Z",
                    "end_time": "2021-10-22T05:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            {
                "failure_mode": "AM_clean",
                "failure_mode_details": [
                    {
                        "duration": 518957,
                        "classification": "clean",
                        "category": "scheduled_down_time",
                        "start": "2021-10-22T04:55:30.680Z",
                        "end": "2021-10-22T05:00:00.000Z",
                        "message": {
                            "configId": "AM_clean",
                            "messageType": "artificial",
                        },
                        "downtimes": [
                            {
                                "classification": "clean",
                                "mds_state_code": 1024,
                                "mode": 8,
                                "program": 8,
                                "start": 1634878530680,
                                "ws_cur_state_code": 16384,
                                "end": 1634878730680,
                                "duration": 200000,
                                "category": "scheduled_down_time",
                            },
                            {
                                "classification": "changeover",
                                "mds_state_code": 1024,
                                "mode": 8,
                                "program": 16,
                                "start": 1634878730680,
                                "ws_cur_state_code": 4,
                                "end": 1634879049637,
                                "duration": 318957,
                                "category": "scheduled_down_time",
                            },
                        ],
                    }
                ],
            },
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file(
                "test/integration_test/res/input_data/downtimes_din_8743_with_children_without_message_clean.json"
            ),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # no filters applied. kpi model = din_8743, messages = german, no filtering by duration applied.
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "failure_mode": "4152.364",
                "filters": {"by_category": {}},
                "language": "de",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T04:00:00.000Z",
                    "end_time": "2021-10-22T05:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_machine_details_with_german_messages.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
    ],
)
def test_machine_details_endpoint(
    url,
    payload,
    expected_result,
    performance_kpi_model_items,
    downtimes,
    touch_messages,
    failure_modes,
    aws_event_mock,
    dynamo_table_mocks,
    s3_client,
    downtime_client_mock,
):
    # Arrange
    from lambda_function import app

    # 'Store' data into respective DynamoDB tables
    performance_kpi_model_items += read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("insight") + read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("din_8743")
    performance_kpi_model = dynamo_table_mocks.get("performance_kpi_model")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(TableName="performance-kpi-model", Item=performance_kpi_model_item)
    rk_failure_mode = dynamo_table_mocks.get("rk_failure_mode")
    rk_failure_mode.put_item(TableName="rk-failure-mode-table", Item=failure_modes)

    # 'Store' messages in S3
    bucket_name = "rk-zait-testing"
    key_name = "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))

    downtime_client_mock.side_effect = [
        GetDowntimeResponse(
            account_id=downtimes["accountId"],
            equipment_id=downtimes["equipmentId"],
            kpi_model=downtimes["kpiModel"],
            downtimes=[ResponseDowntime(**downtime) for downtime in downtimes["downtimes"]],
        )
    ]
    # Act
    client = TestClient(app)
    response = client.post(url, json=payload).json()

    # Assert
    assert response == expected_result


@patch("api.endpoints.machine_details.QueryHandler.get_downtimes_for_machines")
def test_exception_behaviour(
    query_handler_mock,
    aws_event_mock,
    dynamo_table_mocks,
    downtime_client_mock,
):
    from lambda_function import app

    payload = {
        "failure_mode": "4152.364",
        "filters": {"by_category": {}},
        "language": "en",
        "kpi_model": "din_8743",
        "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
        "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
        "max_duration": 0,
        "min_duration": 0,
        "time": {
            "start_time": "2021-10-22T04:00:00.000Z",
            "end_time": "2021-10-22T05:00:00.000Z",
            "timezone": "Europe/Berlin",
        },
    }

    performance_kpi_model_items = read_json_file("test/integration_test/res/input_data/performance_kpi_model.json")
    failure_modes = read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified")

    # 'Store' data into respective DynamoDB tables
    performance_kpi_model_items += read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("insight") + read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("din_8743")
    performance_kpi_model = dynamo_table_mocks.get("performance_kpi_model")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(TableName="performance-kpi-model", Item=performance_kpi_model_item)
    rk_failure_mode = dynamo_table_mocks.get("rk_failure_mode")
    rk_failure_mode.put_item(TableName="rk-failure-mode-table", Item=failure_modes)

    query_handler_mock.side_effect = Exception

    client = TestClient(app)
    with pytest.raises(Exception) as exception:
        client.post(PATH, json=payload).json()

    assert exception.typename == "Exception"


@patch("api.endpoints.machine_details.QueryHandler.get_downtimes_for_machines")
def test_http_exception_behaviour(
    query_handler_mock,
    aws_event_mock,
    dynamo_table_mocks,
    downtime_client_mock,
):
    from lambda_function import app

    payload = {
        "failure_mode": "4152.364",
        "filters": {"by_category": {}},
        "language": "en",
        "kpi_model": "din_8743",
        "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
        "machine": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
        "max_duration": 0,
        "min_duration": 0,
        "time": {
            "start_time": "2021-10-22T04:00:00.000Z",
            "end_time": "2021-10-22T05:00:00.000Z",
            "timezone": "Europe/Berlin",
        },
    }

    performance_kpi_model_items = read_json_file("test/integration_test/res/input_data/performance_kpi_model.json")
    failure_modes = read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified")

    # 'Store' data into respective DynamoDB tables
    performance_kpi_model_items += read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("insight") + read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("din_8743")
    performance_kpi_model = dynamo_table_mocks.get("performance_kpi_model")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(TableName="performance-kpi-model", Item=performance_kpi_model_item)
    rk_failure_mode = dynamo_table_mocks.get("rk_failure_mode")
    rk_failure_mode.put_item(TableName="rk-failure-mode-table", Item=failure_modes)

    query_handler_mock.side_effect = HTTPException(500)

    client = TestClient(app)
    response = client.post(PATH, json=payload).json()

    assert response.get("detail") == "Internal Server Error"
