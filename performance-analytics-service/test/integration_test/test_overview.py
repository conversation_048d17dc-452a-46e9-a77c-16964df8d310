import json

from fastapi.testclient import TestClient
from lib_cloud_sdk.util.file_io import read_json_file
from lib_dtm_common.models.api_models import GetDowntimeResponse, ResponseDowntime
import pytest

PATH = "v1/performance-analytics/stoppage-analysis/overview"


@pytest.mark.parametrize(
    (
        "url",
        "payload",
        "expected_result",
        "performance_kpi_model_items",
        "downtimes",
        "touch_messages",
        "failure_modes",
    ),
    [
        # no filters applied. kpi model = din_8743, messages = english, no filtering by duration applied.
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file("test/integration_test/res/expected_results/expected_result_overview.json"),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # no filters applied. kpi model = din_8743, messages = english, no filtering by duration applied,
        # with artificial message.
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_artificial_message.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_without_message_clean.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by external_failure -> lack, scheduled_down_time -> clean. kpi model = din_8743, messages = english,
        # no filtering by duration applied. no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {
                    "by_category": {
                        "external_failure": ["lack"],
                        "scheduled_down_time": ["clean"],
                    }
                },
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_category_and_classification_filters.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by external_failure -> lack, scheduled_down_time -> clean. kpi model = din_8743, messages = english,
        # no filtering by duration applied, with artificial message. no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {
                    "by_category": {
                        "external_failure": ["lack"],
                        "scheduled_down_time": ["clean"],
                    }
                },
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_category_and_classification_filters_with_artificial_message.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file(
                "test/integration_test/res/input_data/downtimes_din_8743_with_children_without_message_clean.json"
            ),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by internal_failure (no match based on the test data). kpi model = din_8743, messages = english,
        # no filtering by duration applied. no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {"internal_failure": []}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_category_filters.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by external_failure -> tailback. kpi model = din_8743, messages = english,
        # filter by duration 5000 < x < 300000 (minor stops). no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {"external_failure": ["tailback"]}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 300000,
                "min_duration": 5000,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_category_and_classification_and_duration_filters.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # filters by external_failure -> lack. kpi model = din_8743, messages = english,
        # no filtering by duration applied.
        # custom failure mode mapping available for message_id: "4152".
        (
            PATH,
            {
                "filters": {"by_category": {"external_failure": ["lack"]}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 300000,
                "min_duration": 5000,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_category_and_classification_filters_and_custom_failure_modes_mapping.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_with_children.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("cfm_specified"),
        ),
        # no filters applied. kpi model = din_8743, messages = german, no filtering by duration applied.
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {}},
                "language": "de",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_german_messages.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_de.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # no filters applied. kpi model = din_8743, messages = german,
        # no filtering by duration applied, with artificial message.
        # no custom failure mode mapping available.
        (
            PATH,
            {
                "filters": {"by_category": {}},
                "language": "de",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-23T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file(
                "test/integration_test/res/expected_results/expected_result_overview_with_german_messages_with_artificial_message.json"
            ),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_din_8743_without_message_clean_de.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
        # no filters applied. kpi model = din_8743, messages = english, no filtering by duration applied.
        # no custom failure mode mapping available.
        # ovlapping downtimes in days.
        (
            PATH,
            {
                "filters": {"by_category": {}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00+02:00",
                    "end_time": "2021-10-24T00:00:00+02:00",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file("test/integration_test/res/expected_results/expected_result_overview_split.json"),
            read_json_file("test/integration_test/res/input_data/performance_kpi_model.json"),
            read_json_file("test/integration_test/res/input_data/downtimes_overlap.json"),
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
        ),
    ],
)
def test_overview_endpoint(
    url,
    payload,
    expected_result,
    performance_kpi_model_items,
    downtimes,
    touch_messages,
    failure_modes,
    aws_event_mock,
    dynamo_table_mocks,
    s3_client,
    downtime_client_mock,
):
    # Arrange
    from lambda_function import app

    # 'Store' data into respective DynamoDB tables
    performance_kpi_model_items += read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("insight") + read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("din_8743")
    performance_kpi_model = dynamo_table_mocks.get("performance_kpi_model")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(TableName="performance-kpi-model", Item=performance_kpi_model_item)
    rk_failure_mode = dynamo_table_mocks.get("rk_failure_mode")
    rk_failure_mode.put_item(TableName="rk-failure-mode-table", Item=failure_modes)

    # 'Store' messages in S3
    bucket_name = "rk-zait-testing"
    key_name = "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))

    # Set return values for cross account lambda mocks

    downtime_client_mock.side_effect = [
        GetDowntimeResponse.construct(
            account_id=downtimes["accountId"],
            equipment_id=downtimes["equipmentId"],
            kpi_model=downtimes["kpiModel"],
            downtimes=[ResponseDowntime(**downtime) for downtime in downtimes["downtimes"]],
        )
    ]
    # Act
    client = TestClient(app)
    response = client.post(url, json=payload).json()

    # Assert
    assert response == expected_result
