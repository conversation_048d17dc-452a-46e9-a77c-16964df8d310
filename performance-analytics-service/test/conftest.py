import json
import os
from unittest.mock import patch

import boto3
from fastapi import Request
from lib_dtm_common.models.api_models import GetDowntimeResponse
from moto import mock_aws
import pytest


@pytest.fixture
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["SQS_URL"] = "testing"
    os.environ["S2A_AWS_STAGE"] = "testing"
    os.environ["RK_AWS_STAGE"] = "testing"
    os.environ["S2A_ROLE_ARN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"


_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["performance-analytics", "scope-1", "scope-2"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps(
            {
                "userId": "some-user-id",
                "username": "some-user-name",
                "login": "some-login",
                "groups": ["group-1", "site-manager"],
            }
        ),
        "account": json.dumps({"accountId": "readykit-replay", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture
def authorizer_event(aws_credentials):
    event = {
        "aws.event": {"requestContext": _request_context},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    return request


@pytest.fixture
def aws_event_mock(authorizer_event):
    from performance_analytics.utility.s2a_request_wrapper import (
        CommonShare2ActProperties,
    )

    with patch("performance_analytics.utility.s2a_request_wrapper.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def dynamo_table_mocks(aws_credentials):
    with mock_aws():
        db_client = boto3.resource("dynamodb", region_name="eu-central-1")
        performance_kpi_model = db_client.create_table(
            TableName="performance-kpi-model",
            KeySchema=[
                {"AttributeName": "pk", "KeyType": "HASH"},
                {
                    "AttributeName": "sk",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "pk", "AttributeType": "S"},
                {"AttributeName": "sk", "AttributeType": "S"},
                {"AttributeName": "config_type", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
            GlobalSecondaryIndexes=[
                {
                    "IndexName": "config_type",
                    "KeySchema": [
                        {"AttributeName": "config_type", "KeyType": "HASH"},
                        {"AttributeName": "pk", "KeyType": "RANGE"},
                    ],
                    "Projection": {
                        "ProjectionType": "ALL",
                    },
                }
            ],
        )
        rk_failure_mode = db_client.create_table(
            TableName="rk-failure-mode-table",
            KeySchema=[
                {"AttributeName": "line_id", "KeyType": "HASH"},
                {
                    "AttributeName": "failure_mode_id",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "line_id", "AttributeType": "S"},
                {"AttributeName": "failure_mode_id", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )

        yield {
            "performance_kpi_model": performance_kpi_model,
            "rk_failure_mode": rk_failure_mode,
        }


@pytest.fixture
def s3_client(aws_credentials):
    with mock_aws():
        s3_client = boto3.client("s3", region_name="us-east-1")
        s3_client.create_bucket(Bucket="rk-zait-testing")
        yield s3_client


@pytest.fixture
def downtime_client_mock():
    with patch("lib_dtm_client.clients.query_handler._get_downtimes") as dtm_client_mock:
        dtm_client_mock.side_effect = [
            GetDowntimeResponse.parse_obj(
                {
                    "account_id": "readykit-replay",
                    "equipment_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "kpi_model": "insight",
                    "downtimes": [],
                }
            ),
        ]
        yield dtm_client_mock
