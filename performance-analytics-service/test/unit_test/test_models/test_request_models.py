import pydantic
import pytest

PD_V = pydantic.version.version_short()


@pytest.mark.parametrize(
    ("body", "expected_result"),
    [
        # test min_duration < 0 or max_duration < 0
        (
            {
                "filters": {"by_category": {"not": ["evaluated"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": -1,
                "min_duration": -2,
                "time": {
                    "start_time": "2021-05-18T12:00:00.000Z",
                    "end_time": "2021-05-18T20:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            "Wrong input: max_duration=-1, min_duration=-2. Value(s) can not be negative!",
        ),
        # test min_duration < 0 or max_duration > 0
        (
            {
                "filters": {"by_category": {"not": ["evaluated"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 2,
                "min_duration": -2,
                "time": {
                    "start_time": "2021-05-18T12:00:00.000Z",
                    "end_time": "2021-05-18T20:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            "Wrong input: max_duration=2, min_duration=-2. Value(s) can not be negative!",
        ),
        # test max_duration < min_duration
        (
            {
                "filters": {"by_category": {"not": ["evaluated"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 1,
                "min_duration": 2,
                "time": {
                    "start_time": "2021-05-18T12:00:00.000Z",
                    "end_time": "2021-05-18T20:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            "Wrong input: max_duration=1 < min_duration=2",
        ),
    ],
)
def test_request_model_runs_validation_for_durations(body, expected_result):
    # Arrange
    from models.request_models import Body

    # Act
    with pytest.raises(pydantic.ValidationError) as exception:
        Body(**body)

    # Assert
    assert exception.typename == "ValidationError"
    assert str(exception.value).find(expected_result) > 0


@pytest.mark.parametrize(
    ("body", "expected_start_time", "expected_end_time"),
    [
        # test that start and end times are converted from ISO 8601 shaped string
        # to an epoch milliseconds string.
        (
            {
                "filters": {"by_category": {"not": ["evaluated"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 5,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-05-18T12:00:00.000Z",
                    "end_time": "2021-05-18T20:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            "1621339200000",
            "1621368000000",
        )
    ],
)
def test_request_model_runs_validation_for_time(body, expected_start_time, expected_end_time):
    # Arrange
    from models.request_models import Body

    # Act
    model = Body(**body)

    # Assert
    assert (model.time.start_time, model.time.end_time) == (expected_start_time, expected_end_time)


@pytest.mark.parametrize(
    ("body", "expected_result"),
    [
        # test that pydantic will raise a ValidationError if the payload is missing a mandatory key.
        (
            {
                # missing filters
                "language": "en",
                # missing kpi_model
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 5,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-05-18T12:00:00.000Z",
                    "end_time": "2021-05-18T20:00:00.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [
                {
                    "input": {
                        "language": "en",
                        "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                        "max_duration": 5,
                        "min_duration": 0,
                        "time": {
                            "end_time": "2021-05-18T20:00:00.000Z",
                            "start_time": "2021-05-18T12:00:00.000Z",
                            "timezone": "Europe/Berlin",
                        },
                    },
                    "loc": ["filters"],
                    "msg": "Field required",
                    "type": "missing",
                    "url": f"https://errors.pydantic.dev/{PD_V}/v/missing",
                },
                {
                    "input": {
                        "language": "en",
                        "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                        "max_duration": 5,
                        "min_duration": 0,
                        "time": {
                            "end_time": "2021-05-18T20:00:00.000Z",
                            "start_time": "2021-05-18T12:00:00.000Z",
                            "timezone": "Europe/Berlin",
                        },
                    },
                    "loc": ["kpi_model"],
                    "msg": "Field required",
                    "type": "missing",
                    "url": f"https://errors.pydantic.dev/{PD_V}/v/missing",
                },
            ],
        )
    ],
)
def test_pydantic_raises_validation_error_if_mandatory_key_not_present(body, expected_result):
    # Arrange
    import json

    from models.request_models import Body

    # Act
    with pytest.raises(pydantic.ValidationError) as exception:
        Body(**body)

    # Assert
    exception_message = json.loads(exception.value.json())
    assert exception_message == expected_result
