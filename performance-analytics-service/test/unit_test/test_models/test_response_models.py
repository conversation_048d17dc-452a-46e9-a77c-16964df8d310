import pydantic
import pytest

from models.response_models import (
    FMGroupedByMachines,
    MachineDetailsResponse,
    OverviewResponse,
    StoppageAnalysis,
)

PD_V = pydantic.version.version_short()


@pytest.mark.parametrize(
    ("data", "expected_result"),
    [
        # test that MachineDetailsResponse model will raise a ValidationError
        # if the response is missing a mandatory key.
        (
            {
                # misses failure_mode key
                "failure_mode_details": []
            },
            [
                {
                    "input": {"failure_mode_details": []},
                    "loc": ["failure_mode"],
                    "msg": "Field required",
                    "type": "missing",
                    "url": f"https://errors.pydantic.dev/{PD_V}/v/missing",
                }
            ],
        )
    ],
)
def test_machine_details_response_raises_validation_error_if_mandatory_key_not_present(data, expected_result):
    # Arrange
    import json

    # Act
    with pytest.raises(pydantic.ValidationError) as exception:
        MachineDetailsResponse(**data)

    # Assert
    exception_message = json.loads(exception.value.json())
    assert exception_message == expected_result


@pytest.mark.parametrize(
    ("data", "expected_result"),
    [
        # test that MachineDetailsResponse model will fill in default properties if missing.
        (
            {
                "failure_mode": "4152.364",
                # missing failure_mode_details. should default to empty list
            },
            MachineDetailsResponse(failure_mode="4152.364", failure_mode_details=[]),
        )
    ],
)
def test_machine_details_adds_default_properties_if_missing(data, expected_result):
    # Act
    response = MachineDetailsResponse(**data)

    # Assert
    assert response == expected_result


@pytest.mark.parametrize(
    ("data", "expected_result"),
    [
        # test that OverviewResponse model will raise a ValidationError if the response is missing a mandatory key.
        (
            {
                "stoppage_analysis": {
                    # missing quantity
                    "duration": {"machines": {}}
                }
            },
            [
                {
                    "input": {"duration": {"machines": {}}},
                    "loc": ["stoppage_analysis", "quantity"],
                    "msg": "Field required",
                    "type": "missing",
                    "url": f"https://errors.pydantic.dev/{PD_V}/v/missing",
                }
            ],
        )
    ],
)
def test_overview_response_raises_validation_error_if_mandatory_key_not_present(data, expected_result):
    # Arrange
    import json

    # Act
    with pytest.raises(pydantic.ValidationError) as exception:
        OverviewResponse(**data)

    # Assert
    exception_message = json.loads(exception.value.json())
    assert exception_message == expected_result


@pytest.mark.parametrize(
    ("data", "expected_result"),
    [
        # test that OverviewResponse model will fill in default properties if missing.
        (
            {
                "stoppage_analysis": {
                    "quantity": {
                        # machines missing
                    },
                    "duration": {
                        # machines missing
                    },
                }
            },
            OverviewResponse(
                stoppage_analysis=StoppageAnalysis(quantity=FMGroupedByMachines(), duration=FMGroupedByMachines())
            ),
        )
    ],
)
def test_overview_adds_default_properties_if_missing(data, expected_result):
    # Act
    response = OverviewResponse(**data)

    # Assert
    assert response == expected_result
