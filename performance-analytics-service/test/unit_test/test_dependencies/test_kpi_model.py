from lib_cloud_sdk.util.file_io import read_json_file
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelType

GET_VALIDATED_MODEL_INPUT = {
    "account": "readykit-replay",
    "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
    "kpi_model_id": "din_8743",
    "start_time": *************,
    "end_time": *************,
}


def test_get_validated_kpi_model_returns_correct_model_id_for_specified_timerange(dynamo_table_mocks):
    """
    Test that the get_validated_kpi_model function returns the correct KPI model ID for the specified time range.
    """

    from dependencies.kpi_model import get_validated_kpi_model

    # Read test data from a JSON file
    performance_kpi_model_items = read_json_file("test/integration_test/res/input_data/performance_kpi_model.json")
    performance_kpi_model_items += read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("insight") + read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model_name_status_config.json"
    ).get("din_8743")

    # 'Store' data into the respective DynamoDB table
    performance_kpi_model = dynamo_table_mocks.get("performance_kpi_model")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(TableName="performance-kpi-model", Item=performance_kpi_model_item)
    expected_model_id = KpiModel(kpi_model_id="din_8743", kpi_model_type=KpiModelType.GLOBAL)
    result = get_validated_kpi_model(**GET_VALIDATED_MODEL_INPUT)
    assert result == expected_model_id
