from message_texts_query.unified.model.unified_message import UnifiedMessage
import pytest
import pytz

DAY_1_IN_MS = 1634853600000  # Fri Oct 22 2021 00:00:00 GMT+0200
DAY_1_KEY = str(DAY_1_IN_MS)
OFFSET_24_HOURS_IN_MS = 86400000

DAY_WITH_25_HOURS = 1729980000000  #  Sun Oct 27 2024 00:00:00 GMT+0200 (Day with 25 hours)
DAY_WITH_23_HOURS = 1711839600000  #  Sun Mar 31 2024 00:00:00 GMT+0100 (Day with 23 hours)


@pytest.mark.parametrize(
    ("downtimes_input", "time_range", "expected_result"),
    [
        # group dmm messages by failure mode. no custom failure modes in test data.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 20_000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4182",
                        "messageNr": "366",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Lack of containers in infeed section",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 20_000,
                    "ws_cur_state_code": 8,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4182",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 30_000,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 40_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
            ],
            [{"time_from": DAY_1_IN_MS, "time_to": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS}],
            (
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {DAY_1_KEY: {"value": 2}},
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageType": "dmm",
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {DAY_1_KEY: {"value": 1}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {DAY_1_KEY: {"value": 30000}},
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageType": "dmm",
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {DAY_1_KEY: {"value": 10000}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
            ),
        ),
        # group dmm messages by failure mode. no custom failure modes in test data. timerange spans multiple days.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 20_000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4182",
                        "messageNr": "366",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Lack of containers in infeed section",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 20_000,
                    "ws_cur_state_code": 8,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4182",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 30_000,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 40_000,
                    "duration": OFFSET_24_HOURS_IN_MS + 10_000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
            ],
            [{"time_from": DAY_1_IN_MS, "time_to": DAY_1_IN_MS + 2 * OFFSET_24_HOURS_IN_MS}],
            (
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {DAY_1_KEY: {"value": 2}, str(DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS): {"value": 1}},
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageType": "dmm",
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {DAY_1_KEY: {"value": 1}, str(DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS): {"value": 0}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {
                                DAY_1_KEY: {"value": OFFSET_24_HOURS_IN_MS - 10_000},
                                str(DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS): {"value": 40_000},
                            },
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageType": "dmm",
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {
                                DAY_1_KEY: {"value": 10000},
                                str(DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS): {"value": 0},
                            },
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
            ),
        ),
        # group dmm messages by failure mode. custom failure modes in test data.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 20_000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "Electrical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4182",
                        "messageNr": "366",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Lack of containers in infeed section",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 20_000,
                    "ws_cur_state_code": 8,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4182",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": 4184,
                        "messageNr": 368,
                        "equipmentId": "eq_id",
                        "messageType": "dmm",
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 30_000,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 40_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "Electrical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
            ],
            [{"time_from": DAY_1_IN_MS, "time_to": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS}],
            (
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {DAY_1_KEY: {"value": 2}},
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageType": "dmm",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {DAY_1_KEY: {"value": 1}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
                {
                    "failure_modes": {
                        "4184.368": {
                            "days": {DAY_1_KEY: {"value": 30000}},
                            "failure_info": "Container back-up at the discharge",
                            "message": {
                                "configId": 4184,
                                "messageNr": 368,
                                "equipmentId": "eq_id",
                                "messageType": "dmm",
                                "messageText": "Container back-up at the discharge",
                            },
                        },
                        "4182.366": {
                            "days": {DAY_1_KEY: {"value": 10000}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "4182",
                                "messageNr": "366",
                                "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                                "messageClass": "Hint",
                                "messageType": "dmm",
                                "lastChange": 1608038681000,
                                "messageText": "Lack of containers in infeed section",
                            },
                        },
                    }
                },
            ),
        ),
        # group zait messages by failure mode. no custom failure modes in test data.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "MMA_Msg_0364_Txt_17",
                        "messageNr": 364,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Container stop has been closed by Container conveyor",
                        "description": "Production is continued automatically",
                        "messageConfig": {"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        "keyword": "Test",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 20_000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "MMA_366",
                        "messageNr": 366,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Lack of containers in infeed section",
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "messageConfig": {"subsystem": "MMA"},
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 20_000,
                    "ws_cur_state_code": 8,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.366",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "MMA_Msg_0364_Txt_17",
                        "messageNr": 364,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Container stop has been closed by Container conveyor",
                        "description": "Production is continued automatically",
                        "messageConfig": {"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        "keyword": "Test",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 30_000,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 40_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
            ],
            [{"time_from": DAY_1_IN_MS, "time_to": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS}],
            (
                {
                    "failure_modes": {
                        "MMA.364": {
                            "days": {DAY_1_KEY: {"value": 2}},
                            "failure_info": "Container stop has been closed by Container conveyor",
                            "message": {
                                "configId": "MMA_Msg_0364_Txt_17",
                                "messageType": "zait",
                                "messageNr": 364,
                                "equipmentId": "eq_id",
                                "description": "Production is continued automatically",
                                "messageText": "Container stop has been closed by Container conveyor",
                                "messageConfig": {
                                    "subsystem": "MMA",
                                    "keywordId": "Msg_0364_Txt_17",
                                },
                                "keyword": "Test",
                            },
                        },
                        "MMA.366": {
                            "days": {DAY_1_KEY: {"value": 1}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "MMA_366",
                                "messageNr": 366,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Lack of containers in infeed section",
                                "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                                "messageConfig": {"subsystem": "MMA"},
                            },
                        },
                    }
                },
                {
                    "failure_modes": {
                        "MMA.364": {
                            "days": {DAY_1_KEY: {"value": 30000}},
                            "failure_info": "Container stop has been closed by Container conveyor",
                            "message": {
                                "configId": "MMA_Msg_0364_Txt_17",
                                "messageType": "zait",
                                "messageNr": 364,
                                "equipmentId": "eq_id",
                                "description": "Production is continued automatically",
                                "messageText": "Container stop has been closed by Container conveyor",
                                "messageConfig": {
                                    "subsystem": "MMA",
                                    "keywordId": "Msg_0364_Txt_17",
                                },
                                "keyword": "Test",
                            },
                        },
                        "MMA.366": {
                            "days": {DAY_1_KEY: {"value": 10000}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "MMA_366",
                                "messageNr": 366,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Lack of containers in infeed section",
                                "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                                "messageConfig": {"subsystem": "MMA"},
                            },
                        },
                    }
                },
            ),
        ),
        # group zait messages by failure mode. custom failure modes in test data.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "MMA_Msg_0364_Txt_17",
                        "messageNr": 364,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Container stop has been closed by Container conveyor",
                        "description": "Production is continued automatically",
                        "messageConfig": {"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        "keyword": "Test",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 20_000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "Mechanical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "MMA_366",
                        "messageNr": 366,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Lack of containers in infeed section",
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "messageConfig": {"subsystem": "MMA"},
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 20_000,
                    "ws_cur_state_code": 8,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.366",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "MMA_Msg_0364_Txt_17",
                        "messageNr": 364,
                        "equipmentId": "eq_id",
                        "messageType": "zait",
                        "messageText": "Container stop has been closed by Container conveyor",
                        "description": "Production is continued automatically",
                        "messageConfig": {"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        "keyword": "Test",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": DAY_1_IN_MS + 30_000,
                    "ws_cur_state_code": 16,
                    "end": DAY_1_IN_MS + 40_000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "Mechanical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
            ],
            [{"time_from": DAY_1_IN_MS, "time_to": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS}],
            (
                {
                    "failure_modes": {
                        "Mechanical failures": {
                            "days": {DAY_1_KEY: {"value": 2}},
                            "failure_info": "Container stop has been closed by Container conveyor",
                            "message": {
                                "configId": "MMA_Msg_0364_Txt_17",
                                "messageNr": 364,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Container stop has been closed by Container conveyor",
                                "description": "Production is continued automatically",
                                "messageConfig": {
                                    "subsystem": "MMA",
                                    "keywordId": "Msg_0364_Txt_17",
                                },
                                "keyword": "Test",
                            },
                        },
                        "MMA.366": {
                            "days": {DAY_1_KEY: {"value": 1}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "MMA_366",
                                "messageNr": 366,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Lack of containers in infeed section",
                                "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                                "messageConfig": {"subsystem": "MMA"},
                            },
                        },
                    }
                },
                {
                    "failure_modes": {
                        "Mechanical failures": {
                            "days": {DAY_1_KEY: {"value": 30000}},
                            "failure_info": "Container stop has been closed by Container conveyor",
                            "message": {
                                "configId": "MMA_Msg_0364_Txt_17",
                                "messageNr": 364,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Container stop has been closed by Container conveyor",
                                "description": "Production is continued automatically",
                                "messageConfig": {
                                    "subsystem": "MMA",
                                    "keywordId": "Msg_0364_Txt_17",
                                },
                                "keyword": "Test",
                            },
                        },
                        "MMA.366": {
                            "days": {DAY_1_KEY: {"value": 10000}},
                            "failure_info": "Lack of containers in infeed section",
                            "message": {
                                "configId": "MMA_366",
                                "messageNr": 366,
                                "equipmentId": "eq_id",
                                "messageType": "zait",
                                "messageText": "Lack of containers in infeed section",
                                "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                                "messageConfig": {"subsystem": "MMA"},
                            },
                        },
                    }
                },
            ),
        ),
    ],
)
def test_group_display_messages_by_day(downtimes_input, time_range, expected_result):
    # Arrange
    from common.utilities import group_display_messages_by_day

    start_time = time_range[0]["time_from"]
    end_time = time_range[0]["time_to"]
    timezone = pytz.timezone("Europe/Berlin")

    # Act
    result = group_display_messages_by_day(downtimes_input, start_time, end_time, timezone)

    # Assert
    assert result == expected_result


@pytest.mark.parametrize(
    ("downtimes_input", "failure_mode", "expected_result"),
    [
        # check for matching failure modes. test data has dmm messages and no custom failure modes.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 16,
                    "end": 1634860820000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "id": 4182,
                        "messageNr": 366,
                        "info": "Lack of containers in infeed section",
                        "description": "",
                        "keyword": "",
                        "keyword_id": "",
                        "subsystem": "",
                        "subsystem_nr": "",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860820000,
                    "ws_cur_state_code": 8,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4182",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860830000,
                    "ws_cur_state_code": 16,
                    "end": 1634860840000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4184",
                },
            ],
            "4184.368",
            [
                {
                    "duration": 20000,
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:00.000Z",
                    "end": "2021-10-22T00:00:20.000Z",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "downtimes": [],
                },
                {
                    "duration": 10000,
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:30.000Z",
                    "end": "2021-10-22T00:00:40.000Z",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "downtimes": [],
                },
            ],
        ),
        # check for matching failure modes. test data has dmm messages and custom failure modes.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 16,
                    "end": 1634860820000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "Electrical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "id": 4182,
                        "messageNr": 366,
                        "info": "Lack of containers in infeed section",
                        "description": "",
                        "keyword": "",
                        "keyword_id": "",
                        "subsystem": "",
                        "subsystem_nr": "",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860820000,
                    "ws_cur_state_code": 8,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "4182",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860830000,
                    "ws_cur_state_code": 16,
                    "end": 1634860840000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "Electrical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
            ],
            "Electrical failures",
            [
                {
                    "duration": 20000,
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:00.000Z",
                    "end": "2021-10-22T00:00:20.000Z",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "downtimes": [],
                },
                {
                    "duration": 10000,
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:30.000Z",
                    "end": "2021-10-22T00:00:40.000Z",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "downtimes": [],
                },
            ],
        ),
        # check for matching failure modes. test data has zait messages and no custom failure modes.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": UnifiedMessage(
                        configId="MMA_Msg_0364_Txt_17",
                        messageNr=364,
                        equipmentId="eq_id",
                        messageType="zait",
                        messageText="Container stop has been closed by Container conveyor",
                        description="Production is continued automatically",
                        messageConfig={"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        keyword="Test",
                    ).dict(),
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 16,
                    "end": 1634860820000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "id": 366,
                        "info": "Lack of containers in infeed section",
                        "keyword": "",
                        "keyword_id": "~Msg_Txt_Empty",
                        "messageNr": 366,
                        "subsystem": "MMA",
                        "subsystem_nr": "2000",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860820000,
                    "ws_cur_state_code": 8,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.366",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": UnifiedMessage(
                        configId="MMA_Msg_0364_Txt_17",
                        messageNr=364,
                        equipmentId="eq_id",
                        messageType="zait",
                        messageText="Container stop has been closed by Container conveyor",
                        description="Production is continued automatically",
                        messageConfig={"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        keyword="Test",
                    ).dict(),
                    "mode": 8,
                    "program": 1,
                    "start": 1634860830000,
                    "ws_cur_state_code": 16,
                    "end": 1634860840000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
            ],
            "MMA.366",
            [
                {
                    "duration": 10000,
                    "classification": "lack",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:20.000Z",
                    "end": "2021-10-22T00:00:30.000Z",
                    "message": {
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "id": 366,
                        "info": "Lack of containers in infeed section",
                        "keyword": "",
                        "keyword_id": "~Msg_Txt_Empty",
                        "messageNr": 366,
                        "subsystem": "MMA",
                        "subsystem_nr": "2000",
                    },
                    "downtimes": [],
                }
            ],
        ),
        # check for matching failure modes. test data has zait messages and custom failure modes.
        (
            [
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": UnifiedMessage(
                        configId="MMA_Msg_0364_Txt_17",
                        messageNr=364,
                        equipmentId="eq_id",
                        messageType="zait",
                        messageText="Container stop has been closed by Container conveyor",
                        description="Production is continued automatically",
                        messageConfig={"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        keyword="Test",
                    ).dict(),
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 16,
                    "end": 1634860820000,
                    "duration": 20000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "id": 366,
                        "info": "Lack of containers in infeed section",
                        "keyword": "",
                        "keyword_id": "~Msg_Txt_Empty",
                        "messageNr": 366,
                        "subsystem": "MMA",
                        "subsystem_nr": "2000",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860820000,
                    "ws_cur_state_code": 8,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "Mechanical failures",
                    "custom_failure_mode_id": "59fb228f-6f9d-4e98-a824-5bb75a2fa7b1",
                },
                {
                    "classification": "tailback",
                    "mds_state_code": 16,
                    "message": UnifiedMessage(
                        configId="MMA_Msg_0364_Txt_17",
                        messageNr=364,
                        equipmentId="eq_id",
                        messageType="zait",
                        messageText="Container stop has been closed by Container conveyor",
                        description="Production is continued automatically",
                        messageConfig={"subsystem": "MMA", "keywordId": "Msg_0364_Txt_17"},
                        keyword="Test",
                    ).dict(),
                    "mode": 8,
                    "program": 1,
                    "start": 1634860830000,
                    "ws_cur_state_code": 16,
                    "end": 1634860840000,
                    "duration": 10000,
                    "category": "external_failure",
                    "failure_mode": "MMA.364",
                },
            ],
            "Mechanical failures",
            [
                {
                    "duration": 10000,
                    "classification": "lack",
                    "category": "external_failure",
                    "start": "2021-10-22T00:00:20.000Z",
                    "end": "2021-10-22T00:00:30.000Z",
                    "message": {
                        "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF",
                        "id": 366,
                        "info": "Lack of containers in infeed section",
                        "keyword": "",
                        "keyword_id": "~Msg_Txt_Empty",
                        "messageNr": 366,
                        "subsystem": "MMA",
                        "subsystem_nr": "2000",
                    },
                    "downtimes": [],
                }
            ],
        ),
    ],
)
def test_check_for_matching_failure_modes(downtimes_input, failure_mode, expected_result):
    # Arrange
    from common.utilities import check_for_matching_failure_modes

    # Act
    result = list(check_for_matching_failure_modes(downtimes_input, failure_mode))

    # Assert
    assert result == expected_result


DOWNTIME = {
    "classification": "tailback",
    "category": "external_failure",
    "start": 1634875299142,
    "end": 1634875432351,
    "duration": 133209,
    "message": {
        "configId": "4184",
        "messageNr": "368",
        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
        "messageClass": "Hint",
        "messageType": "dmm",
        "lastChange": 1608038681000,
        "messageText": "Container back-up at the discharge",
    },
    "lastUpdateAt": 1652854206252,
}


@pytest.mark.parametrize(
    ("start", "end", "split_times", "expected"),
    [
        pytest.param(
            DAY_1_IN_MS + 10_000,  # start
            DAY_1_IN_MS + 30_000,  # end
            [],  # split_times
            [  # expected
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + 10_000,
                    "end": DAY_1_IN_MS + 30_000,
                    "duration": 20_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                }
            ],
            id="downtime is not split if no split times are provided",
        ),
        pytest.param(
            DAY_1_IN_MS + 10_000,  # start
            DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,  # end
            [DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS],  # split_times
            [  # expected
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + 10_000,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
                    "duration": OFFSET_24_HOURS_IN_MS - 10_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 1,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,
                    "duration": 30_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
            ],
            id="downtime is split if it crosses the split_time",
        ),
        pytest.param(
            DAY_1_IN_MS + 10_000,  # start
            DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,  # end
            [DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS, DAY_1_IN_MS + (2 * OFFSET_24_HOURS_IN_MS)],  # split_times
            [  # expected
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + 10_000,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
                    "duration": OFFSET_24_HOURS_IN_MS - 10_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 1,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,
                    "duration": 30_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
            ],
            id="if more split_times are provided than needed, they get ignored",
        ),
        pytest.param(
            DAY_1_IN_MS + 10_000,  # start
            DAY_1_IN_MS + (2 * OFFSET_24_HOURS_IN_MS) + 30_000,  # end
            [DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS],  # split_times
            [  # expected
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + 10_000,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
                    "duration": OFFSET_24_HOURS_IN_MS - 10_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 1,
                    "end": DAY_1_IN_MS + (2 * OFFSET_24_HOURS_IN_MS) + 30_000,
                    "duration": OFFSET_24_HOURS_IN_MS + 30_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
            ],
            id="the downtime_rest after the last split_time can have any duration",
        ),
        pytest.param(
            DAY_1_IN_MS + 10_000,  # start
            DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,  # end
            [DAY_1_IN_MS, DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS],  # split_times
            [  # expected
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + 10_000,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
                    "duration": OFFSET_24_HOURS_IN_MS - 10_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 1,
                    "end": DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS + 30_000,
                    "duration": 30_000,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": 1608038681000,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": 1652854206252,
                },
            ],
            id="downtime starts after first split time",
        ),
    ],
)
def test_split_downtime_by_day(start, end, split_times, expected):
    from common.utilities import split_downtime_by_day
    # Arrange

    downtime = DOWNTIME
    downtime["start"] = start
    downtime["end"] = end
    downtime["duration"] = end - start

    # Act
    result = split_downtime_by_day(downtime, split_times)

    # Assert
    assert result == expected


@pytest.mark.parametrize(
    ("utc_timestamp", "timezone", "expected_result"),
    [
        (
            1634860800000,  # UTC timestamp for 2021-10-22 00:00:00 UTC
            pytz.timezone("America/New_York"),  # Timezone: UTC-4
            24 * 3600 * 1000,  # 24 hours in milliseconds
        ),
        (
            1634860800000,  # UTC timestamp for 2021-10-22 00:00:00 UTC
            pytz.timezone("Europe/Berlin"),  # Timezone: UTC+1
            24 * 3600 * 1000,  # 24 hours in milliseconds
        ),
        (
            1711839600000,  # UTC timestamp for 2024-03-31 00:00:00 GMT+0100 (DST change in Europe)
            pytz.timezone("Europe/Berlin"),  # Timezone: UTC+1
            23 * 3600 * 1000,  # 23 hours in milliseconds (DST change)
        ),
        (
            1729980000000,  # UTC timestamp for 2024-10-27 00:00:00 GMT+0200 (DST change in Europe)
            pytz.timezone("Europe/Berlin"),  # Timezone: UTC+2
            25 * 3600 * 1000,  # 25 hours in milliseconds (DST change)
        ),
    ],
)
def test_milliseconds_in_day(utc_timestamp, timezone, expected_result):
    from common.utilities import milliseconds_in_day

    result = milliseconds_in_day(utc_timestamp, timezone)

    assert result == expected_result


@pytest.mark.parametrize(
    ("start_time", "end_time", "timezone", "expected_result"),
    [
        pytest.param(
            DAY_1_IN_MS,
            DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
            pytz.timezone("America/New_York"),
            [],
            id="24 hour day, no split times, tz=America/New_York",
        ),
        pytest.param(
            DAY_1_IN_MS,
            DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS,
            pytz.timezone("Europe/Berlin"),
            [],
            id="24 hour day, no split times, tz=Europe/Berlin",
        ),
        pytest.param(
            DAY_WITH_25_HOURS,
            DAY_WITH_25_HOURS + 25 * 3600 * 1000,
            pytz.timezone("Europe/Berlin"),
            [],
            id="25 hour day, no split times, tz=Europe/Berlin",
        ),
        pytest.param(
            DAY_WITH_23_HOURS,
            DAY_WITH_23_HOURS + 23 * 3600 * 1000,
            pytz.timezone("Europe/Berlin"),
            [],
            id="23 hour day, no split times, tz=Europe/Berlin",
        ),
        pytest.param(
            DAY_1_IN_MS - OFFSET_24_HOURS_IN_MS,
            DAY_1_IN_MS + 2 * OFFSET_24_HOURS_IN_MS,
            pytz.timezone("Europe/Berlin"),
            [DAY_1_IN_MS, DAY_1_IN_MS + OFFSET_24_HOURS_IN_MS],
            id="24h day -> 24h day -> 24h day, 2 split times, tz=Europe/Berlin",
        ),
        pytest.param(
            DAY_WITH_25_HOURS - OFFSET_24_HOURS_IN_MS,
            DAY_WITH_25_HOURS + 25 * 3600 * 1000 + OFFSET_24_HOURS_IN_MS,
            pytz.timezone("Europe/Berlin"),
            [DAY_WITH_25_HOURS, DAY_WITH_25_HOURS + 25 * 3600 * 1000],
            id="24h day -> 25h day -> 24h day, 2 split times, tz=Europe/Berlin",
        ),
        pytest.param(
            DAY_WITH_23_HOURS - OFFSET_24_HOURS_IN_MS,
            DAY_WITH_23_HOURS + 23 * 3600 * 1000 + OFFSET_24_HOURS_IN_MS,
            pytz.timezone("Europe/Berlin"),
            [DAY_WITH_23_HOURS, DAY_WITH_23_HOURS + 23 * 3600 * 1000],
            id="24h day -> 23h day -> 24h day, 2 split times, tz=Europe/Berlin",
        ),
    ],
)
def test_get_split_times(start_time, end_time, timezone, expected_result):
    from common.utilities import get_split_times

    result = get_split_times(start_time, end_time, timezone)

    assert result == expected_result
