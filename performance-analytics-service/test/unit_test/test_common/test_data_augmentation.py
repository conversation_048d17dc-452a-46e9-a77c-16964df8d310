import json

from lib_cloud_sdk.util.file_io import read_json_file
import pytest

from models.request_models import Body


@pytest.mark.parametrize(
    ("machine_id", "document", "account", "kpi_model", "body", "touch_messages", "failure_modes", "expected_result"),
    [
        # test that downtimes are augmented.  no filters applied.
        # failure mode key is added and touch messages are resolved for each downtime.
        (
            "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
            {
                "time_from": *************,
                "time_to": *************,
                "downtimes": [
                    {
                        "classification": "tailback",
                        "category": "external_failure",
                        "start": *************,
                        "uncutStart": *************,
                        "end": *************,
                        "duration": 1062215,
                        "message": {
                            "configId": "4184",
                            "messageNr": "368",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Container back-up at the discharge",
                        },
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "lastUpdateAt": *************,
                    },
                    {
                        "classification": "lack",
                        "category": "external_failure",
                        "start": *************,
                        "end": *************,
                        "duration": 29038,
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "message": {
                            "configId": "4182",
                            "messageNr": "366",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Lack of containers in infeed section",
                        },
                        "lastUpdateAt": 1652264053202,
                    },
                ],
                "preclassified_states": [],
                "document_summary": [{"time_from": *************, "time_to": *************}],
                "customer": "readykit-replay",
                "eq_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "product_type": [],
            },
            "readykit-replay",
            "din_8743",
            {
                "filters": {"by_category": {}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-05-18T00:00:00Z",
                    "end_time": "2021-05-18T14:00:00Z",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
            [
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": *************,
                    "uncutStart": *************,
                    "end": *************,
                    "duration": 1062215,
                    "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "lastUpdateAt": *************,
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container back-up at the discharge",
                    },
                    "failure_mode": "4184",
                },
                {
                    "classification": "lack",
                    "category": "external_failure",
                    "start": *************,
                    "end": *************,
                    "duration": 29038,
                    "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "message": {
                        "configId": "4182",
                        "messageNr": "366",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Lack of containers in infeed section",
                    },
                    "lastUpdateAt": 1652264053202,
                    "failure_mode": "4182",
                },
            ],
        ),
        # test that downtimes are augmented.  filter by external_failure -> tailback.
        # failure mode key is added and touch messages are resolved for each downtime.
        (
            "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
            {
                "time_from": *************,
                "time_to": *************,
                "downtimes": [
                    {
                        "classification": "tailback",
                        "category": "external_failure",
                        "start": *************,
                        "uncutStart": *************,
                        "end": *************,
                        "duration": 1062215,
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "message": {
                            "configId": "4184",
                            "messageNr": "368",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Container back-up at the discharge",
                        },
                        "lastUpdateAt": *************,
                    },
                    {
                        "classification": "lack",
                        "category": "external_failure",
                        "start": *************,
                        "end": *************,
                        "duration": 29038,
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "message": {
                            "configId": "4182",
                            "messageNr": "366",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Lack of containers in infeed section",
                        },
                        "lastUpdateAt": 1652264053202,
                    },
                ],
                "preclassified_states": [],
                "document_summary": [{"time_from": *************, "time_to": *************}],
                "customer": "readykit-replay",
                "eq_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "product_type": [],
            },
            "readykit-replay",
            "din_8743",
            {
                "filters": {"by_category": {"external_failure": ["tailback"]}},
                "language": "en",
                "limit": 10,
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-05-18T00:00:00Z",
                    "end_time": "2021-05-18T14:00:00Z",
                    "timezone": "Europe/Berlin",
                },
            },
            read_json_file("test/integration_test/res/input_data/messages_dmm.json"),
            read_json_file("test/integration_test/res/input_data/failure_modes.json").get("no_cfm_specified"),
            [
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": *************,
                    "uncutStart": *************,
                    "end": *************,
                    "duration": 1062215,
                    "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": *************,
                    "failure_mode": "4184",
                },
            ],
        ),
    ],
)
def test_preprocess_downtimes(
    machine_id,
    document,
    account,
    kpi_model,
    body,
    touch_messages,
    failure_modes,
    expected_result,
    dynamo_table_mocks,
    s3_client,
):
    # Arrange
    from common.data_augmentation import preprocess_downtimes

    rk_failure_mode = dynamo_table_mocks.get("rk_failure_mode")
    rk_failure_mode.put_item(TableName="rk-failure-mode-table", Item=failure_modes)
    bucket_name = "rk-zait-testing"
    key_name = "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    bode_model = Body(**body)

    # Act
    result = list(preprocess_downtimes(machine_id, document, account, kpi_model, bode_model))

    # Assert
    assert result == expected_result


@pytest.mark.parametrize(
    ("document", "expected_result"),
    [
        # add the end property to each downtime.
        # calculate the duration of each downtime and add that property to the downtime.
        # handle legacy downtimes. add category property to each downtime.
        (
            {
                "time_from": *************,
                "time_to": *************,
                "downtimes": [
                    {
                        "classification": "tailback",
                        "category": "external_failure",
                        "start": *************,
                        "uncutStart": *************,
                        "end": *************,
                        "duration": 1062215,
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "message": {
                            "configId": "4184",
                            "messageNr": "368",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Container back-up at the discharge",
                        },
                        "lastUpdateAt": *************,
                    },
                    {
                        "classification": "lack",
                        "category": "external_failure",
                        "start": *************,
                        "end": *************,
                        "duration": 29038,
                        "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "message": {
                            "configId": "4182",
                            "messageNr": "366",
                            "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                            "messageClass": "Hint",
                            "messageType": "dmm",
                            "lastChange": *************,
                            "messageText": "Lack of containers in infeed section",
                        },
                        "lastUpdateAt": 1652264053202,
                    },
                ],
                "preclassified_states": [],
                "document_summary": [{"time_from": *************, "time_to": *************}],
                "customer": "readykit-replay",
                "eq_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "product_type": [],
            },
            [
                {
                    "classification": "tailback",
                    "category": "external_failure",
                    "start": *************,
                    "uncutStart": *************,
                    "end": *************,
                    "duration": 1062215,
                    "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "message": {
                        "configId": "4184",
                        "messageNr": "368",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container back-up at the discharge",
                    },
                    "lastUpdateAt": *************,
                },
                {
                    "classification": "lack",
                    "category": "external_failure",
                    "start": *************,
                    "end": *************,
                    "duration": 29038,
                    "id": "readykit-replay_insight_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                    "message": {
                        "configId": "4182",
                        "messageNr": "366",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Lack of containers in infeed section",
                    },
                    "lastUpdateAt": 1652264053202,
                },
            ],
        ),
    ],
)
def test_augment_downtimes(document, expected_result):
    # Arrange
    from common.data_augmentation import augment_downtimes

    # Act
    result = augment_downtimes(document)

    # Assert
    assert result == expected_result


@pytest.mark.parametrize(
    ("downtimes_input", "body", "expected_result"),
    [
        # filter out everything but scheduled_down_time (filter by category only)
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                    "downtimes": [
                        {
                            "classification": "lack",
                            "mds_state_code": 8,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860800000,
                            "ws_cur_state_code": 8,
                            "end": 1634860805000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                        {
                            "classification": "tailback",
                            "mds_state_code": 16,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860805000,
                            "ws_cur_state_code": 16,
                            "end": 1634860810000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                    ],
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860820000,
                    "duration": 10000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860820000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860820000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860825000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860825000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                    ],
                },
            ],
            {
                "filters": {"by_category": {"scheduled_down_time": []}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:30.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860820000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860820000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860825000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860825000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                    ],
                }
            ],
        ),
        # filter out everything but external_failures -> lack (filter by category and by classification)
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                    "downtimes": [
                        {
                            "classification": "lack",
                            "mds_state_code": 8,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860800000,
                            "ws_cur_state_code": 8,
                            "end": 1634860805000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                        {
                            "classification": "tailback",
                            "mds_state_code": 16,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860805000,
                            "ws_cur_state_code": 16,
                            "end": 1634860810000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                    ],
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860820000,
                    "duration": 10000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860820000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860820000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860825000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860825000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 5000,
                            "category": "scheduled_down_time",
                        },
                    ],
                },
            ],
            {
                "filters": {"by_category": {"external_failure": ["lack"]}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:30.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                    "downtimes": [
                        {
                            "classification": "lack",
                            "mds_state_code": 8,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860800000,
                            "ws_cur_state_code": 8,
                            "end": 1634860805000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                        {
                            "classification": "tailback",
                            "mds_state_code": 16,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860805000,
                            "ws_cur_state_code": 16,
                            "end": 1634860810000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                    ],
                }
            ],
        ),
        # wrong filter applied. should not return anything.
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {},
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                }
            ],
            {
                "filters": {"by_category": {"unknown filter": []}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:30.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [],
        ),
        # filter by duration. 0 < x <= 4 seconds
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                    "downtimes": [
                        {
                            "classification": "lack",
                            "mds_state_code": 8,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860800000,
                            "ws_cur_state_code": 8,
                            "end": 1634860805000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                        {
                            "classification": "tailback",
                            "mds_state_code": 16,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860805000,
                            "ws_cur_state_code": 16,
                            "end": 1634860810000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                    ],
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860826000,
                    "duration": 16000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860826000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 4000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860826000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860828000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860828000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                    ],
                },
            ],
            {
                "filters": {"by_category": {}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 4000,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:30.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860826000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 4000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860826000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860828000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860828000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                    ],
                }
            ],
        ),
        # filter by duration. 0 < x <= 2 seconds (should be empty because all downtimes >= 4 sec durations)
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                    "downtimes": [
                        {
                            "classification": "lack",
                            "mds_state_code": 8,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860800000,
                            "ws_cur_state_code": 8,
                            "end": 1634860805000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                        {
                            "classification": "tailback",
                            "mds_state_code": 16,
                            "mode": 8,
                            "program": 1,
                            "start": 1634860805000,
                            "ws_cur_state_code": 16,
                            "end": 1634860810000,
                            "duration": 5000,
                            "category": "external_failure",
                        },
                    ],
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860826000,
                    "duration": 16000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860826000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 4000,
                    "category": "scheduled_down_time",
                    "downtimes": [
                        {
                            "classification": "clean",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 8,
                            "start": 1634860826000,
                            "ws_cur_state_code": 16384,
                            "end": 1634860828000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                        {
                            "classification": "changeover",
                            "mds_state_code": 1024,
                            "mode": 8,
                            "program": 16,
                            "start": 1634860828000,
                            "ws_cur_state_code": 4,
                            "end": 1634860830000,
                            "duration": 2000,
                            "category": "scheduled_down_time",
                        },
                    ],
                },
            ],
            {
                "filters": {"by_category": {}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 2000,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:30.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [],
        ),
        # filter by time boundaries 00:00 >= x <= 00:20
        # (return only items that are within timerange - productive states).
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860820000,
                    "duration": 10000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860820000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "scheduled_down_time",
                },
            ],
            {
                "filters": {"by_category": {}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:00:00.000Z",
                    "end_time": "2021-10-22T00:00:20.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                }
            ],
        ),
        # filter by time boundaries 00:35 >= x <= 00:40 (should be empty because all items are within 00:00 -> 00:30).
        (
            [
                {
                    "classification": "lack",
                    "mds_state_code": 8,
                    "message": {
                        "configId": "4152",
                        "messageNr": "364",
                        "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                        "messageClass": "Hint",
                        "messageType": "dmm",
                        "lastChange": *************,
                        "messageText": "Container stop has been closed by:Inspector",
                    },
                    "mode": 8,
                    "program": 1,
                    "start": 1634860800000,
                    "ws_cur_state_code": 8,
                    "end": 1634860810000,
                    "duration": 10000,
                    "category": "external_failure",
                },
                {
                    "classification": "productive",
                    "mds_state_code": 128,
                    "mode": 8,
                    "program": 1,
                    "start": 1634860810000,
                    "ws_cur_state_code": 128,
                    "end": 1634860820000,
                    "duration": 10000,
                    "message": {"zait": {}},
                    "category": "productive",
                },
                {
                    "classification": "clean",
                    "mds_state_code": 1024,
                    "message": {},
                    "mode": 8,
                    "program": 8,
                    "start": 1634860820000,
                    "ws_cur_state_code": 16384,
                    "end": 1634860830000,
                    "duration": 10000,
                    "category": "scheduled_down_time",
                },
            ],
            {
                "filters": {"by_category": {}},
                "language": "en",
                "kpi_model": "din_8743",
                "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                "max_duration": 0,
                "min_duration": 0,
                "time": {
                    "start_time": "2021-10-22T00:35:00.000Z",
                    "end_time": "2021-10-22T00:00:40.000Z",
                    "timezone": "Europe/Berlin",
                },
            },
            [],
        ),
    ],
)
def test_filter_downtimes(downtimes_input, body, expected_result):
    # Arrange
    from common.data_augmentation import filter_downtimes

    # Act
    body = Body(**body)
    result = list(filter_downtimes(downtimes_input, body))

    # Assert
    assert result == expected_result
