openapi: 3.0.2
info:
  title: FastAPI
  version: 0.1.0
paths:
  /v1/performance-analytics/stoppage-analysis/overview:
    post:
      summary: Overview
      description: >-
        Get the duration and quantity for each failure mode grouped by shift for
        a specific machine or a list of machines.
      operationId: overview_performance_analytics_stoppage_analysis_overview_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OverviewBody'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverviewResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /v1/performance-analytics/stoppage-analysis/machine-details:
    post:
      summary: Machine Details
      description: Get all downtimes for a specific failure mode for a specific machine.
      operationId: >-
        machine_details_performance_analytics_stoppage_analysis_machine_details_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MachineDetailsBody'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineDetailsResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    ChildLevelDowntime:
      title: ChildLevelDowntime
      type: object
      properties:
        classification:
          title: Classification
          type: string
          default: undefined
        category:
          title: Category
          type: string
          default: undefined
        start:
          title: Start
          type: integer
          default: 0
        end:
          title: End
          type: integer
          default: 0
        duration:
          title: Duration
          type: integer
          default: 0
        ws_cur_state_code:
          title: Ws Cur State Code
          type: integer
          default: -1
        mode:
          title: Mode
          type: integer
          default: -1
        program:
          title: Program
          type: integer
          default: -1
        mds_state_code:
          title: Mds State Code
          type: integer
          default: -1
    FMGroupedByMachines:
      title: FMGroupedByMachines
      type: object
      properties:
        machines:
          title: Machines
          type: object
          additionalProperties:
            $ref: '#/components/schemas/FailureModes'
          default: {}
    FailureModes:
      title: FailureModes
      required:
        - failure_modes
      type: object
      properties:
        failure_modes:
          title: Failure Modes
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Shifts'
    Filters:
      title: Filters
      type: object
      properties:
        by_category:
          title: By Category
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          default: {}
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    MachineDetailsBody:
      title: MachineDetailsBody
      required:
        - filters
        - kpi_model
        - language
        - line_id
        - time
        - failure_mode
        - machine
      type: object
      properties:
        filters:
          $ref: '#/components/schemas/Filters'
        kpi_model:
          title: Kpi Model
          type: string
        language:
          title: Language
          type: string
        line_id:
          title: Line Id
          type: string
        time:
          $ref: '#/components/schemas/Time'
        min_duration:
          title: Min Duration
          type: integer
          default: 0
        max_duration:
          title: Max Duration
          type: integer
          default: 0
        failure_mode:
          title: Failure Mode
          type: string
        machine:
          title: Machine
          type: string
    MachineDetailsResponse:
      title: MachineDetailsResponse
      required:
        - failure_mode
      type: object
      properties:
        failure_mode:
          title: Failure Mode
          type: string
        failure_mode_details:
          title: Failure Mode Details
          anyOf:
            - type: array
              items:
                $ref: '#/components/schemas/TopLevelDowntime'
            - type: array
              items: {}
          default: []
    OverviewBody:
      title: OverviewBody
      required:
        - filters
        - kpi_model
        - language
        - line_id
        - time
        - limit
        - machines
        - shifts_to_filter
      type: object
      properties:
        filters:
          $ref: '#/components/schemas/Filters'
        kpi_model:
          title: Kpi Model
          type: string
        language:
          title: Language
          type: string
        line_id:
          title: Line Id
          type: string
        time:
          $ref: '#/components/schemas/Time'
        min_duration:
          title: Min Duration
          type: integer
          default: 0
        max_duration:
          title: Max Duration
          type: integer
          default: 0
        limit:
          title: Limit
          type: integer
        machines:
          title: Machines
          type: array
          items:
            type: string
        shifts_to_filter:
          title: Shifts To Filter
          type: array
          items:
            type: string
    OverviewResponse:
      title: OverviewResponse
      required:
        - stoppage_analysis
      type: object
      properties:
        stoppage_analysis:
          $ref: '#/components/schemas/StoppageAnalysis'
    Shifts:
      title: Shifts
      required:
        - failure_info
        - shifts
      type: object
      properties:
        failure_info:
          title: Failure Info
          type: string
        shifts:
          title: Shifts
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ValueForShift'
    StoppageAnalysis:
      title: StoppageAnalysis
      required:
        - quantity
        - duration
      type: object
      properties:
        quantity:
          $ref: '#/components/schemas/FMGroupedByMachines'
        duration:
          $ref: '#/components/schemas/FMGroupedByMachines'
    Time:
      title: Time
      required:
        - start_time
        - end_time
      type: object
      properties:
        start_time:
          title: Start Time
          type: string
        end_time:
          title: End Time
          type: string
    TopLevelDowntime:
      title: TopLevelDowntime
      type: object
      properties:
        classification:
          title: Classification
          type: string
          default: undefined
        category:
          title: Category
          type: string
          default: undefined
        start:
          title: Start
          type: integer
          default: 0
        end:
          title: End
          type: integer
          default: 0
        duration:
          title: Duration
          type: integer
          default: 0
        message:
          title: Message
          anyOf:
            - $ref: '#/components/schemas/UnifiedMessage'
            - type: object
          default: {}
        downtimes:
          title: Downtimes
          anyOf:
            - type: array
              items:
                $ref: '#/components/schemas/ChildLevelDowntime'
            - type: array
              items: {}
          default: []
    UnifiedMessage:
      title: UnifiedMessage
      required:
        - configId
        - messageType
      type: object
      properties:
        configId:
          title: Configid
          type: string
        messageType:
          title: Messagetype
          type: string
        messageNr:
          title: Messagenr
          type: string
        dataSourceId:
          title: Datasourceid
          type: string
        equipmentId:
          title: Equipmentid
          type: string
        messageClass:
          title: Messageclass
          type: string
        lastChange:
          title: Lastchange
          type: integer
        messageConfig:
          title: Messageconfig
          type: object
        status:
          title: Status
          type: string
        keyword:
          title: Keyword
          type: string
        messageText:
          title: Messagetext
          type: string
        description:
          title: Description
          type: string
        instruction:
          title: Instruction
          type: string
        diagnosis:
          title: Diagnosis
          type: string
        attention:
          title: Attention
          type: string
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            type: string
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
    ValueForShift:
      title: ValueForShift
      required:
        - value
      type: object
      properties:
        value:
          title: Value
          type: integer
