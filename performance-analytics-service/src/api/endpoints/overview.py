import datetime

from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, HTTPException, Request, status
from lib_dtm_client.clients.query_handler import QueryHandler
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties
import pytz

from common.data_augmentation import preprocess_downtimes
from common.utilities import group_display_messages_by_day
from dependencies.kpi_model import get_validated_kpi_model
from models.request_models import OverviewBody
from models.response_models import FMGroupedByMachines, OverviewResponse, StoppageAnalysis

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


@router.post("/overview", response_model=OverviewResponse)
@TRACER.capture_method(capture_response=False)
def overview(
    body: OverviewBody,
    request: Request,
    properties: Share2ActProperties,
) -> OverviewResponse:
    "Get duration and quantity for each failure mode grouped by day for a specific machine or a list of machines."
    LOGGER.info('Calling overview endpoint with event: "%s".', request.scope.get("aws.event"))

    try:
        if not isinstance(properties.account, str):
            raise ValueError("Account must be a string")
        if not isinstance(properties.user_id, str):
            raise ValueError("User ID must be a string")
        if not isinstance(properties.account, str):
            raise ValueError("Account must be a string")
        if not isinstance(properties.global_rights, list):
            raise ValueError("Global rights must be a list of strings")

        machines = body.machines
        timezone = pytz.timezone(body.time.timezone)
        # Start and end time should be at midnight
        start_time = (
            int(
                datetime.datetime.fromtimestamp(int(int(body.time.start_time) / 1000), timezone)
                .replace(hour=0, minute=0, second=0, microsecond=0)
                .timestamp()
            )
            * 1000
        )
        end_time = (
            int(
                datetime.datetime.fromtimestamp(int(int(body.time.end_time) / 1000), timezone)
                .replace(hour=0, minute=0, second=0, microsecond=0)
                .timestamp()
            )
            * 1000
        )
        timezone = pytz.timezone(body.time.timezone)
        kpi_model = get_validated_kpi_model(properties.account, body.line_id, body.kpi_model, start_time, end_time)
        if not kpi_model:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="kpi model not assigned to this line in the given timerange",
            )

        query_handler = QueryHandler()

        query_with_children = False
        for category_key in body.filters.by_category:
            query_with_children = query_with_children or bool(body.filters.by_category[category_key])

        downtimes_per_machine = query_handler.get_downtimes_for_machines(
            properties.account,
            machines,
            kpi_model,
            start_time,
            end_time,
            with_children=query_with_children,
            language=body.language,
        )
        LOGGER.debug("account=%s, downtimes_with_speeds=%s", properties.account, downtimes_per_machine)

        quantity_analysis, duration_analysis = {}, {}
        for machine_id, downtimes in downtimes_per_machine.items():
            processed_downtimes = preprocess_downtimes(
                machine_id, downtimes, properties.account, kpi_model.kpi_model_id, body
            )

            LOGGER.debug("downtimes for machine_id=%s: %s", machine_id, processed_downtimes)
            (
                quantity_analysis[machine_id],
                duration_analysis[machine_id],
            ) = group_display_messages_by_day(processed_downtimes, start_time, end_time, timezone)  # type: ignore[arg-type]

        LOGGER.debug("preparing response")
        response = OverviewResponse(
            stoppage_analysis=StoppageAnalysis(
                quantity=FMGroupedByMachines(machines=quantity_analysis),  # type: ignore[arg-type]
                duration=FMGroupedByMachines(machines=duration_analysis),  # type: ignore[arg-type]
            )
        )
        LOGGER.debug("response=%s", response)
        return response

    except HTTPException as http_exception:
        LOGGER.warning(
            "overview endpoint failed with HTTPException: %s, event=%s",
            http_exception.detail,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise http_exception
    except Exception as exception:
        LOGGER.warning(
            "overview endpoint failed with Exception: %s, event=%s",
            exception,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise exception
