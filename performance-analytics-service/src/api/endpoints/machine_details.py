from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, HTTPException, Request, status
from lib_dtm_client.clients.query_handler import QueryHandler
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties

from common.data_augmentation import preprocess_downtimes
from common.utilities import check_for_matching_failure_modes
from dependencies.kpi_model import get_validated_kpi_model
from models.request_models import MachineDetailsBody
from models.response_models import MachineDetailsResponse

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


@router.post("/machine-details", response_model=MachineDetailsResponse)
@TRACER.capture_method(capture_response=False)
def machine_details(
    body: MachineDetailsBody,
    request: Request,
    properties: Share2ActProperties,
) -> MachineDetailsResponse:
    """Get all downtimes for a specific failure mode for a specific machine."""
    LOGGER.info('Calling machine-details endpoint with event: "%s".', request.scope.get("aws.event"))

    try:
        if not isinstance(properties.account, str):
            raise ValueError("Account must be a string")
        if not isinstance(properties.account, str):
            raise ValueError("Account must be a string")
        machine_id = body.machine
        start_time = int(body.time.start_time)
        end_time = int(body.time.end_time)

        kpi_model = get_validated_kpi_model(properties.account, body.line_id, body.kpi_model, start_time, end_time)
        if not kpi_model:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="kpi model not assigned to this line in the given timerange",
            )

        query_handler = QueryHandler()
        document = query_handler.get_downtimes_for_machines(
            properties.account,
            [machine_id],
            kpi_model,
            start_time,
            end_time,
            with_children=True,
            language=body.language,
        ).get(machine_id)

        if document is None:
            return MachineDetailsResponse(failure_mode=body.failure_mode, failure_mode_details=[])

        downtimes = preprocess_downtimes(machine_id, document, properties.account, kpi_model.kpi_model_id, body)
        matching_downtimes = list(check_for_matching_failure_modes(downtimes, body.failure_mode))

        return MachineDetailsResponse(failure_mode=body.failure_mode, failure_mode_details=matching_downtimes)

    except HTTPException as http_exception:
        LOGGER.warning(
            "machine_details endpoint failed with HTTPException: %s, event=%s",
            http_exception.detail,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise http_exception
    except Exception as exception:
        LOGGER.warning(
            "machine_details endpoint failed with Exception: %s, event=%s",
            exception,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise exception
