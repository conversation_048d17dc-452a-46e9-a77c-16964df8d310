# Copyright (c) 2019, Syskron GmbH. All rights reserved.

from collections.abc import Iterator
from typing import Any

from aws_lambda_powertools import Logger
from lib_cloud_sdk.processing import create_augmentation_step
from lib_cloud_sdk.processing.steps import FillEndPropertyStep, MatchFailureModeStep
from lib_dtm_common.models.api_models import ResponseUnifiedMessage
from performance_analytics.fast_api.other.document_processor import DocumentProcessor

from common.constants import (
    CATEGORY,
    CLASSIFICATION,
    LOSS_CATEGORY,
    MAPPED_OP_MODE,
    PRODUCTIVE,
)
from models.request_models import MachineDetailsBody, OverviewBody

LOGGER = Logger()


def preprocess_downtimes(
    machine_id: str,
    document: dict[str, Any],
    account: str,
    kpi_model: str,
    body: OverviewBody | MachineDetailsBody,
) -> Iterator[dict[str, Any]]:
    """Preprocessing downtimes.

    Augment the downtimes.
    Merge downtimes by category.
    Add the failure mode key and resolve touch messages for each downtime.
    """
    LOGGER.debug(
        "prepocess downtimes: machine_id=%s, account=%s, kpi_model=%s",
        machine_id,
        account,
        kpi_model,
    )
    augmented_downtimes = augment_downtimes(document)
    filtered_downtimes = filter_downtimes(augmented_downtimes, body)
    processor = DocumentProcessor()
    downtimes = processor.preprocess_data(
        [
            MatchFailureModeStep(),
        ],
        filtered_downtimes,  # type: ignore[arg-type]
    )
    return downtimes  # type: ignore[return-value]


def augment_downtimes(document: dict[str, Any]) -> list[dict[str, Any]]:
    """Augment downtimes.

    Add the end property to each downtime.
    Calculate the duration of each downtime and add that property to the downtime.
    """
    LOGGER.debug("Calling augment_data()")
    downtimes = document.get("downtimes", [])

    # fix messages
    for downtime in downtimes:
        if downtime.get("message"):
            downtime["message"] = ResponseUnifiedMessage(**downtime.get("message")).model_dump(
                by_alias=True, exclude_none=True
            )

    processor = DocumentProcessor()
    augmented_downtimes = processor.preprocess_data(
        [
            FillEndPropertyStep(document["time_to"]),
        ],
        downtimes,
    )
    augmented_downtime_list = list(augmented_downtimes)
    return augmented_downtime_list  # type: ignore[return-value]


def filter_downtimes(
    downtimes: list[dict[str, Any]], body: OverviewBody | MachineDetailsBody
) -> Iterator[dict[str, Any]]:
    """Filter downtimes.

    Filter downtimes by duration.
    Filter downtimes by category and/or classification.
    Filter downtimes by timerange (start and end times).
    Each one of these steps is applied sequentially.
    """
    start_time = int(body.time.start_time)
    end_time = int(body.time.end_time)

    def _check_duration(
        item: dict[str, Any],
        before: dict[str, Any],  # noqa: ARG001
        after: dict[str, Any],  # noqa: ARG001
    ) -> dict[str, Any] | None:
        # Handle "empty" duration when duration == 0
        if body.max_duration == 0 and body.min_duration == 0:
            return item
        if body.max_duration == 0:
            return item if item["duration"] > body.min_duration else None
        if body.min_duration == 0:
            return item if item["duration"] <= body.max_duration else None
        return item if body.max_duration >= item["duration"] > body.min_duration else None

    def _filter_downtimes(
        item: dict[str, Any],
        before: dict[str, Any],  # noqa: ARG001
        after: dict[str, Any],  # noqa: ARG001
    ) -> dict[str, Any] | None:
        category: str | None = item.get(CATEGORY)
        classification = item.get(CLASSIFICATION) or item.get(LOSS_CATEGORY) or item.get(MAPPED_OP_MODE)
        category_filters = body.filters.by_category.keys()
        classification_filters = body.filters.by_category.get(category or "", [])
        # No Filters set. Return item
        if not category_filters:
            return item if category != PRODUCTIVE else None

        # Only category filters set.
        if not classification_filters:
            return item if category in category_filters else None

        # Category and classification filters set.
        # Filter out children downtimes which are not in the selected classification filters.
        if classification in classification_filters and "children" in item:
            children = []
            duration = 0
            for child in item["children"]:
                if child[CLASSIFICATION] in classification_filters:
                    children.append(child)
                    duration += child["duration"]
            if len(children) == len(item["children"]):
                return item

            # Adjust durations and times
            item["duration"] = duration
            item["start"] = children[0]["start"]
            item["end"] = children[-1]["end"]
            item["children"] = children

        # Filter out item if top level classification is not in selected filters.
        return item if classification in classification_filters else None

    def _check_start_boundary(
        item: dict[str, Any],
        before: dict[str, Any],  # noqa: ARG001
        after: dict[str, Any],  # noqa: ARG001
    ) -> dict[str, Any] | None:
        return item if int(item["start"]) >= start_time else None

    def _check_end_boundary(
        item: dict[str, Any],
        before: dict[str, Any],  # noqa: ARG001
        after: dict[str, Any],  # noqa: ARG001
    ) -> dict[str, Any] | None:
        return item if int(item["end"]) <= end_time else None

    LOGGER.debug("Calling filter_data() with filter_classes: %s", body.filters.by_category)

    processor = DocumentProcessor()
    filtered_downtimes = processor.preprocess_data(
        [
            create_augmentation_step("DurationFilter", _check_duration),
            create_augmentation_step("FilterDowntimes", _filter_downtimes),
            create_augmentation_step("StartBoundaryFilter", _check_start_boundary),
            create_augmentation_step("EndBoundaryFilter", _check_end_boundary),
        ],
        downtimes,  # type: ignore[arg-type]
    )

    return filtered_downtimes  # type: ignore[return-value]
