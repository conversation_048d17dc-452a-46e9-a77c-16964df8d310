from collections.abc import Iterator
import copy
import datetime
from typing import Any

from aws_lambda_powertools import Logger
from lib_cloud_sdk.processing.steps.match_failure_mode_step import DEFAULT_FAILURE_MODE
from lib_cloud_sdk.time_conversion import convert_epoch_time_to_iso
from message_texts_query.dmm.dmm_messages_handler import DmmMessagesHandler
from message_texts_query.unified.unified_messages_handler import get_default_unified

from common.constants import DEFAULT_INFO, FAILURE_INFO, MESSAGE

LOGGER = Logger()

DAYS_KEY = "days"


def split_downtime(downtime: dict, end_time: int) -> tuple[dict, dict]:
    "Splits the downtime object into two, one that ends at end_time and the second one will be the rest of it."
    downtime_day = copy.deepcopy(downtime)
    downtime_rest = copy.deepcopy(downtime)
    downtime_day["duration"] = end_time - downtime_day["start"]
    downtime_day["end"] = end_time
    downtime_rest["duration"] = downtime_rest["end"] - end_time
    downtime_rest["start"] = end_time + 1
    return downtime_day, downtime_rest


def split_downtime_by_day(downtime: dict, split_times: list[int]) -> list[dict[str, Any]]:
    "Splits the downtime object into n number of objects until it fits completely into a single day."
    splitted_downtimes = []
    while split_times and split_times[0] < downtime["start"]:
        split_times.pop(0)

    if not split_times:
        return [downtime]

    while True:
        if split_times and split_times[0] < downtime["end"]:
            split_time = split_times.pop(0)
            downtime_day, downtime_rest = split_downtime(downtime, split_time)
            splitted_downtimes.append(downtime_day)
            downtime = downtime_rest
        else:
            splitted_downtimes.append(downtime)
            break
    return splitted_downtimes


def milliseconds_in_day(utc_timestamp: int, timezone: datetime.tzinfo) -> int:
    """Calculate the number of milliseconds in the day for a given UTC timestamp and timezone.

    Since a day can have 23, 24, or 25 hours depending on the daylight saving time, we need to calculate the number of
    milliseconds in the day for the given timestamp and timezone.

    Args:
        utc_timestamp: The UTC timestamp in milliseconds.
        timezone: The timezone string (e.g., 'America/New_York').

    Returns:
        The number of milliseconds in the day.
    """
    local_time = datetime.datetime.fromtimestamp(int(utc_timestamp / 1000), timezone)
    start_of_day = local_time.replace(hour=0, minute=0, second=0, microsecond=0)
    end_of_day = (start_of_day + datetime.timedelta(days=1)).astimezone(timezone)

    start_utc_offset = start_of_day.utcoffset()
    end_utc_offset = end_of_day.utcoffset()
    if start_utc_offset is None or end_utc_offset is None:
        LOGGER.error("Could not determine the UTC offset for the given timezone.")
        return 24 * 3600 * 1000

    dst_offset_diff = start_utc_offset - end_utc_offset
    milliseconds = (24 * 3600 + dst_offset_diff.total_seconds()) * 1000

    return int(milliseconds)


def get_split_times(start_time: int, end_time: int, timezone: datetime.tzinfo) -> list[int]:
    """Returns a list of split times between start_ time and end_time.

    The split times are the start of each day between start_time and end_time, both exclusive.
    Start and end time should be an epoch timestamp at midnight.
    """
    split_times = []
    current_time = start_time + milliseconds_in_day(start_time, timezone)
    while current_time < end_time:
        split_times.append(current_time)
        current_time += milliseconds_in_day(current_time, timezone)
    return split_times


def group_display_messages_by_day(
    downtimes: Iterator[dict[str, Any]], start_time: int, end_time: int, timezone: datetime.tzinfo
) -> tuple[dict[str, Any], dict[str, Any]]:
    """Group the touch messages (messages to be displayed in the frontend) by day."""
    quantity_failure_modes: dict[str, Any] = {}
    duration_failure_modes: dict[str, Any] = {}

    split_times = get_split_times(start_time, end_time, timezone)
    day_timestamps = [start_time, *split_times]

    for downtime in downtimes:
        downtimes_by_day = split_downtime_by_day(downtime, split_times)

        for downtime_by_day in downtimes_by_day:
            display_message = (
                _build_display_message(downtime_by_day)
                if _is_dmm_message(downtime_by_day)
                else downtime_by_day["failure_mode"]
            )

            day_timestamp = max(t for t in day_timestamps if t <= downtime_by_day["start"])

            # Calculate and build quantity analysis
            _initialize_display_messages(quantity_failure_modes, downtime_by_day, display_message, split_times)
            _extend_display_messages(quantity_failure_modes, display_message, day_timestamp, 1)

            # Calculate and build duration analysis
            _initialize_display_messages(duration_failure_modes, downtime_by_day, display_message, split_times)
            _extend_display_messages(
                duration_failure_modes,
                display_message,
                day_timestamp,
                downtime_by_day["duration"],
            )

    return {"failure_modes": quantity_failure_modes}, {"failure_modes": duration_failure_modes}


def check_for_matching_failure_modes(
    downtimes: Iterator[dict[str, Any]], failure_mode: str
) -> Iterator[dict[str, Any]]:
    """Check the downtimes list for matching failure modes and return only the ones that match.

    Example failure modes: DMM = 4151.364 / ZAIT = MMA.364
    We can't have the message_nr (364 in example) for DMM at this point in loss['failure_mode'].
    That is why we need a special handling for DMM (split the incoming failure_mode from the
    frontend so that the comparison works).
    """
    for downtime in downtimes:
        failure_mode_split = failure_mode.split(".")
        fm_to_compare = failure_mode_split[0] if failure_mode_split[0].isnumeric() else failure_mode
        if fm_to_compare == downtime["failure_mode"]:
            yield {
                "duration": downtime["duration"],
                "classification": downtime.get("classification"),
                "category": downtime.get("category"),
                "start": convert_epoch_time_to_iso(downtime["start"]),
                "end": convert_epoch_time_to_iso(downtime["end"]),
                "message": downtime["message"],
                "downtimes": _initialize_display_downtimes(downtime.get("children", [])),
            }


def _initialize_display_downtimes(children: list[dict[str, Any]]) -> list[dict]:
    return [
        {
            "classification": child.get("classification"),
            "mds_state_code": child.get("raw_values", {}).get("aggregated_state"),
            "mode": child.get("raw_values", {}).get("mode"),
            "program": child.get("raw_values", {}).get("program"),
            "start": child.get("start"),
            "ws_cur_state_code": child.get("raw_values", {}).get("state"),
            "end": child.get("end"),
            "duration": child.get("duration"),
            "category": child.get("category"),
        }
        for child in children
    ]


def _initialize_display_messages(
    analysis: dict[str, Any],
    item: dict[str, Any],
    display_message: str,
    included_days: list[int],
) -> None:
    """Initialize the not already visited touch messages.

    Add the failure information (failure information is the actual resolved touch message).
    """
    if display_message not in analysis:
        analysis[display_message] = {
            DAYS_KEY: {},
            FAILURE_INFO: item["message"].get("messageText")
            if item.get("message", {}).get("messageText")
            else DEFAULT_INFO,
            MESSAGE: item["message"] if item.get("message", {}).get("messageType") else None,
        }

        for day in included_days:
            analysis[display_message][DAYS_KEY][str(day)] = {"value": 0}


def _extend_display_messages(analysis: dict[str, Any], display_message: str, day: int, value: int) -> None:
    """Increment the value if the touch message already exists for a particular day.

    If the incoming day is not present in the dictionary (unknown day)
    then create a new entry for this day.
    """
    if str(day) in analysis[display_message][DAYS_KEY]:
        analysis[display_message][DAYS_KEY][str(day)]["value"] += value
    else:
        analysis[display_message][DAYS_KEY][str(day)] = {"value": value}


def get_unified_message(item: dict[str, Any]) -> dict:
    return (
        item["message"]
        if item["message"] and item["message"].get("messageType")
        else get_default_unified(DEFAULT_FAILURE_MODE).dict()  # type: ignore[no-untyped-call]
    )


def _is_dmm_message(item: dict[str, Any]) -> bool:
    """Determine if the incoming message item is a DMM message or not."""
    message_item = get_unified_message(item)
    is_dmm_message = message_item.get("messageType") == DmmMessagesHandler.MESSAGE_TYPE
    return is_dmm_message


def _build_display_message(item: dict[str, Any]) -> str:
    """Build the touch message that is going to be used for comparison later on.

    This is needed because we can't have the message_nr for DMM in loss['failure_mode'].
    """
    message_item = get_unified_message(item)
    return f"{message_item.get('configId')}.{message_item.get('messageNr')}"
