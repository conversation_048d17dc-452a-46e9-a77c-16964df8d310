from fastapi import HTTPException, Request, status
from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

REQUIRED_FEATURE_FLAG = "performance-analytics"


def check_service_availability(request: Request) -> None:
    properties = CommonShare2ActProperties(request.scope.get("aws.event", {}).get("requestContext"))
    if REQUIRED_FEATURE_FLAG not in properties.global_rights:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Service is disabled for this user.")
