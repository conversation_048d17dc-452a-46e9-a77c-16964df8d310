from aws_lambda_powertools import Logger
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
from lib_kpi_config_client.models.api_models import ApiKpiModelTimeValidity
from lib_kpi_config_client.models.config_types import KpiModel

LOGGER = Logger()


def get_validated_kpi_model(
    account: str, line_id: str, kpi_model_id: str, start_time: int, end_time: int | None
) -> KpiModel | None:
    """Get the valid KPI model for the specified time range."""
    kpi_config_api_client = KpiConfigApiClient()
    assigned_kpi_models_in_timerange: list[ApiKpiModelTimeValidity] = (
        kpi_config_api_client.get_kpi_models_assigned_in_timerange(
            account=account, line_id=line_id, start=start_time, end=end_time
        )
    )
    for model in assigned_kpi_models_in_timerange:
        if model.kpi_model_id == kpi_model_id:
            return KpiModel(kpi_model_id=model.kpi_model_id, kpi_model_type=model.kpi_model_type)
    return None
