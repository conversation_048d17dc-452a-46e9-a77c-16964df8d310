from performance_analytics.models.machine_report_models import ResponseUnifiedMessage
from pydantic import BaseModel


class ValueForDay(BaseModel):
    value: int


class Days(BaseModel):
    failure_info: str
    message: dict | None
    days: dict[str, ValueForDay]  # str = {day} epoch format


class FailureModes(BaseModel):
    failure_modes: dict[str, Days]  # str = {failure_mode}


class FMGroupedByMachines(BaseModel):
    machines: dict[str, FailureModes] = {}  # str = {machine_id}


class StoppageAnalysis(BaseModel):
    quantity: FMGroupedByMachines
    duration: FMGroupedByMachines


class OverviewResponse(BaseModel):
    stoppage_analysis: StoppageAnalysis


class BaseDowntime(BaseModel):
    classification: str = "undefined"
    category: str = "undefined"
    start: int = 0
    end: int = 0
    duration: int = 0


class ChildLevelDowntime(BaseDowntime):
    ws_cur_state_code: int = -1
    mode: int = -1
    program: int = -1
    mds_state_code: int = -1


class TopLevelDowntime(BaseDowntime):
    message: ResponseUnifiedMessage | dict = {}
    downtimes: list[ChildLevelDowntime] | list = []


class MachineDetailsResponse(BaseModel):
    failure_mode: str
    failure_mode_details: list[TopLevelDowntime] | list = []
