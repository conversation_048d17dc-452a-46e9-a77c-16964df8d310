from typing import Self

from lib_cloud_sdk.time_conversion import convert_iso_time_to_epoch
from pydantic import BaseModel, field_validator, model_validator
from pytz import all_timezones


class Filters(BaseModel):
    by_category: dict[str, list[str]] = {}


class Time(BaseModel):
    start_time: str
    end_time: str
    timezone: str

    @field_validator("start_time", "end_time")
    @classmethod
    def check_time(cls, time: str) -> str:
        return str(convert_iso_time_to_epoch(time))

    @field_validator("timezone")
    @classmethod
    def validate_timezone(cls, timezone: str) -> str:
        if timezone not in all_timezones:
            raise ValueError("Invalid timezone provided")
        return timezone


class Body(BaseModel):
    filters: Filters
    kpi_model: str
    language: str
    line_id: str
    time: Time
    min_duration: int = 0
    max_duration: int = 0

    @model_validator(mode="after")
    def validate_durations(self) -> Self:
        if self.max_duration != 0 and self.max_duration < self.min_duration:
            raise ValueError(
                f"Wrong input: max_duration={self.max_duration} " + f"< min_duration={self.min_duration}",
            )
        if self.max_duration < 0 or self.min_duration < 0:
            raise ValueError(
                f"Wrong input: max_duration={self.max_duration}, min_duration={self.min_duration}. Value(s) can not be negative!",
            )
        return self


class OverviewBody(Body):
    limit: int
    machines: list[str]


class MachineDetailsBody(Body):
    failure_mode: str
    machine: str
