from aws_lambda_powertools import Tracer
from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from lib_cloud_sdk.util.sentry.init_fastapi import init_sentry
from mangum import Mangum
from performance_analytics.fast_api.middleware.sentry_tags import SetSentryTagsMiddleware
from performance_analytics.utility.logging import setup_logger

from api.route_handler import router as api_router
from common.constants import COMPRESSION_MINIMUM_SIZE
from dependencies.authorization import check_service_availability

init_sentry()

TRACER = Tracer()

setup_logger()  # type: ignore[no-untyped-call]

app = FastAPI(dependencies=[Depends(check_service_availability)])
app.include_router(api_router, prefix="/v1/performance-analytics/stoppage-analysis")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["POST, OPTIONS"],
    allow_headers=["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token"],
)
app.add_middleware(GZipMiddleware, minimum_size=COMPRESSION_MINIMUM_SIZE)
app.add_middleware(SetSentryTagsMiddleware)

lambda_handler = Mangum(app)
