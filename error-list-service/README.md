# Error List Service

The Error List Service is a reporting tool designed to generate detailed reports of downtimes and uptimes by listing all messages that started within a specified time range. This service helps in monitoring system performance, diagnosing issues, and maintaining optimal operational efficiency.

## Table of Contents
- [Features](#features)
- [Architecture](#architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Usage](#usage)
- [API Endpoints](#api-endpoints)
- [Logging](#logging)
- [Testing](#testing)
- [Deployment](#deployment)

## Features
- List all messages that started during the specified period.
- RESTful API interface for easy integration.
- Secure access with proper authentication and authorization mechanisms.

## Architecture
The service is built using the following components:
- **FastAPI**: A modern, fast (high-performance) web framework for building APIs with Python 3.7+.
- **AWS Services**:
  - **API Gateway**: To expose the FastAPI endpoints as RESTful APIs.
  - **AWS Lambda**: To run the FastAPI application as serverless functions.
  - **AWS Secrets Manager**: For secure retrieval of API keys and other secrets.
- **Requests**: Used for making HTTP requests to fetch message history data.

## Prerequisites
- **Python 3.11**: Ensure Python 3.11 is installed on your system.
- **AWS Account**: Required for accessing AWS services if deploying in AWS.
- **Access to Message History API**: The service depends on the Message History API for data retrieval.

## Installation
1. **Clone the Repository**
2. **Create a Virtual Environment**
3. **Install Dependencies**

## Usage
Run the application using the following command:
```sh
uvicorn src.main:app --reload
```

## API Endpoints
- **Get Error List**: Retrieve a report of messages started within a specified time range.
  - **Endpoint**: `GET https://api.rk.share2act-{stageName}.io/v1/performance-analytics/error-list/{machine_id}`
  - **Parameters**:
    - `machine_id` (path): The ID of the machine.
    - `startTime` (query): The start of the time range (epoch milliseconds).
    - `endTime` (query): The end of the time range (epoch milliseconds).
  - **Headers**:
    - `x-api-key`: API Key can be retrieved from Secrets Manager (RK account). `Key Name: message-recorder-api-secret`.

## Logging
- **Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL.
- **Configuration**: Adjust the logging level through the Advanced Settings in the AWS Console`.

## Testing
- **Running Unit Tests**: Ensure that test configuration does not interfere with production settings. Mock external dependencies like AWS services using tools like `moto`.

## Deployment
- **Deploying to AWS**: Use the AWS CloudFormation to deploy the service to AWS Lambda, API Gateway, and other services.
- **CI/CD Pipeline**: Jenkins is used for automating the deployment process.
- **Monitoring**: Use AWS CloudWatch for monitoring the service performance and logs.
