import json
from unittest.mock import <PERSON><PERSON>ock, patch

from fastapi import status
from fastapi.testclient import TestClient

from test.conftest import Du<PERSON><PERSON><PERSON>ponse


def test_get_error_list_api(requests_mock: MagicMock, secret_mock: MagicMock, aws_request_mock: MagicMock) -> None:
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    auth_context = {
        "claims": '{"scopes":["scope-1","scope-2", "show-error-list-report"]}',
        "principalId": "some-principal-id",
        "user": '{"userId":"some-user-id","username":"some-user-name","login":"some-login","groups":["group-1","site-manager"]}',
        "account": '{"accountId":"account1","userPoolId":"some-user-pool"}',
    }
    aws_request_mock.scope["aws.event"]["requestContext"]["authorizer"] = auth_context
    properties = CommonShare2ActProperties(aws_request_mock.scope.get("aws.event").get("requestContext"))
    with patch("performance_analytics.utility.s2a_request_wrapper.get_authorizer_context") as prop_mock:
        prop_mock.return_value = properties._authorizer_context
        from src.main import app

        client = TestClient(app)
        response = client.get(
            "/v1/performance-analytics/error-list/57fe079c-447a-4e48-81fd-4a5a62fec9b4?startTime=*************&endTime=*************"
        )
        assert response.status_code == status.HTTP_200_OK
        with open(r"test/api_test/example_response.json") as fp:
            data = json.load(fp)
        assert data == json.loads(response.content)


def test_get_error_list_api_error_case(
    requests_mock: MagicMock, secret_mock: MagicMock, aws_request_mock: MagicMock
) -> None:
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    auth_context = {
        "claims": '{"scopes":["scope-1","scope-2", "show-error-list-report"]}',
        "principalId": "some-principal-id",
        "user": '{"userId":"some-user-id","username":"some-user-name","login":"some-login","groups":["group-1","site-manager"]}',
        "account": '{"accountId":"account1","userPoolId":"some-user-pool"}',
    }
    aws_request_mock.scope["aws.event"]["requestContext"]["authorizer"] = auth_context
    properties = CommonShare2ActProperties(aws_request_mock.scope.get("aws.event").get("requestContext"))
    with patch("performance_analytics.utility.s2a_request_wrapper.get_authorizer_context") as prop_mock:
        prop_mock.return_value = properties._authorizer_context
        data = {"disc": {"detail": "Problem in fetching data from API"}}
        requests_mock.return_value = DummyResponse(json.dumps(data), is_ok=False)
        from src.main import app

        client = TestClient(app)
        response = client.get(
            "/v1/performance-analytics/error-list/57fe079c-447a-4e48-81fd-4a5a62fec9b4?startTime=*************&endTime=*************"
        )
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert data["disc"] == json.loads(response.content)


def test_exception_get_error_list_api(aws_request_mock: MagicMock) -> None:
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    auth_context = {
        "claims": '{"scopes":["scope-1","scope-2"]}',
        "principalId": "some-principal-id",
        "user": '{"userId":"some-user-id","username":"some-user-name","login":"some-login","groups":["group-1","site-manager"]}',
        "account": '{"accountId":"account1","userPoolId":"some-user-pool"}',
    }
    aws_request_mock.scope["aws.event"]["requestContext"]["authorizer"] = auth_context
    properties = CommonShare2ActProperties(aws_request_mock.scope.get("aws.event").get("requestContext"))
    with patch("performance_analytics.utility.s2a_request_wrapper.get_authorizer_context") as prop_mock:
        prop_mock.return_value = properties._authorizer_context

        from src.main import app

        client = TestClient(app)
        response = client.get(
            "/v1/performance-analytics/error-list/57fe079c-447a-4e48-81fd-4a5a62fec9b4?startTime=*************&endTime=*************"
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json() == {"detail": "Error List Report service is not available for this account."}
