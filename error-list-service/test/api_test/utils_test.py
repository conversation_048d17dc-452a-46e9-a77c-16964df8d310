from collections.abc import Generator
import json
from unittest.mock import MagicMock, patch

import pytest

API_KEY = "mock-api-key"


@pytest.fixture
def mocked_secrets_client() -> Generator[MagicMock]:
    """Fixture to mock secrets client."""
    with patch("src.api.endpoints.utils.secrets_client") as mock_client:
        mock_client.get_secret_value.return_value = {"SecretString": json.dumps({"api_key": API_KEY})}
        yield mock_client


def test_get_api_key(mocked_secrets_client: MagicMock) -> None:
    from src.api.endpoints.utils import SECRET_NAME, get_api_key

    api_key = get_api_key()
    mocked_secrets_client.get_secret_value.assert_called_once_with(SecretId=SECRET_NAME)
    assert api_key == API_KEY
