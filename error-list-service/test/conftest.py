from collections.abc import Generator
import json
import os
from unittest.mock import MagicMock, patch

from fastapi import Request
import pytest


@pytest.fixture
def aws_credentials() -> None:
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["AWS_STAGE"] = "testing"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"
    os.environ["MESSAGE_RECORDER_API_SECRET_NAME"] = "message-recorder-api-secret"


@pytest.fixture
def aws_request_mock() -> Request:
    event = {
        "aws.event": {"requestContext": {}},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    return request


@pytest.fixture
def secret_mock() -> Generator[MagicMock]:
    with patch("api.endpoints.utils.get_api_key") as secret_mock:
        secret_mock.return_value = "its_a_secret"
        yield secret_mock


class DummyResponse:
    def __init__(self, data: str, is_ok: bool = True):
        self.data = data
        self.ok = is_ok
        self.status_code = 200 if self.ok else 500

    def json(self) -> list[dict[str, str]]:
        return json.loads(self.data)


@pytest.fixture
def requests_mock() -> Generator[list[dict[str, str]]]:
    with patch("requests.get") as requests_mock:
        with open(r"test/api_test/example_input.json") as fp:
            data = json.load(fp)
        requests_mock.return_value = DummyResponse(json.dumps(data))
        yield requests_mock
