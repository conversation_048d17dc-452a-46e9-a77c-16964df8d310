#POETRY

[tool.poetry]
name = "error-list-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
fastapi = "^0"
mangum = "^0"
requests = "^2.32.3"
secweb = "^1.11.0"
lib-performance-analytics = "^32"
ft2-cloud-sdk = "^16"

[tool.poetry.group.dev.dependencies]
pre-commit = "*"
pylint = "^3"
pytest = "^7"
boto3 = "^1.28"
pytest-cov = "*"
pytest-mock = "^3"
mock = "^5"
moto = "^4"
ruff = "^0"
toml = "^0"
mypy = "^1"
httpx = "^0.27.2"
uvicorn = "^0.27"  # pin to that version due to "h11" not beeing installed otherwise
types-requests = "^2"
boto3-stubs = { extras= ["essential", "exceptions"], version="^1"}

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"


[tool.mypy]
incremental = true
cache_dir = ".mypy_cache"
python_version = "3.13"
disallow_untyped_defs = true
follow_imports = "silent"
disallow_untyped_calls = true
disallow_incomplete_defs = true
exclude = ["test"]
mypy_path = ["src"]
namespace_packages =  true
explicit_package_bases = true
plugins = [
  "pydantic.mypy"
]

[[tool.mypy.overrides]]
module = ["Secweb.*"]
ignore_missing_imports = true

[tool.ruff]
src = ["src", "test"]
target-version = 'py313'
extend = "../.configs/ruff.toml"

[tool.ruff.lint]
extend-per-file-ignores = { "*models*" = ["N815"] }

[tool.ruff.lint.isort]
known-local-folder = ["src", "test"]


[tool.pytest.ini_options]
addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes --junitxml=../test-reports/report/error-list-service-cov.xml"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning",
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = "./src"
testpaths = ["test"]

#COVERAGE

[tool.coverage.run]
branch = true
omit = ["test/*", "*/__init__.py", "*/_version.py"]

[tool.coverage.report]
precision = 2
fail_under = 92

[tool.coverage.xml]
output = "../test-reports/coverage/error-list-service-cov.xml"
