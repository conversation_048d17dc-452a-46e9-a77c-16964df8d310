
SHELL:=/bin/bash

.DEFAULT_GOAL := help

GIT_COMMIT = $(shell git rev-parse HEAD)
BRANCH_NAME = $(shell git symbolic-ref --short HEAD)

CWD := $(shell pwd)
SERVICE_PATH = $(shell cd ../; pwd)
SERVICE_NAME ?= $(shell basename $(SERVICE_PATH))
FUNCTION_NAME ?= $(shell basename $(CWD))
ZIP_FILE ?= $(FUNCTION_NAME)-$(GIT_COMMIT).zip

JUNIT_XML ?= $(SERVICE_PATH)/test-reports/report/$(FUNCTION_NAME).xml
COV_REPORT_XML ?= $(SERVICE_PATH)/test-reports/coverage/$(FUNCTION_NAME).xml

RK_BUCKET ?= rk-deploy-jenkins-dev
S3_PATH = $(SERVICE_NAME)/$(BRANCH_NAME)

# Export variables for sub-makefiles
export TEST_FILES_OR_DIRS := test

################################################################
######    Fall back to targets in shared Makefile     ##########
################################################################

# Hack to automatically update submodules
SUBMODULE := $(shell git submodule update --init --recursive)

.PHONY: %
%: Makefile
	@$(MAKE) -e -f ../.configs/Makefile $@

.PHONY: Makefile
Makefile: ;

.PHONY: init
init:
	@echo "Initialization already done with install. Skipping..."

.PHONY: bundle
bundle: clean ## bundles the service
	mkdir -p build/libs/ build/distributions/
	poetry export --output requirements.txt --with-credentials
	poetry run pip install --platform manylinux2014_aarch64 --no-deps -r requirements.txt -t build/libs
	cd build/libs/; zip -qr ../../build/distributions/$(ZIP_FILE) . ; cd ../../
	cd src/; zip -qr ../build/distributions/$(ZIP_FILE) . ; cd ../

.PHONY: generate-openapi
generate-openapi:
	@echo "███ Generating openapi.json..."
	export PYTHONPATH="${PYTHONPATH}:./src" \
	&& poetry run python src/api/documentation/openapi_generator.py
	@echo

.PHONY: generate-client
generate-client: generate-openapi
	@echo "███ Generating frontend client..."
	npx @openapitools/openapi-generator-cli generate -c openapitools.json -o client/frontend

####################################################
# update this specific lambda function manually
update-fkn: upload update-lambda-code

upload:
	aws s3 cp build/distributions/$(ZIP_FILE) s3://$(RK_BUCKET)/$(S3_PATH)/

update-lambda-code:
	aws lambda update-function-code --function-name $(FUNCTION_NAME) --s3-bucket $(RK_BUCKET) --s3-key $(S3_PATH)/$(ZIP_FILE) --no-cli-pager
