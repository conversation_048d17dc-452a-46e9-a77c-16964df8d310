from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from lib_cloud_sdk.util.sentry.init_fastapi import init_sentry
from mangum import Mangum
from performance_analytics.fast_api.middleware.sentry_tags import (
    SetSentryTagsMiddleware,
)
from Secweb import <PERSON>c<PERSON>eb
from Secweb.CacheControl import CacheControl

from api.documentation.metadata import get_tags_metadata
from api.route_handler import router as api_router

init_sentry()

app = FastAPI(openapi_tags=get_tags_metadata())

app.include_router(api_router, prefix="/v1/performance-analytics")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token"],
)

SecWeb(app=app, Option={"corp": {"Cross-Origin-Resource-Policy": "same-site"}})
app.add_middleware(CacheControl, Option={"no-cache": True})
app.add_middleware(SetSentryTagsMiddleware)

handler = Mangum(app)
