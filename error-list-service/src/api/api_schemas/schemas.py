import json

from pydantic import BaseModel, ConfigDict, Field, field_validator
from pydantic.alias_generators import to_camel


# Use camel case for request response to adhere to OpenAPI specifications
# But keep snake case to adhere to PEP8
class ApiModel(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True, from_attributes=True)


class Message(ApiModel):
    message_id: str = Field(..., alias="messageId")
    start_time: int = Field(..., alias="startTime")
    equipment_id: str = Field(..., alias="equipmentId")
    message_class: str = Field(..., alias="messageClass")
    end_time: int | None = Field(..., alias="endTime")
    keyword: str | None

    class Config:
        validate_assignment = False


class ConfigData(ApiModel):
    message_id: str = Field(..., alias="messageId")
    message_nr: int = Field(..., alias="messageNr")
    message_text: dict[str, str] = Field(..., alias="messageText")
    message_type: str = Field(..., alias="messageType")
    message_class: str = Field(..., alias="messageClass")
    datasource_source_id: str = Field(..., alias="datasourceSourceId")

    class Config:
        validate_assignment = False

    @field_validator("message_text", mode="before")
    def ensure_message_text_is_dict(cls, value: dict | str) -> dict:  # noqa: N805
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return {}
        elif not isinstance(value, dict):
            return {}
        return value


class ErrorListItem(ApiModel):
    message: Message
    config: ConfigData | None


class ErrorListResponse(ApiModel):
    messages: list[ErrorListItem]
    last_processed_timestamp: int | None = Field(default=None, alias="lastProcessedTimestamp")
