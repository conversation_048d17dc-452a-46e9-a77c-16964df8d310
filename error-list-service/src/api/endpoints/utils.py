import json
import os

from aws_lambda_powertools import Logger
import boto3
from fastapi import HTTPException
import requests

LOGGER = Logger()

secrets_client = boto3.client("secretsmanager")
SECRET_NAME = os.getenv("MESSAGE_RECORDER_API_SECRET_NAME", "message-recorder-api-secret")
aws_stage = os.getenv("AWS_STAGE", "dev")
STAGE = f"-{aws_stage}" if aws_stage != "prod" else ""


def get_api_key() -> str:
    response = secrets_client.get_secret_value(SecretId=SECRET_NAME)
    secret = json.loads(response["SecretString"])
    return secret["api_key"]


def fetch_message_history(
    machine_id: str,
    start_time: int,
    end_time: int,
    last_processed_timestamp: int | None,
) -> dict:
    api_key = get_api_key()
    url = (
        f"https://message-history.rk.share2act{STAGE}.io"
        f"/messages/{machine_id}/paginated?startTime={start_time}"
        f"&endTime={end_time}&messageConfig=true"
    )
    if last_processed_timestamp is not None:
        url += f"&lastProcessedTimestamp={last_processed_timestamp}"

    headers = {"x-api-key": api_key}
    response = requests.get(url, headers=headers, timeout=30)
    LOGGER.info("Response from message history api is: %s", response)
    if response.ok:
        return response.json()
    else:
        error_data = response.json()
        LOGGER.error("Error from message history api is: %s", error_data)
        detail = error_data.get("message", "Problem in fetching data from API")
        raise HTTPException(status_code=response.status_code, detail=detail)
