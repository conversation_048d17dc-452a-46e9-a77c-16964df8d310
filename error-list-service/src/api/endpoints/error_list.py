from aws_lambda_powertools import Logger
from fastapi import APIRouter, HTTPException, Query, Request, status

from api.api_schemas.schemas import ErrorListResponse
from api.endpoints.dependencies import Share2ActProperties
from api.endpoints.utils import fetch_message_history

LOGGER = Logger()

router = APIRouter()
ERROR_LIST_REPORT = "show-error-list-report"


@router.get(
    "/error-list/{machine_id}",
    response_model=ErrorListResponse,
    tags=["error-list"],
)
def get_error_list(
    request: Request,
    properties: Share2ActProperties,
    machine_id: str,
    start_time: int = Query(..., alias="startTime"),
    end_time: int = Query(..., alias="endTime"),
    last_processed_timestamp: int | None = Query(default=None, alias="lastProcessedTimestamp"),
) -> ErrorListResponse:
    """Returns Error list."""
    LOGGER.debug(
        "Received a GET request with params: request: %s machine_id : %s, ",
        request,
        machine_id,
    )
    if ERROR_LIST_REPORT not in properties.global_rights:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Error List Report service is not available for this account.",
        )
    result = fetch_message_history(machine_id, start_time, end_time, last_processed_timestamp)
    return ErrorListResponse.model_validate(result)
