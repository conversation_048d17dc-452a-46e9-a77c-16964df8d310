from typing import Annotated

from fastapi import Depends, Request
from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties


def get_share2act_properties(request: Request) -> CommonShare2ActProperties:
    return CommonShare2ActProperties(request.scope.get("aws.event", {}).get("requestContext"))


Share2ActProperties = Annotated[CommonShare2ActProperties, Depends(get_share2act_properties)]
