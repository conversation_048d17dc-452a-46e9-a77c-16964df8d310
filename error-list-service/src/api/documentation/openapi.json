{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/v1/performance-analytics/error-list/{machine_id}": {"get": {"tags": ["error-list"], "summary": "Get Error List", "description": "Returns Error list.", "operationId": "get_error_list_v1_performance_analytics_error_list__machine_id__get", "parameters": [{"name": "machine_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Machine Id"}}, {"name": "startTime", "in": "query", "required": true, "schema": {"type": "integer", "title": "Starttime"}}, {"name": "endTime", "in": "query", "required": true, "schema": {"type": "integer", "title": "Endtime"}}, {"name": "lastProcessedTimestamp", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lastprocessedtimestamp"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ConfigData": {"properties": {"messageId": {"type": "string", "title": "Messageid"}, "messageNr": {"type": "integer", "title": "Messagenr"}, "messageText": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Messagetext"}, "messageType": {"type": "string", "title": "Messagetype"}, "messageClass": {"type": "string", "title": "Messageclass"}, "datasourceSourceId": {"type": "string", "title": "Datasourcesourceid"}}, "type": "object", "required": ["messageId", "messageNr", "messageText", "messageType", "messageClass", "datasourceSourceId"], "title": "ConfigData"}, "ErrorListItem": {"properties": {"message": {"$ref": "#/components/schemas/Message"}, "config": {"anyOf": [{"$ref": "#/components/schemas/ConfigData"}, {"type": "null"}]}}, "type": "object", "required": ["message", "config"], "title": "ErrorListItem"}, "ErrorListResponse": {"properties": {"messages": {"items": {"$ref": "#/components/schemas/ErrorListItem"}, "type": "array", "title": "Messages"}, "lastProcessedTimestamp": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lastprocessedtimestamp"}}, "type": "object", "required": ["messages"], "title": "ErrorListResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Message": {"properties": {"messageId": {"type": "string", "title": "Messageid"}, "startTime": {"type": "integer", "title": "Starttime"}, "equipmentId": {"type": "string", "title": "Equipmentid"}, "messageClass": {"type": "string", "title": "Messageclass"}, "endTime": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Endtime"}, "keyword": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Keyword"}}, "type": "object", "required": ["messageId", "startTime", "equipmentId", "messageClass", "endTime", "keyword"], "title": "Message"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}, "tags": [{"name": "error-list", "description": "Error list"}]}