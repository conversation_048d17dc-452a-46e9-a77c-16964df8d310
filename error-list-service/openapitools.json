{"$schema": "node_modules/@openapitools/openapi-generator-cli/config.schema.json", "spaces": 2, "generator-cli": {"version": "7.11.0"}, "generatorName": "typescript-fetch", "output": "./", "inputSpec": "src/api/documentation/openapi.json", "gitRepoId": "performance-analytics-service", "gitUserId": "s2a_perf", "gitHost": "pd.bitbucket.syskron.com/scm", "additionalProperties": {"npmName": "@s2a/error-list-client", "supportsES6": true}}