"Service for publishing events on the ISC bus."

from typing import Literal, assert_never

from aws_lambda_powertools import Logger
from lib_s2a_events_v3.comments_service.comment_created_broadcast import (
    CommentCreatedBroadcast,
    CommentCreatedBroadcastEvent,
)
from lib_s2a_events_v3.comments_service.comment_deleted_broadcast import (
    CommentDeletedBroadcastEvent,
)
from lib_s2a_events_v3.comments_service.comment_updated_broadcast import (
    CommentUpdatedBroadcastEvent,
)
from lib_s2a_events_v3.exceptions import EventPublicationFailedException

from api.api_schemas.schemas import Comment

EventType = Literal["created", "updated", "deleted"]
type CommentEvent = CommentCreatedBroadcastEvent | CommentDeletedBroadcastEvent | CommentUpdatedBroadcastEvent
# This is just syntactic sugar since isc-events-v3 does not currently allow to define
# shared definitions. The comment entity is the same over all three event types
type CommentForEvent = CommentCreatedBroadcast


LOGGER = Logger()


class IscEventService:
    "Publishes events to the ISC bus."

    @classmethod
    def _get_event_instance(cls, event_type: EventType) -> CommentEvent:
        "Factory method that creates the corresponding event depending on `event_type`."
        match event_type:
            case "created":
                return CommentCreatedBroadcastEvent()  # type: ignore[no-untyped-call]
            case "updated":
                return CommentUpdatedBroadcastEvent()  # type: ignore[no-untyped-call]
            case "deleted":
                return CommentDeletedBroadcastEvent()  # type: ignore[no-untyped-call]
            case _:
                assert_never(event_type)

    @classmethod
    def _build_event_entity(cls, comment: Comment, account: str) -> CommentForEvent:
        """Builds event entity according to event schema.“.

        Transforms the database entity into an event entity since they have different
        attribute names. Additionally enriches event entity with required account id.

        Args:
            comment (Comment): Database entity to adapt.
            account (str): Account id of this comment.

        Returns:
            Adapted event entity.
        """
        return {
            "account": account,
            "equipmentId": comment.equipment_id,
            "kpiModel": comment.kpi_model_key,
            "start": comment.start_date,
            "end": comment.end_date,
            "category": comment.category,
            "text": comment.text,
            "title": comment.title,
            "commentId": comment.comment_id,
            "createdAt": comment.creation_date,
            "updatedAt": comment.change_date,
            "createdBy": comment.creator_id,
        }

    @classmethod
    def publish(cls, comment: Comment, *, account: str, event_type: EventType) -> None:
        """Publishes `Comment` event to ISC.

        Args:
            comment (Comment): Comment to be published.
            account (str): Account id the comment originates from.
            event_type (EventType): Type of event. A choice of 'created' | 'updated' | 'deleted'.
        """
        event = cls._get_event_instance(event_type)
        event.set_source("comments-service")
        try:
            data = cls._build_event_entity(comment, account)
            event.set_message(data)
            LOGGER.debug("Publishing event", extra={"event": event.get_message().dict()})  # type: ignore[no-untyped-call]
            event.publish()  # type: ignore[no-untyped-call]
        except AttributeError:
            LOGGER.exception("Error when publishing event. Cannot construct comment.")
        except EventPublicationFailedException:
            LOGGER.exception("Error when publishing event")
        # Isolate lib_s2a_events_v3 since it doesn't communicate clearly about possible exceptions.
        # Catching specific exceptions is not possbile without having leaky abstractions.
        except Exception:
            LOGGER.exception("Error ocurred in lib_s2a_events_v3")
