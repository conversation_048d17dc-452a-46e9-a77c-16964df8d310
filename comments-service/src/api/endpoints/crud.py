import time
import uuid

from aws_lambda_powertools import Logger
from lib_equipment_cache_common.clients.equipment_client import EquipmentClient
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
from lib_kpi_config_client.utils.constants import KPI_MODEL_INSIGHT
from sqlalchemy import and_
from sqlalchemy.orm import Session

from database.models import CommentEntity

from ..api_schemas.schemas import Comment, CreateComment

LOGGER = Logger()


class CommentNotFoundError(Exception):
    pass


def get_all(
    equipment_id: str,
    kpi_model: str,
    start_date: int,
    end_date: int | None,
    customer: str,
    session: Session,
) -> list[CommentEntity]:
    end_date = end_date or _current_time_in_milliseconds()
    result = session.query(CommentEntity).filter(
        and_(CommentEntity.equipment_id == equipment_id, CommentEntity.kpi_model_key == kpi_model)
    )
    result_filtered_by_time = result.filter(
        and_(start_date <= CommentEntity.end_date, end_date >= CommentEntity.start_date)
    )
    result_filtered_by_customer = result_filtered_by_time.filter(customer == CommentEntity.customer).all()
    LOGGER.debug("Returning response: %s.", str(result_filtered_by_customer))
    return result_filtered_by_customer


def create(
    body: CreateComment, customer: str, session: Session, user_id: str, user_groups: list[str]
) -> CommentEntity:
    _validate_create_permissions_insight(
        user_id=user_id,
        kpi_model_id=body.kpi_model_key,
        user_groups=user_groups,
        equipment_id=body.equipment_id,
        account=customer,
        timestamp=body.start_date,
    )
    timestamp = _current_time_in_milliseconds()
    comment_id = str(uuid.uuid4())
    comment = CommentEntity(
        comment_id=comment_id,
        equipment_id=body.equipment_id,
        kpi_model_key=body.kpi_model_key,
        start_date=body.start_date,
        end_date=body.end_date,
        creator_id=body.creator_id,
        category=body.category,
        text=body.text,
        creation_date=timestamp,
        change_date=timestamp,
        title=body.title,
        attachments=body.attachments,
        customer=customer,
    )
    session.add(comment)
    session.commit()
    LOGGER.debug("Added comment in database: %s.", str(comment))
    return comment


def update(
    comment_id: str, body: Comment, user_id: str, customer: str, session: Session, user_groups: list[str]
) -> CommentEntity:
    def _update_class_variables_from_requested_fields() -> None:
        for attribute, value in vars(body).items():
            if attribute in ["comment_id", "creation_date", "change_date", "customer"]:
                continue
            if value is not None:
                LOGGER.debug("Setting value: %s for attribute: %s", value, attribute)
                setattr(comment, attribute, value)
            else:
                LOGGER.debug("Skip setting value: %s for attribute: %s", value, attribute)

    comment = _try_get_comment_entity(comment_id, user_id, customer, session, user_groups)
    _update_class_variables_from_requested_fields()
    modified_now = _current_time_in_milliseconds()
    comment.change_date = modified_now
    session.add(comment)
    session.commit()
    session.refresh(comment)
    LOGGER.debug("Updated comment in database: %s.", str(comment))
    return comment


def delete(comment_id: str, user_id: str, customer: str, session: Session, user_groups: list[str]) -> CommentEntity:
    comment = _try_get_comment_entity(comment_id, user_id, customer, session, user_groups)
    session.delete(comment)  # type: ignore[no-untyped-call]
    session.commit()
    LOGGER.debug("Deleted comment in database: %s.", str(comment))
    return comment


def _try_get_comment_entity(
    comment_id: str, user_id: str, customer: str, session: Session, user_groups: list[str]
) -> CommentEntity:
    comment = session.query(CommentEntity).get(comment_id)

    if comment is None:
        error_message = f"No comment with {comment_id} found in database."
        raise CommentNotFoundError(error_message)
    if (comment.creator_id != user_id and "admin" not in user_groups) or comment.customer != customer:
        error_message = f"User {user_id} is not allowed to update/delete comment: {comment_id}."
        raise PermissionError(error_message)

    return comment


def _current_time_in_milliseconds() -> int:
    return int(time.time() * 1000)


def _validate_create_permissions_insight(
    user_id: str,
    kpi_model_id: str,
    user_groups: list[str],
    equipment_id: str,
    account: str,
    timestamp: int,
) -> None:
    """Validates if the user has the required permissions to perform the action.

    This validation is specific to the INSIGHT model and ensures that the user
    belongs to the required groups or the insight is the only active model at the point of time.
    """
    line_id = EquipmentClient(account=account).get_equipment_by_id(equipment_id=equipment_id).line_id
    assigned_kpi_models = KpiConfigApiClient().get_kpi_models_assigned_at_time(
        account=account, line_id=line_id, start=timestamp
    )

    if (
        kpi_model_id == KPI_MODEL_INSIGHT
        and not any(group in user_groups for group in ["admin", "site-manager"])
        and not (len(assigned_kpi_models) == 1 and assigned_kpi_models[0].kpi_model_id == KPI_MODEL_INSIGHT)
    ):
        raise PermissionError(f"User {user_id} is not allowed to create comment")
