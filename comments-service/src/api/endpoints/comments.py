import asyncio
import itertools

from aws_lambda_powertools import Logger
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from lib_equipment_cache_common.clients.equipment_client import EquipmentClient
from lib_equipment_cache_common.exceptions.exceptions import EquipmentClientResponseError, EquipmentNotFoundError
from sqlalchemy.exc import DatabaseError
from sqlalchemy.orm import Session

from api.api_schemas.schemas import Comment, CreateComment, DeletedComment
from api.endpoints.crud import CommentNotFoundError, create, delete, get_all, update
from api.endpoints.utils import get_customer, get_user_groups, get_user_id
from database.models import CommentEntity
from dependencies.database_session import get_session
from services.isc_event_service import IscEventService

LOGGER = Logger()

router = APIRouter()


@router.get(
    "/comments",
    response_model=list[Comment],
    tags=["comments"],
    dependencies=[Depends(validate_fast_api_timerange)],
    operation_id="get_comments",
)
async def get_comments(
    request: Request,
    equipment_id: str = Query(..., alias="equipmentId"),
    is_line: bool = Query(False, alias="isLine"),
    kpi_model: str = Query(..., alias="kpiModel"),
    start_date: int = Query(..., alias="startDate"),
    end_date: int | None = Query(None, alias="endDate"),
    session: Session = Depends(get_session),
) -> list[CommentEntity]:
    """Get all comments within the specified timerange for a specific equipmentId and kpiModel.

    - **equipmentId**: required to filter comments by equipmentId
    - **isLine**: required to query comments for all equipment of a line
    - **kpiModel**: required to filter comments by kpiModel
    - **startDate**: required to specify the start of the timerange
    - **endDate**: required to specify the end of the timerange
    """
    LOGGER.debug(
        "Received a GET request with querystring params: equipment_id : %s, kpi_model: %s, start_date: %s, end_date: %s.",
        equipment_id,
        kpi_model,
        start_date,
        end_date,
    )
    customer = get_customer(request)

    if is_line:
        equipment_service = EquipmentClient(customer)
        try:
            machines = equipment_service.get_equipments_by_line_id(equipment_id)
        except EquipmentNotFoundError as error:
            LOGGER.exception("No machines found for line: %s", equipment_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"No machines found for line: {equipment_id}"
            ) from error
        except EquipmentClientResponseError as error:
            LOGGER.exception("Error while getting equipment: %s", equipment_id)
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(error)) from error

        LOGGER.info("Getting notes for: %s", machines)

        get_all_async = [
            asyncio.to_thread(get_all, machine.equipment_id, kpi_model, start_date, end_date, customer, session)
            for machine in machines
        ]
        res = await asyncio.gather(*get_all_async)
        return list(itertools.chain(*res))  # flatten list

    else:
        return get_all(equipment_id, kpi_model, start_date, end_date, customer, session)


@router.post(
    "/comments",
    response_model=Comment,
    status_code=status.HTTP_201_CREATED,
    tags=["comments"],
    operation_id="create_comment",
)
def create_comment(body: CreateComment, request: Request, session: Session = Depends(get_session)) -> CommentEntity:
    """Create a comment with all the information specified in the body.

    See CreateComment schema for more details.

    - **body**: contains all the information needed to store the comment
    """
    LOGGER.debug("Received a POST request with body: %s.", body)
    user_id = get_user_id(request)
    user_groups = get_user_groups(request)
    customer = get_customer(request)
    try:
        comment = create(body, customer, session, user_id, user_groups)
    except PermissionError as error:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(error)) from error
    except DatabaseError as error:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(error)) from error

    IscEventService.publish(comment, account=customer, event_type="created")

    return comment


@router.put(
    "/comments/{comment_id}",
    response_model=Comment,
    tags=["comments"],
    operation_id="update_comment",
)
def update_comment(
    comment_id: str, body: Comment, request: Request, session: Session = Depends(get_session)
) -> CommentEntity:
    """Update a comment with the information specified in the body.

    See Comment schema for more details.

    - **body**: contains all the information needed to update the comment
    - **commentId**: required to specify the comment to be updated
    """
    LOGGER.debug("Received a PUT request for comment_id: %s with body: %s.", comment_id, body)
    user_id = get_user_id(request)
    customer = get_customer(request)
    user_groups = get_user_groups(request)
    try:
        updated_comment = update(comment_id, body, user_id, customer, session, user_groups)
    except PermissionError as error:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(error)) from error
    except CommentNotFoundError as error:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(error)) from error

    IscEventService.publish(updated_comment, account=customer, event_type="updated")

    return updated_comment


@router.delete(
    "/comments/{comment_id}",
    response_model=DeletedComment,
    tags=["comments"],
    operation_id="delete_comment",
)
def delete_comment(comment_id: str, request: Request, session: Session = Depends(get_session)) -> CommentEntity:
    """Delete a comment by the specified commentId.

    - **commentId**: required to specify the comment to be updated
    """
    LOGGER.debug("Received a DELETE request for comment_id: %s.", comment_id)
    user_id = get_user_id(request)
    customer = get_customer(request)
    user_groups = get_user_groups(request)
    try:
        deleted_comment = delete(comment_id, user_id, customer, session, user_groups)
    except PermissionError as error:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(error)) from error
    except CommentNotFoundError as error:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(error)) from error

    IscEventService.publish(deleted_comment, account=customer, event_type="deleted")

    return deleted_comment
