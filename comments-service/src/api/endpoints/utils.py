import json

from fastapi import Request


def get_user_id(request: Request) -> str:
    user = json.loads(request.scope.get("aws.event").get("requestContext").get("authorizer").get("user"))  # type: ignore[union-attr]
    user_id = user.get("userId")
    return user_id


def get_customer(request: Request) -> str:
    account = json.loads(request.scope.get("aws.event").get("requestContext").get("authorizer").get("account"))  # type: ignore[union-attr]
    customer = account.get("accountId")
    return customer


def get_user_groups(request: Request) -> list[str]:
    user = json.loads(request.scope.get("aws.event").get("requestContext").get("authorizer").get("user"))  # type: ignore[union-attr]
    user_groups = user.get("groups")
    return user_groups if isinstance(user_groups, list) else [user_groups]
