from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel


# Use camel case for request response to adhere to OpenAPI specifications
# But keep snake case to adhere to PEP8
class ApiModel(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True, from_attributes=True)


class CreateComment(ApiModel):
    equipment_id: str = Field(..., alias="equipmentId")
    kpi_model_key: str = Field(..., alias="kpiModelKey")
    start_date: int = Field(..., alias="startDate")
    end_date: int = Field(..., alias="endDate")
    creator_id: str = Field(..., alias="creatorId")
    category: str
    text: str
    title: str
    attachments: list[str] | None = None


class Comment(CreateComment):
    comment_id: str = Field(..., alias="commentId")
    creation_date: int = Field(..., alias="creationDate")
    change_date: int = Field(..., alias="changeDate")


class DeletedComment(ApiModel):
    comment_id: str = Field(..., alias="commentId")
