{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/v1/performance-analytics/comments": {"get": {"tags": ["comments"], "summary": "Get Comments", "description": "Get all comments within the specified timerange for a specific equipmentId and kpiModel.\n\n- **equipmentId**: required to filter comments by equipmentId\n- **isLine**: required to query comments for all equipment of a line\n- **kpiModel**: required to filter comments by kpiModel\n- **startDate**: required to specify the start of the timerange\n- **endDate**: required to specify the end of the timerange", "operationId": "get_comments", "parameters": [{"name": "equipmentId", "in": "query", "required": true, "schema": {"type": "string", "title": "Equipmentid"}}, {"name": "isLine", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Isline"}}, {"name": "kpiModel", "in": "query", "required": true, "schema": {"type": "string", "title": "Kpimodel"}}, {"name": "startDate", "in": "query", "required": true, "schema": {"type": "integer", "title": "Startdate"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Enddate"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Comment"}, "title": "Response Get Comments"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["comments"], "summary": "Create Comment", "description": "Create a comment with all the information specified in the body.\n\nSee CreateComment schema for more details.\n\n- **body**: contains all the information needed to store the comment", "operationId": "create_comment", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateComment"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Comment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/performance-analytics/comments/{comment_id}": {"put": {"tags": ["comments"], "summary": "Update Comment", "description": "Update a comment with the information specified in the body.\n\nSee Comment schema for more details.\n\n- **body**: contains all the information needed to update the comment\n- **commentId**: required to specify the comment to be updated", "operationId": "update_comment", "parameters": [{"name": "comment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Comment Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Comment"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Comment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["comments"], "summary": "Delete Comment", "description": "Delete a comment by the specified commentId.\n\n- **commentId**: required to specify the comment to be updated", "operationId": "delete_comment", "parameters": [{"name": "comment_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Comment Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeletedComment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Comment": {"properties": {"equipmentId": {"type": "string", "title": "Equipmentid"}, "kpiModelKey": {"type": "string", "title": "Kpi<PERSON>del<PERSON>"}, "startDate": {"type": "integer", "title": "Startdate"}, "endDate": {"type": "integer", "title": "Enddate"}, "creatorId": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "category": {"type": "string", "title": "Category"}, "text": {"type": "string", "title": "Text"}, "title": {"type": "string", "title": "Title"}, "attachments": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Attachments"}, "commentId": {"type": "string", "title": "Commentid"}, "creationDate": {"type": "integer", "title": "Creationdate"}, "changeDate": {"type": "integer", "title": "Changedate"}}, "type": "object", "required": ["equipmentId", "kpiModelKey", "startDate", "endDate", "creatorId", "category", "text", "title", "commentId", "creationDate", "changeDate"], "title": "Comment"}, "CreateComment": {"properties": {"equipmentId": {"type": "string", "title": "Equipmentid"}, "kpiModelKey": {"type": "string", "title": "Kpi<PERSON>del<PERSON>"}, "startDate": {"type": "integer", "title": "Startdate"}, "endDate": {"type": "integer", "title": "Enddate"}, "creatorId": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "category": {"type": "string", "title": "Category"}, "text": {"type": "string", "title": "Text"}, "title": {"type": "string", "title": "Title"}, "attachments": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Attachments"}}, "type": "object", "required": ["equipmentId", "kpiModelKey", "startDate", "endDate", "creatorId", "category", "text", "title"], "title": "CreateComment"}, "DeletedComment": {"properties": {"commentId": {"type": "string", "title": "Commentid"}}, "type": "object", "required": ["commentId"], "title": "DeletedComment"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}, "tags": [{"name": "comments", "description": "CRUD operations with comments"}]}