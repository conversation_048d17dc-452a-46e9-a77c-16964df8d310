from sqlalchemy import BigI<PERSON>ger, Column, Text
from sqlalchemy_utils import ScalarListType

from .configuration import Base


class CommentEntity(Base):
    __tablename__ = "comments"

    comment_id = Column(Text, primary_key=True, index=True)
    equipment_id = Column(Text, nullable=False, index=True)
    kpi_model_key = Column(Text, nullable=False, index=True)
    customer = Column(Text, nullable=False)
    start_date = Column(BigInteger, nullable=False)
    end_date = Column(BigInteger, nullable=False)
    creator_id = Column(Text, nullable=False)
    text = Column(Text, nullable=False)
    creation_date = Column(BigInteger, nullable=False)
    change_date = Column(BigInteger, nullable=False)
    category = Column(Text, nullable=False)
    title = Column(Text, nullable=False)
    attachments = Column(ScalarListType())

    def __repr__(self) -> str:
        return f"""
        Comment(
            comment_id={self.comment_id},
            equipment_id={self.equipment_id},
            creator_id={self.creator_id},
            start_date={self.start_date},
            end_date={self.end_date},
            text={self.text},
            category={self.category},
            customer={self.customer}
        )"""

    def __eq__(self, other: object) -> bool:
        return (
            isinstance(other, CommentEntity)
            and self.comment_id == other.comment_id
            and self.start_date == other.start_date
            and self.end_date == other.end_date
            and self.equipment_id == other.equipment_id
            and self.kpi_model_key == other.kpi_model_key
            and self.creator_id == other.creator_id
            and self.text == other.text
            and self.creation_date == other.creation_date
            and self.change_date == other.change_date
            and self.category == other.category
            and self.title == other.title
            and self.customer == other.customer
            and self.attachments == other.attachments
        )
