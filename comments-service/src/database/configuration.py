import json
import os

import boto3
from botocore.exceptions import ClientError
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

CLIENT_SECRET_ARN = os.environ.get("CLIENT_SECRET_ARN", "arn:aws:secretsmanager:us-east-1:123456789012:secret:dummy")
DB_NAME = os.environ.get("DB_NAME", "performance")

secretsmanager_client = boto3.client("secretsmanager")

if CLIENT_SECRET_ARN.endswith("dummy"):
    secret = {
        "username": "postgres",
        "password": "example",
        "engine": "postgres",
        "host": "db",
        "port": 5432,
        "dbClusterIdentifier": "dummy",
    }
else:
    try:
        response = secretsmanager_client.get_secret_value(SecretId=CLIENT_SECRET_ARN)
        if response and response.get("SecretString"):
            secret = json.loads(response["SecretString"])
    except ClientError:
        pass

if secret is None:
    raise RuntimeError("Could not get secret from secretsmanager")

USER = secret["username"]
PASSWORD = secret["password"]
PORT = secret["port"]
HOST = secret["host"]

dialect_and_db_name = f"postgresql://{USER}:{PASSWORD}@{HOST}:{PORT}/{DB_NAME}"

Base = declarative_base()

engine = create_engine(
    dialect_and_db_name,
    echo=True,
)

SessionLocal = sessionmaker(engine)
