# pylint: skip-file
"""Update indexes, set eq_id and kpi model instead of start and end date

Revision ID: 314778d32aa0
Revises: b0c0fe96fa77
Create Date: 2021-02-25 11:31:46.875319

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "314778d32aa0"
down_revision = "b0c0fe96fa77"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_comments_end_date", table_name="comments")
    op.drop_index("ix_comments_start_date", table_name="comments")
    op.create_index(op.f("ix_comments_equipment_id"), "comments", ["equipment_id"], unique=False)
    op.create_index(op.f("ix_comments_kpi_model_key"), "comments", ["kpi_model_key"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_comments_kpi_model_key"), table_name="comments")
    op.drop_index(op.f("ix_comments_equipment_id"), table_name="comments")
    op.create_index("ix_comments_start_date", "comments", ["start_date"], unique=False)
    op.create_index("ix_comments_end_date", "comments", ["end_date"], unique=False)
    # ### end Alembic commands ###
