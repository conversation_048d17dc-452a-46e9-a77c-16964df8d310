# pylint: skip-file
"""Create comments table

Revision ID: b0c0fe96fa77
Revises:
Create Date: 2021-02-18 17:37:07.883789

"""
import sqlalchemy as sa
import sqlalchemy_utils

from alembic import op

# revision identifiers, used by Alembic.
revision = "b0c0fe96fa77"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "comments",
        sa.Column("comment_id", sa.Text(), nullable=False),
        sa.Column("start_date", sa.BigInteger(), nullable=False),
        sa.Column("end_date", sa.BigInteger(), nullable=False),
        sa.Column("equipment_id", sa.Text(), nullable=False),
        sa.Column("kpi_model_key", sa.Text(), nullable=False),
        sa.Column("creator_id", sa.Text(), nullable=False),
        sa.Column("text", sa.Text(), nullable=False),
        sa.Column("creation_date", sa.BigInteger(), nullable=False),
        sa.Column("change_date", sa.BigInteger(), nullable=False),
        sa.Column("category", sa.Text(), nullable=False),
        sa.Column("title", sa.Text(), nullable=True),
        sa.Column(
            "attachments", sqlalchemy_utils.types.scalar_list.ScalarListType(), nullable=True
        ),
        sa.PrimaryKeyConstraint("comment_id"),
    )
    op.create_index(op.f("ix_comments_comment_id"), "comments", ["comment_id"], unique=False)
    op.create_index(op.f("ix_comments_end_date"), "comments", ["end_date"], unique=False)
    op.create_index(op.f("ix_comments_start_date"), "comments", ["start_date"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_comments_start_date"), table_name="comments")
    op.drop_index(op.f("ix_comments_end_date"), table_name="comments")
    op.drop_index(op.f("ix_comments_comment_id"), table_name="comments")
    op.drop_table("comments")
    # ### end Alembic commands ###
