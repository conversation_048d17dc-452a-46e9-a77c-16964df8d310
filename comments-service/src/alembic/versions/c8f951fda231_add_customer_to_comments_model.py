# pylint: skip-file
"""add customer to comments model

Revision ID: c8f951fda231
Revises: 314778d32aa0
Create Date: 2021-03-02 13:28:19.734943

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "c8f951fda231"
down_revision = "314778d32aa0"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("comments", sa.Column("customer", sa.Text(), nullable=True))

    op.execute("UPDATE comments SET customer = 'readykit-replay'")
    op.alter_column("comments", "customer", nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("comments", "customer")
    # ### end Alembic commands ###
