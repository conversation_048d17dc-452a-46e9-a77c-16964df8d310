"""Make title non nullable

Revision ID: bedc6ef6f04d
Revises: c8f951fda231
Create Date: 2024-04-15 09:15:53.549882

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "bedc6ef6f04d"
down_revision = "c8f951fda231"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("comments", "title", nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("comments", "title", nullable=True)
    # ### end Alembic commands ###
