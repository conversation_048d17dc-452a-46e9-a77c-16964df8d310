## Use Openapi Generator To Generate Frontend Client Models

There is already a documentation here:
https://pd.bitbucket.syskron.com/projects/S2A_PERF/repos/unit-of-measurement-cache/browse 

There are a few differences for the comments service.

### First setup
Add necessary packages to `Dockerfile`
```
RUN npm -g i \
    @openapitools/openapi-generator-cli \
    @semantic-release/exec@6 \
    @semantic-release/git@10 \
    conventional-changelog-conventionalcommits \
    semantic-release@19
    
RUN curl -fsSL https://apt.corretto.aws/corretto.key | gpg --dearmor -o /etc/apt/keyrings/corretto-keyring.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/corretto-keyring.gpg] https://apt.corretto.aws stable main" | tee /etc/apt/sources.list.d/corretto.list \
    && apt-get update && apt-get install -y java-17-amazon-corretto-jdk
```

To generate the specification file and the frontend client, add the following commands to the `Makefile`:
```makefile
.PHONY: generate-openapi
generate-openapi:
	@echo "███ Generating openapi.json..."
	export PYTHONPATH="${PYTHONPATH}:./src" \
	&& poetry run python src/api/documentation/openapi_generator.py
	@echo

.PHONY: generate-client
generate-client: generate-openapi
	@echo "███ Generating frontend client..."
	openapi-generator-cli generate -c openapitools.json -o client/frontend

	@echo
```

Create [openapitools.json](openapitools.json) in the project directory and change the project specific parts 
like `inputSpec` and `npmName`.

Create [openapi_generator.py](src%2Fapi%2Fdocumentation%2Fopenapi_generator.py) in the project directory and add the following code:
```python
"""Generates openAPI specification file from FastAPI app."""

import json
from pathlib import Path

from main import app # change it to the name of the FastAPI app

if __name__ == "__main__":
    openapi_schema = app.openapi()

    script_dir = Path(__file__).resolve().parent

    with open(script_dir / "openapi.json", "w", encoding="utf-8") as f:
        json.dump(openapi_schema, f, indent=4)
```

---
### Generate frontend client manually
All the commands below will be triggered and run by semantic-release. However, it is possible to generate the client for
development purposes.


Build and enter to the container

```shell
 make docker-build service=comments-service
 make docker-shell service=comments-service
```

Run make bundle
```shell
make bundle
```

Generate openapi.json file. This file will be used as an input to generate the frontend client.

```shell
make generate-openapi
```


Generate frontend client.It will create frontend client under `client/frontend` 

```shell
make generate-client
```

Run npm install in `client/frontend` directory to make sure client is generated without an issue 

```shell
npm install
```
---
### Setup CI

Create [.releaserc](.releaserc) in the project directory. It will be used in CI for semantic release. 
Change `repositoryUrl` with the correct repository url.

Add the `CI` environment variable to your `ci.yml` under the environment section and `./.npmrc:/root/.npmrc` under the volume section:
```
    volumes:
      - ./.npmrc:/root/.npmrc
    environment:
      - CI=1
```

Use the credentials functions in your `Build Image` stage in the `Jenkinsfile`:
```
fn.prepareCredentialsSsh()
fn.prepareCredentialsNpm()
fn.setNpmRegistry(serviceName)
```

Next, add the generation of the openapi specification file and `semantic-release` to the `Jenkinsfile`:
```
stage('Deploy Frontend Client') {
    when {
    branch 'test'
    }
    steps {
        script {
            def serviceName = "${env.SERVICE_NAME}"
            def branchName = "${env.BRANCH_NAME}"
            def openApiCmd = '/bin/bash -c " git config --global --add safe.directory ' + "${env.WORKSPACE}" + ' && git config --global --add safe.directory /app && make generate-openapi"'
            def semanticReleaseCmd = '/bin/bash -c " git config --global --add safe.directory ' + "${env.WORKSPACE}" + ' && git config --global --add safe.directory /app && npx semantic-release"'
            withCredentials(bindings: [usernamePassword(credentialsId: 'artifactory-s2a-jenkins', usernameVariable: 'PYPI_USER', passwordVariable: 'PYPI_PASS')]) {
                fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), openApiCmd)
                fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), semanticReleaseCmd)
            }
        }
    }
}
```

In new project structure, setting up Jenkins is more straightforward. You can use the following code snippet in your `Jenkinsfile`:

```text
performancePipe(
  serviceName: 'project-name',
  withClient: true,
  install: customInstall)

```

Manually add a tag to one of the previous commits on bitbucket (Make sure it is on test branch)

```text
v1.0.1
```


#### Test the setup on your feature branch

To test the setup on you feature branch, modify the .releaserc file and Jenkinsfile as follows:


[.releaserc](.releaserc)
```text
{
  "branches": ["your feature branch name"],
   ....
   ....
   ....
}


```
Jenkinsfile (assuming using the performancePipe function)
```text

def customFrontendClientInstall = {
    def branchName = "${env.BRANCH_NAME}"
    def serviceName = "${env.SERVICE_NAME}"
    def gitConfigCmd = '/bin/bash -c "set -e; git config --global --add safe.directory ' +
    "${env.WORKSPACE}" + ' && git config --global --add safe.directory /app'
    String openApiCmd = gitConfigCmd + ' && make generate-openapi"'
    String semanticReleaseCmd = gitConfigCmd + ' && npx semantic-release"'
    withCredentials(bindings: [usernamePassword(credentialsId: 'artifactory-s2a-jenkins', usernameVariable: 'PYPI_USER', passwordVariable: 'PYPI_PASS')]) {
        fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), openApiCmd)
        fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), semanticReleaseCmd)
    }
}

performancePipe(
  serviceName: 'kpi-config-service',
  withClient: true,
  install: customInstall,
  deployClient: customFrontendClientInstall)

```

In this case, you need a tag on your feature branch.

It is also possible to run semantic release on your local without triggering any real operation
on Jenkins
  
```bash
npx semantic-release --noop
```

---

### Use The Generated Models In The Frontend
Follow the instructions [here](https://pd.bitbucket.syskron.com/projects/s2a_perf/repos/unit-of-measurement-cache/browse/README.md?at=ee5866df5ebddbb125e1cac03bccc8acbcd68f04&useDefaultHandler=true#295)
