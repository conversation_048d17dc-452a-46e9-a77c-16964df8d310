#!/bin/bash

# Initialization logic
poetry run alembic --config src/alembic.ini upgrade head

# Start the program with its arguments passed by `docker-compose` as the parameters to this entrypoint
if [ $# == 0 ]
then
  echo "Assuming execution from shell"
  PS1='🐳  \[\033[1;36m\]\u@\h \[\033[1;34m\]\w\[\033[0;35m\]\n  \[\033[1;36m\]# \[\033[0m\]' bash
else
  echo "Assuming execution from Makefile"
  make test
fi
