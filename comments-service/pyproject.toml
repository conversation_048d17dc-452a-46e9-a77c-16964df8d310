#POETRY

[tool.poetry]
name = "comments-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]
package-mode = false

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
wrapt = "^1"
alembic = "^1.5"
fastapi = "^0"
mangum = "^0"
SQLAlchemy = "^1.3"
SQLAlchemy-Utils = "^0"
ft2-cloud-sdk = "^16"
psycopg2-binary = "*"
Secweb = "*"
lib-s2a-events-v3 = "^35"
lib-public-api-utilities = "^7"
lib-kpi-config-client = "^3.9"
lib-equipment-cache-common = "^2"
lib-performance-analytics = "^33"

[tool.poetry.group.dev.dependencies]
pre-commit = "*"
boto3 = "^1.28"
pytest = "^7"
pytest-cov = "*"
pytest-mock = "^3"
mock = "^5"
moto = "^4"
httpx = "*"
uvicorn = "^0.27"  # pin to that version due to "h11" not beeing installed otherwise
poetry-plugin-export = "^1.8"
toml = "^0"
mypy = "^1"
ruff = "^0"
boto3-stubs = { extras= ["essential"], version="^1"}
sqlalchemy-stubs = "*"
types-sqlalchemy-utils = "*"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

[build-system]
requires = ["poetry-core>=2.0.0", "aws-lambda-powertools", "iso8601"]
build-backend = "poetry.core.masonry.api"

#PYTEST

[tool.pytest.ini_options]
addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes --junitxml=../test-reports/report/comments-service-cov.xml"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning",
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = ["src"]
testpaths = ["test"]

#COVERAGE

[tool.coverage.run]
branch = true
omit = ["test/*", "*/__init__.py", "*/_version.py"]

[tool.coverage.report]
precision = 2
fail_under = 92

[tool.coverage.xml]
output = "../test-reports/coverage/comments-service-cov.xml"

#MYPY
[tool.mypy]
incremental = true
cache_dir = ".mypy_cache"
python_version = "3.13"
disallow_untyped_defs = true
follow_imports = "silent"
disallow_untyped_calls = true
disallow_incomplete_defs = true
exclude = ["test", "src/alembic/*"]
mypy_path = ["src"]
namespace_packages =  true
explicit_package_bases = true
plugins = [
  "pydantic.mypy"
]

[[tool.mypy.overrides]]
module = "Secweb.*"
ignore_missing_imports = true

#RUFF
[tool.ruff]
src = ["src", "test"]
target-version = 'py313'
extend = "../.configs/ruff.toml"
exclude = ["*alembic*"]

[tool.ruff.lint.isort]
known-local-folder = ["test"]
