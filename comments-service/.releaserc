{
  "branches": ["test"],
  "plugins":
  [
    [
      "@semantic-release/commit-analyzer",
      {
        "preset": "conventionalcommits",
        "presetConfig": {
          "commitUrlFormat": "{{host}}/projects/S2A_PERF/repos/{{repository}}/commits/{{hash}}",
          "compareUrlFormat": "{{host}}/projects/S2A_PERF/repos/{{repository}}/compare/diff?sourceBranch=refs%2Ftags%2F{{currentTag}}&targetBranch=refs%2Ftags%2F{{previousTag}}"
        }
      },
    ],
    [
      "@semantic-release/exec",
      {
        "prepareCmd": "cd client/frontend && npm install && npm run build && npm --no-git-tag-version version ${nextRelease.version}"
      }
    ],
    [
      "@semantic-release/git",
      {
        "assets": [
          "client/frontend/package.json",
          "client/frontend/package-lock.json"
        ],
        "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"
      }
    ],
    [
      "@semantic-release/npm",
        {
          "npmPublish": true,
          "pkgRoot": "client/frontend"
        }
      ]
  ],
  "repositoryUrl": "ssh://****************************/s2a_perf/performance-analytics-service.git"
}
