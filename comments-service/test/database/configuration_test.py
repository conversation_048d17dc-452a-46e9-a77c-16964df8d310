import json
import os

import boto3
from moto import mock_secretsmanager


@mock_secretsmanager
def test_configuration_secret():
    os.environ["CLIENT_SECRET_ARN"] = "arn:aws:secretsmanager:us-east-1:123456789012:secret:testing"
    secretsmanager_client = boto3.client("secretsmanager")

    secret = json.dumps(
        {
            "username": "testing",
            "password": "testing",
            "engine": "testing",
            "host": "testing",
            "port": "5432",
            "dbClusterIdentifier": "testing",
        }
    )
    secretsmanager_client.create_secret(Name="testing", SecretString=secret)

    from dependencies.database_session import get_session

    get_session()


def test_configuration_dummy():
    os.environ["CLIENT_SECRET_ARN"] = "arn:aws:secretsmanager:us-east-1:123456789012:secret:dummy"
    from dependencies.database_session import get_session

    session = get_session()
    assert session is not None
