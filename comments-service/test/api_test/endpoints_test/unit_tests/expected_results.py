NOT_UPDATED_COMMENT = {
    "equipmentId": "some equipment id",
    "kpiModelKey": "some kpi model",
    "startDate": 1,
    "endDate": 5,
    "creatorId": "some creator id",
    "category": "some category",
    "text": "some text",
    "title": "some title",
    "attachments": ["some uuid for attachment"],
    "commentId": "some-uuid",
    "creationDate": 1,
    "changeDate": 1,
}

UPDATED_COMMENT = {
    "equipmentId": "some equipment id",
    "kpiModelKey": "some kpi model",
    "startDate": 1,
    "endDate": 5,
    "creatorId": "some creator id",
    "category": "some category",
    "text": "some text UPDATED",
    "title": "some title UPDATED",
    "attachments": ["some uuid for attachment UPDATED"],
    "commentId": "some-uuid",
    "creationDate": 1,
    "changeDate": 15,
}

DELETED_COMMENT = {
    "commentId": "some-uuid",
}

EXPECTED_RESULT_GET_ALL = [NOT_UPDATED_COMMENT]
