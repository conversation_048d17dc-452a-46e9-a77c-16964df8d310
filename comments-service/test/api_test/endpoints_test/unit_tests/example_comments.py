from api.api_schemas.schemas import Comment

comment = Comment(
    comment_id="some-uuid",
    equipment_id="some equipment id",
    kpi_model_key="some kpi model",
    start_date=1,
    end_date=5,
    creator_id="some creator id",
    category="some category",
    text="some text",
    title="some title",
    attachments=["some uuid for attachment"],
    creation_date=1,
    change_date=1,
)


comment_updated = Comment(
    comment_id="some-uuid",
    equipment_id="some equipment id",
    kpi_model_key="some kpi model",
    start_date=1,
    end_date=5,
    creator_id="some creator id",
    category="some category",
    text="some text UPDATED",
    title="some title UPDATED",
    attachments=["some uuid for attachment UPDATED"],
    creation_date=1,
    change_date=15,
)
