from fastapi import Request
import pytest


@pytest.fixture
def request_scope_mock():
    event = {
        "aws.event": {
            "requestContext": {
                "authorizer": {
                    "claims": '{"scopes":["performance-analytics","scope-2"]}',
                    "principalId": "some-principal-id",
                    "user": '{"userId": "user_id", "user_groups": ["site-manager"]}',
                    "account": '{"accountId": "account_id"}',
                }
            }
        },
        "type": "http",
        "headers": "some headers",
    }
    request = Request(scope=event)
    return request


def test_get_user_id(request_scope_mock):
    from api.endpoints.utils import get_user_id

    user_id = get_user_id(request_scope_mock)

    assert user_id == "user_id"


def test_get_customer(request_scope_mock):
    from api.endpoints.utils import get_customer

    customer = get_customer(request_scope_mock)

    assert customer == "account_id"
