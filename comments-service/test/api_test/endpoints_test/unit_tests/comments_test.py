from fastapi import status
from fastapi.exceptions import ResponseValidationError
from fastapi.testclient import TestClient
import pytest

from api.endpoints.crud import CommentNotFoundError
from services.isc_event_service import IscEventService

from .example_comments import comment
from .expected_results import (
    DELETED_COMMENT,
    EXPECTED_RESULT_GET_ALL,
    NOT_UPDATED_COMMENT,
    UPDATED_COMMENT,
)


def test_get_comments_status_ok(time_mock, get_all_mock, get_customer_mock, session_mock):
    from main import app

    client = TestClient(app)

    response = client.get(
        '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=123&isLine=false'
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == EXPECTED_RESULT_GET_ALL


def test_get_comments_status_raises_500_if_equipment_cache_raises_error(
    time_mock, get_all_mock, get_customer_mock, session_mock, mocker
):
    from lib_equipment_cache_common.exceptions.exceptions import EquipmentClientResponseError

    from main import app

    client = TestClient(app)
    mocker.patch(
        "lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipments_by_line_id",
        side_effect=EquipmentClientResponseError,
    )

    response = client.get(
        '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=123&isLine=true'
    )
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_get_comments_status_raises_404_if_no_machines_are_found_for_line_id(
    time_mock, get_all_mock, get_customer_mock, session_mock, mocker
):
    from lib_equipment_cache_common.exceptions.exceptions import EquipmentNotFoundError

    from main import app

    client = TestClient(app)
    mocker.patch(
        "lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipments_by_line_id",
        side_effect=EquipmentNotFoundError,
    )

    response = client.get(
        '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=123&isLine=true'
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_get_comments_raises_validation_error(
    time_mock,
    get_all_mock_with_validation_errors,
    get_customer_mock,
    session_mock,
):
    from main import app

    client = TestClient(app)

    # return value is set to [ {'missing': 'attributes'} ]. See conftest.
    with pytest.raises(ResponseValidationError) as ex_info:
        assert client.get(
            '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=123&isLine=false'
        )
    assert "11 validation errors:" in str(ex_info.value)


def test_get_comments_is_line(
    time_mock,
    get_equipments_by_line_id_mock,
    get_all_mock,
    get_customer_mock,
    session_mock,
):
    from main import app

    client = TestClient(app)

    # Arrange
    get_equipments_by_line_id_mock.return_value = get_equipments_by_line_id_mock.return_value[:2]
    # Act
    response = client.get(
        '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=123&isLine=true'
    )
    # Verify
    get_equipments_by_line_id_mock.assert_called_once()
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [comment.model_dump(by_alias=True), comment.model_dump(by_alias=True)]


def test_create_comment_status_ok(
    create_mock, get_customer_mock, get_user_id_mock, get_user_groups_mock, session_mock, mocker
):
    from main import app

    client = TestClient(app)

    mocker.patch.object(IscEventService, "publish")
    response = client.post("/v1/performance-analytics/comments", json=comment.model_dump())
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json() == NOT_UPDATED_COMMENT


def test_create_comment_raises_validation_error(
    create_mock_with_validation_errors, get_customer_mock, get_user_id_mock, get_user_groups_mock, session_mock
):
    from main import app

    client = TestClient(app)

    # return value is set to {'missing': 'attributes'}. See conftest.
    with pytest.raises(ResponseValidationError) as ex_info:
        assert client.post("/v1/performance-analytics/comments", json=comment.model_dump())
    assert "11 validation errors" in str(ex_info.value)


@pytest.mark.parametrize(
    ("error", "expected_response"),
    [(PermissionError, status.HTTP_403_FORBIDDEN)],
)
def test_create_comment_returns_correct_http_error_on_failure(
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
    mocker,
    error,
    expected_response,
):
    from main import app

    client = TestClient(app)

    mocker.patch("api.endpoints.comments.create", side_effect=error)
    response = client.post("/v1/performance-analytics/comments", json=comment.model_dump())
    assert response.status_code == expected_response


def test_update_comment_status_ok(
    update_mock,
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
    mocker,
):
    from main import app

    client = TestClient(app)

    mocker.patch.object(IscEventService, "publish")
    response = client.put("/v1/performance-analytics/comments/some-uuid", json=comment.model_dump())
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == UPDATED_COMMENT


def test_update_comment_raises_validation_error(
    update_mock_with_validation_errors,
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
):
    from main import app

    client = TestClient(app)

    # return value is set to {'missing': 'attributes'}. See conftest.
    with pytest.raises(ResponseValidationError) as ex_info:
        assert client.put("/v1/performance-analytics/comments/some-uuid", json=comment.model_dump())
    assert "11 validation errors" in str(ex_info.value)


@pytest.mark.parametrize(
    ("error", "expected_response"),
    [
        (PermissionError, status.HTTP_403_FORBIDDEN),
        (CommentNotFoundError, status.HTTP_404_NOT_FOUND),
    ],
)
def test_update_comment_returns_correct_http_error_on_failure(
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
    mocker,
    error,
    expected_response,
):
    from main import app

    client = TestClient(app)

    mocker.patch("api.endpoints.comments.update", side_effect=error)
    response = client.put("/v1/performance-analytics/comments/some-uuid", json=comment.model_dump())
    assert response.status_code == expected_response


def test_delete_comment_status_ok(
    delete_mock,
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
    mocker,
):
    from main import app

    client = TestClient(app)

    mocker.patch.object(IscEventService, "publish")
    response = client.delete("/v1/performance-analytics/comments/some-uuid")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == DELETED_COMMENT


def test_delete_comment_raises_validation_error(
    delete_mock_with_validation_error,
    get_customer_mock,
    session_mock,
    get_user_id_mock,
    get_user_groups_mock,
):
    from main import app

    client = TestClient(app)

    # return value is set to {'missing': 'attributes'}. See conftest.
    with pytest.raises(ResponseValidationError) as ex_info:
        assert client.delete("/v1/performance-analytics/comments/some-uuid")
    assert "1 validation error" in str(ex_info.value)


@pytest.mark.parametrize(
    ("error", "expected_response"),
    [
        (PermissionError, status.HTTP_403_FORBIDDEN),
        (CommentNotFoundError, status.HTTP_404_NOT_FOUND),
    ],
)
def test_delete_comment_returns_correct_http_error_on_failure(
    get_customer_mock, session_mock, get_user_id_mock, mocker, error, expected_response, get_user_groups_mock
):
    from main import app

    client = TestClient(app)

    mocker.patch("api.endpoints.comments.delete", side_effect=error)
    response = client.delete("/v1/performance-analytics/comments/some-uuid")
    assert response.status_code == expected_response


def test_get_comments_gt_week_raises_error(get_customer_mock, session_mock, mocker):
    from main import app

    client = TestClient(app)

    mocker.patch("time.time", return_value=12345)
    response = client.get(
        '/v1/performance-analytics/comments?equipmentId="test-equipmentId"&kpiModel="test-kpiModel"&startDate=1710028800000&endDate=1710892800000&isLine=false'
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}
