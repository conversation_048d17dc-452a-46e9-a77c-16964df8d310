import logging

from lib_kpi_config_client.models.api_models import ApiKpiModelTimeValidity
from lib_kpi_config_client.models.enums import KpiModelType
from lib_kpi_config_client.utils.constants import KPI_MODEL_DIN_8743, K<PERSON>_MODEL_INSIGHT
import pytest

from api.api_schemas.schemas import Comment, CreateComment
from api.endpoints.crud import (
    CommentNotFoundError,
    _validate_create_permissions_insight,
    create,
    delete,
    get_all,
    update,
)
from database.models import CommentEntity
from dependencies.database_session import get_session

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
logger.setLevel(logging.INFO)


class MockEquipmentClient:
    line_id: str = "mock_line_id"


@pytest.fixture(autouse=True)
def crud_tests_setup(mocker):
    mocker.patch("lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipment_by_id")
    mocker.patch("lib_kpi_config_client.api.api_client.KpiConfigApiClient.get_kpi_models_assigned_at_time")


def create_comment_body():
    """Helper function to create a common comment body."""
    return CreateComment(
        equipment_id="abc",
        kpi_model_key="insight",
        start_date=1,
        end_date=2,
        creator_id="a",
        category="productive",
        text="test",
        title="nanananana",
        attachments=["batman", "robin"],
    )


def assert_comment_fields(comment, expected_body):
    """Helper function to assert common comment fields."""
    assert comment.equipment_id == expected_body.equipment_id
    assert comment.kpi_model_key == expected_body.kpi_model_key
    assert comment.start_date == expected_body.start_date
    assert comment.end_date == expected_body.end_date
    assert comment.creator_id == expected_body.creator_id
    assert comment.category == expected_body.category
    assert comment.text == expected_body.text
    assert comment.title == expected_body.title
    assert comment.attachments == expected_body.attachments


@pytest.fixture(autouse=True)
def database_cleanup():
    session = get_session().__next__()
    session.query(CommentEntity).delete()
    session.commit()
    yield
    session.query(CommentEntity).delete()
    session.commit()


def test_get_all():
    session = get_session().__next__()

    irrelevant_parameters = {
        "creator_id": "a",
        "category": "productive",
        "text": "Hello World",
        "creation_date": 0,
        "change_date": 0,
    }

    comment_1 = CommentEntity(
        comment_id="1",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=2,
        end_date=3,
        customer="readykit-replay",
        attachments=["x", "y"],
        title="title_1",
        **irrelevant_parameters,
    )
    comment_2 = CommentEntity(
        comment_id="2",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=0,
        end_date=2,
        customer="readykit-replay",
        title="title_2",
        **irrelevant_parameters,
    )
    comment_3 = CommentEntity(
        comment_id="3",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=3,
        end_date=5,
        customer="readykit-replay",
        title="title_3",
        **irrelevant_parameters,
    )
    comment_4 = CommentEntity(
        comment_id="4",
        equipment_id="def",
        kpi_model_key="opi",
        start_date=2,
        end_date=3,
        customer="readykit-replay",
        title="title_4",
        **irrelevant_parameters,
    )
    comment_5 = CommentEntity(
        comment_id="5",
        equipment_id="abc",
        kpi_model_key="oee",
        start_date=2,
        end_date=3,
        customer="readykit-replay",
        title="title_5",
        **irrelevant_parameters,
    )
    comment_6 = CommentEntity(
        comment_id="6",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=6,
        end_date=8,
        customer="readykit-replay",
        title="title_6",
        **irrelevant_parameters,
    )
    comment_7 = CommentEntity(
        comment_id="7",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=2,
        end_date=3,
        customer="syskron",
        title="title_7",
        **irrelevant_parameters,
    )

    session.bulk_save_objects([comment_1, comment_2, comment_3, comment_4, comment_5, comment_6, comment_7])
    session.commit()
    response = get_all(
        equipment_id="abc",
        kpi_model="opi",
        start_date=1,
        end_date=4,
        session=session,
        customer="readykit-replay",
    )
    assert (len(response)) == 3
    assert comment_1 in response
    assert comment_2 in response
    assert comment_3 in response


def test_create():
    session = get_session().__next__()

    body = CreateComment(
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=1,
        end_date=2,
        creator_id="a",
        category="productive",
        text="test",
        title="nanananana",
        attachments=["batman", "robin"],
    )

    comment = create(
        body=body, session=session, customer="readykit-replay", user_id="a", user_groups=["admin", "site-manager"]
    )
    comment_in_db = session.query(CommentEntity).first()
    assert comment_in_db == comment
    assert comment.equipment_id == "abc"
    assert comment.kpi_model_key == "opi"
    assert comment.start_date == 1
    assert comment.end_date == 2
    assert comment.creator_id == "a"
    assert comment.category == "productive"
    assert comment.text == "test"
    assert comment.title == "nanananana"
    assert comment.attachments == ["batman", "robin"]


@pytest.mark.parametrize(
    ("user_groups", "should_raise"),
    [
        (["default"], True),
        (["light-admin"], True),
        (["maintenance"], True),
        (["maintenance-manager"], True),
        (["storeman"], True),
        (["storeman-manager"], True),
        (["production-manager"], True),
        (["task-service-manager"], True),
        (["admin"], False),
        (["site-manager"], False),
    ],
    ids=[
        "missing_admin_default",
        "missing_admin_light_admin",
        "missing_admin_maintenance",
        "missing_admin_maintenance_manager",
        "missing_admin_storeman",
        "missing_admin_storeman_manager",
        "missing_admin_production_manager",
        "missing_admin_task_service_manager",
        "with_admin",
        "with_site_manager",
    ],
)
def test_create_insight(user_groups, should_raise):
    session = get_session().__next__()
    body = create_comment_body()

    if should_raise:
        with pytest.raises(PermissionError):
            create(body=body, session=session, customer="readykit-replay", user_id="a", user_groups=user_groups)
    else:
        comment = create(body=body, session=session, customer="readykit-replay", user_id="a", user_groups=user_groups)
        comment_in_db = session.query(CommentEntity).first()

        assert comment_in_db == comment
        assert_comment_fields(comment, body)


def test_validate_create_permissions_only_insight(mocker):
    mocker.patch(
        "lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipment_by_id",
        return_value=MockEquipmentClient(),
    )
    mock_kpi_models = [
        ApiKpiModelTimeValidity(
            kpi_model_id=KPI_MODEL_INSIGHT,
            kpi_model_type=KpiModelType.GLOBAL,
            kpi_model_name=KPI_MODEL_INSIGHT,
            start=0,
        )
    ]
    mocker.patch(
        "lib_kpi_config_client.api.api_client.KpiConfigApiClient.get_kpi_models_assigned_at_time",
        return_value=mock_kpi_models,
    )

    _validate_create_permissions_insight(
        user_id="user-1",
        kpi_model_id=KPI_MODEL_INSIGHT,
        user_groups=[],
        equipment_id="test-equipment-id",
        account="test-account",
        timestamp=1,
    )


def test_validate_create_permissions_multiple_kpi_models(mocker):
    mocker.patch(
        "lib_equipment_cache_common.clients.equipment_client.EquipmentClient.get_equipment_by_id",
        return_value=MockEquipmentClient(),
    )
    mock_kpi_models = [
        ApiKpiModelTimeValidity(
            kpi_model_id=KPI_MODEL_INSIGHT,
            kpi_model_type=KpiModelType.GLOBAL,
            kpi_model_name=KPI_MODEL_INSIGHT,
            start=0,
        ),
        ApiKpiModelTimeValidity(
            kpi_model_id=KPI_MODEL_DIN_8743,
            kpi_model_type=KpiModelType.GLOBAL,
            kpi_model_name=KPI_MODEL_DIN_8743,
            start=0,
        ),
    ]

    mocker.patch(
        "lib_kpi_config_client.api.api_client.KpiConfigApiClient.get_kpi_models_assigned_at_time",
        return_value=mock_kpi_models,
    )

    with pytest.raises(PermissionError):
        _validate_create_permissions_insight(
            user_id="user-1",
            kpi_model_id=KPI_MODEL_INSIGHT,
            user_groups=[],
            equipment_id="test-equipment-id",
            account="test-account",
            timestamp=1,
        )


@pytest.mark.parametrize(
    ("user_id", "creator_id", "user_groups", "should_raise"),
    [
        pytest.param("creator", "creator", ["default"], False, id="default_role_user_and_creator_same"),
        pytest.param("other_user", "creator", ["default"], True, id="default_role_user_and_creator_different"),
        pytest.param("creator", "creator", ["admin"], False, id="admin_role_user_and_creator_same"),
        pytest.param("other_user", "creator", ["admin"], False, id="admin_role_user_and_creator_different"),
        pytest.param("creator", "creator", ["site-manager"], False, id="site_manager_role_user_and_creator_same"),
        pytest.param(
            "other_user", "creator", ["site-manager"], True, id="site_manager_role_user_and_creator_different"
        ),
        pytest.param("creator", "creator", ["maintenance"], False, id="maintenance_role_user_and_creator_same"),
        pytest.param("other_user", "creator", ["maintenance"], True, id="maintenance_role_user_and_creator_different"),
    ],
)
def test_update(user_id, creator_id, user_groups, should_raise):
    session = get_session().__next__()
    # Use create_comment_body to create the initial comment
    initial_comment_body = create_comment_body()
    comment = CommentEntity(
        comment_id="1",
        **initial_comment_body.__dict__,
        customer="readykit-replay",
        creation_date=0,
        change_date=0,
    )
    comment.creator_id = creator_id
    session.add(comment)
    session.commit()

    updated_comment_body = create_comment_body()
    updated_comment_body.creator_id = user_id
    updated_comment_body.equipment_id = "def"
    updated_comment_body.kpi_model_key = "oee"
    updated_comment_body.start_date = 1
    updated_comment_body.end_date = 2
    updated_comment_body.category = "breakdown"
    updated_comment_body.text = "Rabbit"
    updated_comment_body.title = "Carrot"
    updated_comment_body.attachments = ["batman", "robin"]

    if should_raise:
        with pytest.raises(PermissionError):
            update(
                comment_id="1",
                user_id=user_id,
                body=updated_comment_body,
                customer="readykit-replay",
                session=session,
                user_groups=user_groups,
            )
    else:
        updated_comment = update(
            comment_id="1",
            user_id=user_id,
            body=updated_comment_body,
            customer="readykit-replay",
            session=session,
            user_groups=user_groups,
        )
        comment_in_db = session.query(CommentEntity).first()

        assert updated_comment == comment_in_db
        # Validate immutable fields
        assert updated_comment.comment_id == "1"
        assert updated_comment.creation_date == 0
        # set automatically
        assert updated_comment.creator_id == user_id
        assert updated_comment.change_date != 0
        # changed attributes
        assert comment.equipment_id == updated_comment_body.equipment_id
        assert comment.kpi_model_key == updated_comment_body.kpi_model_key
        assert comment.start_date == updated_comment_body.start_date
        assert comment.end_date == updated_comment_body.end_date
        assert comment.category == updated_comment_body.category
        assert comment.text == updated_comment_body.text
        assert comment.title == updated_comment_body.title
        assert comment.attachments == updated_comment_body.attachments


def test_update_not_found():
    session = get_session().__next__()
    body = Comment(
        equipment_id="def",
        kpi_model_key="oee",
        start_date=1,
        end_date=2,
        creator_id="ü",
        category="breakdown",
        text="Rabbit",
        title="Carrot",
        attachments=["batman", "robin"],
        comment_id="2",
        creation_date=1,
        change_date=2,
    )

    with pytest.raises(CommentNotFoundError):
        update(
            comment_id="none",
            user_id="ü",
            body=body,
            customer="readykit-replay",
            session=session,
            user_groups=["admin", "site-manager"],
        )


def test_update_account_mismatch():
    session = get_session().__next__()
    comment = CommentEntity(
        comment_id="1",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=2,
        end_date=3,
        creator_id="ü",
        category="productive",
        text="test",
        creation_date=0,
        change_date=2,
        title="nanananana",
        attachments=["x", "y"],
        customer="readykit-replay",
    )
    session.add(comment)
    session.commit()

    with pytest.raises(PermissionError):
        comment = update(
            comment_id="1",
            user_id="ü",
            body=comment,
            customer="syskron",
            session=session,
            user_groups=["admin", "site-manager"],
        )


def test_update_creator_mismatch_default_role():
    session = get_session().__next__()
    comment = CommentEntity(
        comment_id="1",
        equipment_id="abc",
        kpi_model_key="opi",
        start_date=2,
        end_date=3,
        creator_id="ü",
        category="productive",
        text="test",
        creation_date=0,
        change_date=2,
        title="nanananana",
        attachments=["x", "y"],
        customer="readykit-replay",
    )
    session.add(comment)
    session.commit()

    with pytest.raises(PermissionError):
        comment = update(
            comment_id="1",
            user_id="x",
            body=comment,
            customer="readykit-replay",
            session=session,
            user_groups=["default"],
        )


@pytest.mark.parametrize(
    ("user_id", "user_groups"),
    [
        pytest.param("a", ["default"], id="test_delete_userid_and_creator_same_with_default_role"),
        pytest.param("x", "admin", id="test_delete_userid_and_creator_different_with_admin_role_as_string"),
        pytest.param("x", ["admin"], id="test_delete_userid_and_creator_different_with_admin_role_as_list"),
        pytest.param("a", ["admin"], id="test_delete_userid_and_creator_same_with_admin_role"),
    ],
)
def test_delete(user_id, user_groups):
    session = get_session().__next__()

    irrelevant_parameters = {
        "creator_id": "a",
        "category": "productive",
        "text": "Hello World",
        "creation_date": 0,
        "change_date": 0,
        "equipment_id": "abc",
        "kpi_model_key": "opi",
        "start_date": 2,
        "end_date": 3,
        "customer": "readykit-replay",
        "title": "title_1",
    }

    comment_1 = CommentEntity(comment_id="1", **irrelevant_parameters)
    comment_2 = CommentEntity(comment_id="2", **irrelevant_parameters)
    comment_3 = CommentEntity(comment_id="3", **irrelevant_parameters)

    session.bulk_save_objects([comment_1, comment_2, comment_3])
    session.commit()

    comment = delete(
        comment_id="1", user_id=user_id, customer="readykit-replay", session=session, user_groups=user_groups
    )
    comments_in_db = session.query(CommentEntity).all()

    assert comment == comment_1
    assert comment_1 not in comments_in_db
    assert len(comments_in_db) == 2


def test_delete_not_found():
    session = get_session().__next__()

    with pytest.raises(CommentNotFoundError):
        delete(
            comment_id="none",
            user_id="a",
            customer="syskron-replay",
            session=session,
            user_groups=["admin", "site-manager"],
        )


def test_delete_account_mismatch():
    session = get_session().__next__()

    irrelevant_parameters = {
        "creator_id": "a",
        "category": "productive",
        "text": "Hello World",
        "creation_date": 0,
        "change_date": 0,
        "equipment_id": "abc",
        "kpi_model_key": "opi",
        "start_date": 2,
        "end_date": 3,
        "title": "title_1",
    }

    comment = CommentEntity(comment_id="1", customer="readykit-replay", **irrelevant_parameters)

    session.add(comment)
    session.commit()

    with pytest.raises(PermissionError):
        comment = delete(
            comment_id="1",
            user_id="a",
            customer="syskron-replay",
            session=session,
            user_groups=["admin", "site-manager"],
        )


def test_delete_creator_mismatch_default_role():
    session = get_session().__next__()

    irrelevant_parameters = {
        "creator_id": "a",
        "category": "productive",
        "text": "Hello World",
        "creation_date": 0,
        "change_date": 0,
        "equipment_id": "abc",
        "kpi_model_key": "opi",
        "start_date": 2,
        "end_date": 3,
        "title": "title_1",
    }

    comment = CommentEntity(comment_id="1", customer="readykit-replay", **irrelevant_parameters)

    session.add(comment)
    session.commit()

    with pytest.raises(PermissionError):
        comment = delete(
            comment_id="1", user_id="x", customer="readykit-replay", session=session, user_groups=["default"]
        )
