import json
import os
from unittest.mock import MagicMock, patch

from fastapi import Request
import pytest

from .api_test.endpoints_test.unit_tests.example_comments import (
    comment,
    comment_updated,
)


@pytest.fixture
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["SQS_QUEUENAME"] = "testing"
    os.environ["SQS_URL"] = "https://eu-central-1.queue.amazonaws.com/testing"
    os.environ["AWS_STAGE"] = "testing"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"
    os.environ["EVENTBRIDGE_NAME"] = "my-event-bridge"


@pytest.fixture
def session_mock():
    with patch("api.endpoints.comments.get_session") as db_session_mock:
        db_session_mock = MagicMock()  # noqa: PLW2901
        db_session_mock.return_value = None
        yield db_session_mock


@pytest.fixture
def get_all_mock():
    with patch("api.endpoints.comments.get_all") as get_all_mock:
        get_all_mock.return_value = [comment]
        yield get_all_mock


@pytest.fixture
def get_all_mock_with_validation_errors():
    with patch("api.endpoints.comments.get_all") as get_all_mock_error:
        get_all_mock_error.return_value = [{"missing": "attributes"}]
        yield get_all_mock_error


@pytest.fixture
def create_mock():
    with patch("api.endpoints.comments.create") as create_mock:
        create_mock.return_value = comment
        yield create_mock


@pytest.fixture
def create_mock_with_validation_errors():
    with patch("api.endpoints.comments.create") as create_mock_error:
        create_mock_error.return_value = {"missing": "attributes"}
        yield create_mock_error


@pytest.fixture
def update_mock():
    with patch("api.endpoints.comments.update") as update_mock:
        update_mock.return_value = comment_updated
        yield update_mock


@pytest.fixture
def update_mock_with_validation_errors():
    with patch("api.endpoints.comments.update") as update_mock_error:
        update_mock_error.return_value = {"missing": "attributes"}
        yield update_mock_error


@pytest.fixture
def delete_mock():
    with patch("api.endpoints.comments.delete") as delete_mock:
        delete_mock.return_value = comment
        yield delete_mock


@pytest.fixture
def delete_mock_with_validation_error():
    with patch("api.endpoints.comments.delete") as delete_mock_error:
        delete_mock_error.return_value = {"missing": "attributes"}
        yield delete_mock_error


@pytest.fixture
def get_user_id_mock():
    with patch("api.endpoints.comments.get_user_id") as get_user_id_mock:
        get_user_id_mock.return_value = "Pikachu"
        yield get_user_id_mock


@pytest.fixture
def get_customer_mock():
    with patch("api.endpoints.comments.get_customer") as get_customer_mock:
        get_customer_mock.return_value = "readykit-replay"
        yield get_customer_mock


@pytest.fixture
def get_user_groups_mock():
    with patch("api.endpoints.comments.get_user_groups") as get_user_groups_mock:
        get_user_groups_mock.return_value = ["site-manager"]
        yield get_user_groups_mock


@pytest.fixture
def get_equipments_by_line_id_mock():
    from lib_equipment_cache_common.models.equipment_models import EquipmentFromCache

    with patch("api.endpoints.comments.EquipmentClient.get_equipments_by_line_id") as get_equipments_by_line_id:
        mock_equipment_props = {
            "line_id": "some-line-id",
            "version": 1,
            "techDesc": "some-tech-desc",
            "createdAt": "2021-09-01T00:00:00.000Z",
            "level": "some-level",
            "iconUrl": "some-icon-url",
            "description": "some-description",
            "account": "some-account",
            "updatedAt": "2021-09-01T00:00:00.000Z",
        }
        get_equipments_by_line_id.return_value = [
            EquipmentFromCache(equipment_id="4913fb8b-9012-4e19-87cf-87791f0efbc0", **mock_equipment_props),
            EquipmentFromCache(equipment_id="94ca1ada-f71d-4c31-86e7-0ed3e798b270", **mock_equipment_props),
            EquipmentFromCache(equipment_id="c7ae813a-e1ed-4cb9-9052-b02424f8f612", **mock_equipment_props),
            EquipmentFromCache(equipment_id="d6c91b19-7b0c-4165-88fb-378e90a59f79", **mock_equipment_props),
        ]
        yield get_equipments_by_line_id


@pytest.fixture
def time_mock():
    with patch("time.time") as time_mock:
        time_mock.return_value = 12345
        yield time_mock


_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["scope-1", "scope-2"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps(
            {
                "userId": "some-user-id",
                "username": "some-user-name",
                "login": "some-login",
                "groups": ["group-1", "site-manager"],
            }
        ),
        "account": json.dumps({"accountId": "readykit-replay", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture
def aws_request_mock():
    event = {
        "aws.event": {"requestContext": _request_context},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    return request


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import (
        CommonShare2ActProperties,
    )

    with patch("performance_analytics.utility.s2a_request_wrapper.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock
