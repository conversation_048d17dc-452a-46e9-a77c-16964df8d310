from lib_s2a_events_v3.comments_service.comment_created_broadcast import (
    CommentCreatedBroadcast,
)
from lib_s2a_events_v3.comments_service.comment_deleted_broadcast import (
    CommentDeletedBroadcast,
)
from lib_s2a_events_v3.comments_service.comment_updated_broadcast import (
    CommentUpdatedBroadcast,
)
from lib_s2a_events_v3.exceptions import EventPublicationFailedException
import pytest

from api.api_schemas.schemas import Comment
from services.isc_event_service import (
    CommentCreatedBroadcastEvent,
    CommentDeletedBroadcastEvent,
    CommentForEvent,
    CommentUpdatedBroadcastEvent,
    IscEventService,
)

ORIGINAL_COMMENT = Comment(
    equipmentId="equipmentId",
    kpiModelKey="kpiModelKey",
    startDate=123,
    endDate=456,
    creatorId="creatorId",
    text="text",
    title="title-foo",
    attachments=None,
    commentId="commentId",
    creationDate=890,
    changeDate=1234,
    category="category",
)
MALFORMED_COMMENT = {"missing": "missing"}
ACCOUNT_ID = "accountId"
EXPECTED_COMMENT: CommentForEvent = {
    "account": ACCOUNT_ID,
    "equipmentId": ORIGINAL_COMMENT.equipment_id,
    "kpiModel": ORIGINAL_COMMENT.kpi_model_key,
    "start": ORIGINAL_COMMENT.start_date,
    "end": ORIGINAL_COMMENT.end_date,
    "createdBy": ORIGINAL_COMMENT.creator_id,
    "text": ORIGINAL_COMMENT.text,
    "title": ORIGINAL_COMMENT.title,
    "commentId": ORIGINAL_COMMENT.comment_id,
    "createdAt": ORIGINAL_COMMENT.creation_date,
    "updatedAt": ORIGINAL_COMMENT.change_date,
    "category": ORIGINAL_COMMENT.category,
}


def test_comment_entities_of_all_events_are_the_same():
    create_event_comment: CommentCreatedBroadcast = EXPECTED_COMMENT
    delete_event_comment: CommentDeletedBroadcast = EXPECTED_COMMENT
    updated_event_comment: CommentUpdatedBroadcast = EXPECTED_COMMENT

    assert create_event_comment == delete_event_comment == updated_event_comment


def test_comment_entity_should_be_transformed_correctly():
    transformed_comment = IscEventService._build_event_entity(ORIGINAL_COMMENT, ACCOUNT_ID)

    assert transformed_comment == EXPECTED_COMMENT


@pytest.mark.parametrize(
    ("event_type", "event_class"),
    [
        ("created", CommentCreatedBroadcastEvent),
        ("updated", CommentUpdatedBroadcastEvent),
        ("deleted", CommentDeletedBroadcastEvent),
    ],
)
def test_publish_event_should_succeed(mocker, caplog, event_type, event_class):
    mocker.patch.object(event_class, "publish")

    IscEventService.publish(ORIGINAL_COMMENT, account=ACCOUNT_ID, event_type=event_type)

    assert not caplog.records


@pytest.mark.parametrize(
    ("event_type", "event_class"),
    [
        ("created", CommentCreatedBroadcastEvent),
        ("updated", CommentUpdatedBroadcastEvent),
        ("deleted", CommentDeletedBroadcastEvent),
    ],
)
def test_publish_event_should_log_on_publishing_failure(mocker, caplog, event_type, event_class):
    mocker.patch.object(event_class, "publish", side_effect=EventPublicationFailedException)

    IscEventService.publish(ORIGINAL_COMMENT, account=ACCOUNT_ID, event_type=event_type)

    assert len(caplog.records) == 1
    assert "Error when publishing event" in caplog.records[0].getMessage()


@pytest.mark.parametrize(
    "event_type",
    [("created"), ("updated"), ("deleted")],
)
def test_publish_event_should_log_on_event_creation_failure(caplog, event_type):
    IscEventService.publish(MALFORMED_COMMENT, account=ACCOUNT_ID, event_type=event_type)

    assert len(caplog.records) == 1
    assert "Error when publishing event" in caplog.records[0].getMessage()
