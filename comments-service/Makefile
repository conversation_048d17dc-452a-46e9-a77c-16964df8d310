SHELL:=/bin/bash

.DEFAULT_GOAL := help

GIT_COMMIT = $(shell git rev-parse HEAD)
BRANCH_NAME = $(shell git symbolic-ref --short HEAD)

CWD := $(shell pwd)
SERVICE_PATH = $(shell cd ../; pwd)
SERVICE_NAME ?= $(shell basename $(SERVICE_PATH))
FUNCTION_NAME ?= $(shell basename $(CWD))

RK_BUCKET ?= rk-deploy-jenkins-dev
S3_PATH = $(SERVICE_NAME)/$(BRANCH_NAME)
ZIP_FILE ?= $(FUNCTION_NAME)-$(GIT_COMMIT).zip

# Export variables for sub-makefiles
export TEST_FILES_OR_DIRS := test
export SRC_DIR := src

################################################################
######    Fall back to targets in shared Makefile     ##########
################################################################

# Hack to automatically update submodules
SUBMODULE := $(shell git submodule update --init --recursive)

.PHONY: %
%: Makefile
ifneq (,$(wildcard .configs/Makefile))
# For use in Docker
	@$(MAKE) -e -f .configs/Makefile $@;
else ifneq (,$(wildcard ../.configs/Makefile))
# For use in local
	@$(MAKE) -e -f ../.configs/Makefile $@;
endif

.PHONY: Makefile
Makefile: ;

.PHONY: init
init:
	@echo "Initialization already done with install. Skipping..."

.PHONY: bundle
bundle: clean ## bundles the service
	mkdir -p build/libs/ build/distributions/
	poetry export --output requirements.txt --with-credentials
	poetry run pip install --platform manylinux2014_aarch64 --no-deps -r requirements.txt -t build/libs
	cd build/libs/; zip -qr ../../build/distributions/$(ZIP_FILE) . ; cd ../../
	cd src/; zip -qr ../build/distributions/$(ZIP_FILE) . ; cd ../

.PHONY: clean
clean:
	rm -rf build/
	rm -rf dist/
	rm -f requirements.txt

.PHONY: create-migrations
create-migrations: ## Create migrations using alembic
	poetry run alembic --config src/alembic.ini revision --autogenerate -m "$(m)";

.PHONY: migrate-up
migrate-up: ## Run migrations using alembic
	poetry run alembic --config src/alembic.ini upgrade head;

.PHONY: migrate-down
migrate-down: ## Rollback migrations using alembic
	poetry run alembic --config src/alembic.ini downgrade -1;

.PHONY: generate-openapi
generate-openapi:
	@echo "███ Generating openapi.json..."
	export PYTHONPATH="${PYTHONPATH}:./src" \
	&& poetry run python src/api/documentation/openapi_generator.py
	@echo

.PHONY: generate-client
generate-client: generate-openapi
	@echo "███ Generating frontend client..."
	npx @openapitools/openapi-generator-cli generate -c openapitools.json -o client/frontend

####################################################
# update this specific lambda function manually
update-fkn: upload update-lambda-code

upload:
	aws s3 cp build/distributions/$(ZIP_FILE) s3://$(RK_BUCKET)/$(S3_PATH)/

update-lambda-code:
	aws lambda update-function-code --function-name $(FUNCTION_NAME) --s3-bucket $(RK_BUCKET) --s3-key $(S3_PATH)/$(ZIP_FILE) --no-cli-pager
