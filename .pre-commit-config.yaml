# Apply to all files without commiting:
#   pre-commit run --all-files
# Update the repos in this file:
#   pre-commit autoupdate
fail_fast: false
default_install_hook_types:
  - pre-commit
  - commit-msg
default_stages:
  - pre-commit
repos:
  - repo: local
    hooks:
      - id: pull-configs
        name: Pull latest config updates
        language: system
        entry: make pull-configs
        pass_filenames: false
        always_run: true

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.2
    hooks:
      - id: ruff-format
        name: format
        types: [python]
        require_serial: true

      - id: ruff
        name: lint
        args: ["--exit-non-zero-on-fix", "--fix", "-v"]
        types: [python]
        require_serial: true

  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.19.0
    hooks:
      - id: commitlint
        stages: [commit-msg]
        additional_dependencies:
          [
            "--registry=https://syskronx.jfrog.io/syskronx/api/npm/npm/",
            "@commitlint/config-conventional",
            "@s2a/commitlint-config",
            "conventional-changelog-conventionalcommits",
          ]
        language_version: lts

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files # Prevents giant files from being committed.
      - id: check-yaml # Checks yaml files for parseable syntax.
        args: [--unsafe]
      - id: detect-aws-credentials # Checks for the existence of AWS secrets that you have set up with the AWS CLI.
        args: [--allow-missing-credentials]
      - id: detect-private-key # Checks for the existence of private keys.
