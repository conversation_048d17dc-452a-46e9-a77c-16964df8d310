# Apply to all files without commiting:
#   pre-commit run --all-files
# Update thr repos in this file:
#   pre-commit autoupdate
fail_fast: false
default_language_version:
  python: python3.13
default_install_hook_types:
  - pre-commit
  - commit-msg
repos:
  - repo: local
    hooks:
      - id: check-config-updates
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        name: Check for config updates
        stages: [pre-commit]
        language: system
        entry: make pull-configs
        pass_filenames: false
        always_run: true

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.3
    hooks:
      - id: ruff-format
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        name: format
        stages: [pre-commit]
        types: [python]
        require_serial: true

      - id: ruff
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        name: lint
        stages: [pre-commit]
        args: ["--exit-non-zero-on-fix", "--fix", "-v"]
        types: [python]
        require_serial: true

  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.19.0
    hooks:
      - id: commitlint
        stages: [commit-msg]
        additional_dependencies:
          [
            "--registry=https://syskronx.jfrog.io/syskronx/api/npm/npm/",
            "@commitlint/config-conventional",
            "@s2a/commitlint-config",
            "conventional-changelog-conventionalcommits",
          ]
        language_version: lts

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files # Prevents giant files from being committed.
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
      - id: check-yaml # Checks yaml files for parseable syntax.
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        args: [--unsafe]
      - id: detect-aws-credentials # Checks for the existence of AWS secrets that you have set up with the AWS CLI.
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        args: [--allow-missing-credentials]
      - id: detect-private-key # Checks for the existence of private keys.
        files: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)

  # non-migrated projects
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        exclude: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
        language_version: python3.13
        args: [--line-length=100]
        additional_dependencies: ["click==8.1.3"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [
            # E203 - Black will enforce an equal amount of whitespace around slice operators.
            # Due to this, Flake8 will raise E203 whitespace before ':' warnings.
            #
            # W503 - When breaking a line, Black will break it before a binary operator.
            # This is compliant with PEP 8, but this behaviour will cause flake8 to raise W503
            # line break before binary operator warnings.
            "--max-line-length=100",
            "--ignore=E203, W503",
          ]
        exclude: (test|alembic|performance-analytics-service-config-generator|performance-analytics-service|error-list-service|comments-service)/

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.9.0
    hooks:
      - id: pyupgrade
        exclude: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-merge-conflict # Check for files that contain merge conflict strings.
        exclude: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
      - id: debug-statements # Check for debugger imports and py37+ breakpoint() calls in python source.
        exclude: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
      - id: detect-private-key # Checks for the existence of private keys.
        exclude: (^performance-analytics-service-config-generator/.*)|(^performance-analytics-service/.*)|(^error-list-service/.*)|(^comments-service/.*)
