# Developer-Tooling
This repository contains configuration files that should be identical accross all our backend projects to have a single source of truth for linting, formatting, etc.

It is intended to be added to your repository as a submodule. This means, there is one central config for all projects. It is not possible to change the content of `.configs` for one specific project only.

## Adding central developer configs to your project
***

### Add the submodule
```sh
# remove existing .configs folder
rm -rf .configs
# add submodule
git submodule add --force https://pd.bitbucket.syskron.com/scm/s2a_perf/developer-configs.git .configs
```

Note: this needs to be added with `https` because <PERSON> clones repositories with `https`. See [here](#use-ssh-instead-of-https) if you use `ssh`.

### Makefile
Update your `Makefile` by adding this:

```make
################################################################
######    Fall back to targets in shared Makefile     ##########
################################################################

# Hack to automatically update submodules
SUBMODULE := $(shell git submodule update --init --recursive)

.PHONY: %
%: Makefile
	@$(MAKE) -e -f .configs/Makefile $@

.PHONY: Makefile
Makefile: ;
```

🚀 *Note*: You also have the choice to use `uv` instead of `poetry`. To use `uv`, modify the above from `.configs/Makefile` to `.configs/Makefile-uv`. Note that you will also need to update your `Dockerfile` as well as your `pyproject.toml`. You can take the `pid-service` as reference.
You also may want to set the index credentials via env variables in your shell config. See
- https://docs.astral.sh/uv/configuration/environment/#uv_index_name_password
- https://docs.astral.sh/uv/configuration/environment/#uv_index_name_username


To override default variables that are used in the shared Makefile, see [here](#variables).

It is obligatory to set `TEST_FILES_OR_DIRS` and point it to your `test` directories:
```make
export TEST_FILES_OR_DIRS := "$(TST_DIR)/integration/ $(TST_DIR)/unit/"
```

The `Makefile` in this repository contains targets that are common across all backend projects. You may want to remove those targets from your projects' `Makefile`. This may include the following targets:
- `help`
- `test`
- `init` and `install`
- `lock`
- `lint`, `format` or `black` and the like
- `clean`
- `build`

### pyproject.toml
Add these new requirements to the `dev-dependencies` section:
```toml
toml = "^0"
ruff = "^0"
mypy = "^1"
```

Remove any unused dependencies for formatting and linting, like `pylint` and its extensions, `black`, `flake8`, `isort` etc.

Make sure the `[tool.pytest.ini_options]` sets the `junitxml` option and includes python and test paths. It should look similar to this:
```toml
[tool.pytest.ini_options]
addopts = " -rf --strict --durations 10 --color yes --junitxml=test-reports/report/stream-processor-design-speeds-cov.xml"
pythonpath = ["src"]
testpaths = ["test"]
```

Make sure to declare an output file for `coverage`:
```toml
[tool.coverage.xml]
output = "test-reports/coverage/stream-processor-design-speeds-cov.xml"
```

Add the following configuration for `mypy`:

```toml
[tool.mypy]
incremental = true
cache_dir = ".mypy_cache"
python_version = "3.13"
disallow_untyped_defs = true
follow_imports = "silent"
disallow_untyped_calls = true
disallow_incomplete_defs = true
exclude = ["test"] # things like test, cdk, semantic_release...
mypy_path = "src" # point to the name of your src directory
namespace_packages =  true
explicit_package_bases = true
```
Additionally, if you use `pydantic`, you may want to add this as well:
```toml
plugins = [
  "pydantic.mypy"
]
```

If you want to exclude imports without types from being type checked, include this section for every dependency you want to go without type checking:

```toml
[[tool.mypy.overrides]]
module = "Secweb.*"
ignore_missing_imports = true
```

Settings for `ruff`:

```toml
[tool.ruff]
src = ["src", "test"]
target-version = 'py313'
extend = ".configs/ruff.toml"

[tool.ruff.lint.isort]
known-local-folder = ["src", "test"] # might be necessary if imports are not sorted correctly, depending on the folder layout
```

### Cleanup
You will want to remove any `# pylint: disable...` comments in your code as well as any `# System import`, `# Library import` and `# Application import` comments as they prevent automatic import sorting from working correctly.

### Other Configs

Then, create the symlinks. This will remove `.editorconfig`, `.pre-commit-config.yaml` and `commitlint.config.js` from your project root if present and create symlinks to the respective files in the `config` directory:

```bash
make link-configs
```

These files have their canonical name and location in the project root, which allows the tools to pick up the shared configuration even if it is in the `.configs` subdirectory.

Now everything is set up and you're ready to use these configs.

## Use of central developer configs

### Use SSH instead of HTTPS
Execute this command once on your machine:
```sh
git config --global url."ssh://****************************/".insteadOf "https://pd.bitbucket.syskron.com/scm/"
```

### Update
To pull updates, run
```bash
make pull-configs
```

**Note**: if `pull-configs` fails in the pre-commit hook, add the pulled changes to your commit and try again.

### Linting, formatting and typechecking
Run
- `make format` for formatting
- `make typecheck` for typechecking
- `make lint` for typechecking & linting
- `make lint-fix` for linting & auto-fixes (like import sorting)
- `make lint-unsafe-fix` for linting with (possibly unsafe, but more powerful) autofixes

## Overriding content of the shared Makefile

### Variables
You can override the variables in the shared Makefile by exporting a variable of the same name in the project-specific Makefile:

```make
# this is the project-specific Makefile
# Override the default values for the shared Makefile
export SRC_DIR := src
export TEST_FILES_OR_DIRS := "$(TST_DIR)/integration/ $(TST_DIR)/unit/"
export BUNDLE_DIR := build/libs
export PLATFORM := manylinux2014_aarch64
```

To determine which variables you need to `export` check which ones are used in the targets of `.configs/Makefile` that you want to use. It is only obligatory to export `TEST_FILES_OR_DIRS` as it's used for `pytest`. For the rest, sensible defaults are in place that can be overriden if needed.

### Overriding targets
Simply define the targets you want to override in your project-specific Makefile. The targets in the shared Makefile of the same name will not run.

### Extending targets
Extend targets by adding the following to your top-level Makefile target:

```make
  @$(MAKE) -e -f .configs/Makefile $@
```

Example:
```make
# this is the project-specific Makefile
.PHONY: test
test:	pytest-cdk
# Additionally run the target of the same name in the `.configs/Makefile` as well
	@$(MAKE) -e -f .configs/Makefile $@
```
