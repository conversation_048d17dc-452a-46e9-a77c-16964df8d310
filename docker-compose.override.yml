services:
  test-checkmat-curve-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./checkmat-curve-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-comments-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./comments-service/:/app
    working_dir: /app
    entrypoint:
      - ./docker-entrypoint.sh

  test-custom-database-user-creator:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./custom-database-user-creator/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-customer-settings-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./customer-settings-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-performance-analytics-kpi-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./performance-analytics-kpi-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-performance-analytics-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./performance-analytics-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-performance-analytics-service-config-generator:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./performance-analytics-service-config-generator/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-performance-analytics-units-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./performance-analytics-units-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-products-speeds-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./products-speeds-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash

  test-threshold-service:
    environment:
      AWS_REGION: "eu-central-1"
      AWS_DEFAULT_REGION: "eu-central-1"
      LANG: "C.UTF-8"
      AWS_XRAY_CONTEXT_MISSING: "LOG_ERROR"
      AWS_XRAY_DEBUG_MODE: "TRUE"
      POWERTOOLS_TRACE_DISABLED: 1
    volumes:
      - ./threshold-service/:/app
    working_dir: /app
    stdin_open: true
    tty: true
    entrypoint:
      - /bin/bash
