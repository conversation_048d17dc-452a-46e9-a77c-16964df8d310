@Library('performance/jenkins') _
@Library('syskron/aws@master')
import com.syskronx.aws.ReadyKit
import com.syskronx.aws.Functions
import com.syskronx.aws.SbomBuilder

def fn = new Functions()
def rk = new ReadyKit()

properties([
    parameters([
       booleanParam(name: 'SKIP_JOB', defaultValue: false, description: 'Ignore [skip ci] commit prefix'),
    ])
])

node('arm64'){
  stage('Clone..') {
      cleanWs()
      checkout scm
  }
  try {
    // Repo info
    def REPOSITORY_NAME = 'performance-analytics-service'
    def SERVICE_NAME = 's2a-performance'

    // Lambdas
    def LAMBDA_FKN_PERFORMANCE = 'performance-analytics-service'
    def LAMBDA_FKN_THRESHOLD = 'threshold-service'
    def LAMBDA_FKN_KPI_SERVICE = 'performance-analytics-kpi-service'
    def LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE = 'customer-settings-service'
    def LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE = 'products-speeds-service'
    def LAMBDA_FKN_UNITS_SERVICE = 'performance-analytics-units-service'
    def LAMBDA_FKN_ERROR_LIST = 'error-list-service'
    def LAMBDA_FKN_COMMENTS = 'comments-service'
    def LAMBDA_FKN_CHECKMAT_CURVE = 'checkmat-curve-service'
    def LAMBDA_CONFIG_GENERATOR = 'performance-analytics-service-config-generator'
    def LAMBDA_DB_USER_CREATOR = 'custom-database-user-creator'
    def LAMBDA_LIST="""${LAMBDA_FKN_PERFORMANCE} ${LAMBDA_FKN_THRESHOLD} \
                    ${LAMBDA_FKN_KPI_SERVICE} \
                    ${LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE} ${LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE} \
                    ${LAMBDA_CONFIG_GENERATOR} ${LAMBDA_DB_USER_CREATOR} ${LAMBDA_FKN_UNITS_SERVICE} \
                    ${LAMBDA_FKN_COMMENTS} ${LAMBDA_FKN_CHECKMAT_CURVE} ${LAMBDA_FKN_ERROR_LIST}"""

    // Users
    def RK_USER = ReadyKit.getRkUser("${BRANCH_NAME}")
    def S2A_USER = ReadyKit.getS2AUser("${env.BRANCH_NAME}")
    def RK_BUCKET = ReadyKit.getRkS3Bucket("${BRANCH_NAME}")

    // Env info
    def STAGE = ReadyKit.getRkStage("${BRANCH_NAME}")

    // Aws resources
    def S3_PATH="${REPOSITORY_NAME}/${env.BRANCH_NAME}"
    def CLOUDFORMATION_STACK_NAME = 'performance-analytics-service'
    def CLOUDFORMATION_TEMPLATE_FILE_NAME = "deployment/cloudformation/${CLOUDFORMATION_STACK_NAME}.json"
    def CLOUDFORMATION_VPC_STACK_NAME = 'performance-vpc'
    def CLOUDFORMATION_VPC_TEMPLATE_FILE_NAME = "deployment/cloudformation/${CLOUDFORMATION_VPC_STACK_NAME}.json"
    def CLOUDFORMATION_RDS_STACK_NAME = 'performance-rds'
    def CLOUDFORMATION_RDS_TEMPLATE_FILE_NAME = "deployment/cloudformation/${CLOUDFORMATION_RDS_STACK_NAME}.json"
    def CLOUDFORMATION_PERFORMANCE_DYNAMODB_STACK_NAME = 'performance-dynamodb'
    def CLOUDFORMATION_PERFORMANCE_DYNAMODB_TEMPLATE_FILE_NAME = "deployment/cloudformation/${CLOUDFORMATION_PERFORMANCE_DYNAMODB_STACK_NAME}.json"

    // Stages
    withEnv(["AWS_DEFAULT_REGION=eu-central-1"]) {
      stage('docker') {
        withCredentials(
          bindings: [
            usernamePassword(credentialsId: 'artifactory-s2a-jenkins', usernameVariable: 'PYPI_USER', passwordVariable: 'PYPI_PASS'),
            usernamePassword(credentialsId: "${RK_USER}", usernameVariable: 'AWS_ACCESS_KEY_ID', passwordVariable: 'AWS_SECRET_ACCESS_KEY')
          ]
        ) {
          fn.prepareCredentialsSsh()
          fn.prepareCredentialsNpm()
          withEnv(["PYPI_USER=${PYPI_USER}", "PYPI_PASS=${PYPI_PASS}", "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}"]) {
            sh "docker-compose -f docker-compose.yml -f ci.yml build --no-cache ${REPOSITORY_NAME}"
            fn.setNpmRegistry(REPOSITORY_NAME)
            sh "docker-compose -f docker-compose.yml -f ci.yml up -d ${REPOSITORY_NAME}"
          }
        }
      }
      stage('build') {
        sh 'bash -c "git config --unset-all core.hooksPath"'
        sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"make ci-init\""
        def lambdas = [:]
        "${LAMBDA_LIST}".split().each{ function ->
          lambdas[function] = {
            sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"cd ${function}; if [ \"${function}\" == \"performance-analytics-service-config-generator\" ] || [ \"${function}\" == \"performance-analytics-service\" ] || [ \"${function}\" == \"error-list-service\" ] || [ \"${function}\" == \"comments-service\" ]; then make install-poetry; else make install; fi\""
          }
        }

        parallel lambdas

        sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c" + ' "sed -i -e \"s/%%TIMESTAMP%%/$(date +%s)/g\" ' + "${CLOUDFORMATION_TEMPLATE_FILE_NAME}\""
      }
      stage('test') {
       if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def tests = [:]
          "${LAMBDA_LIST}".split().each{ function ->
            tests[function] = {
                sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"cd ${function}; git config --global --add safe.directory /app; make lint\""
                if ( function == "comments-service")
                {
                  sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"cd ${function}; make migrate-up\""
                }
                sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"cd ${function}; make test\""
            }
          }
          tests["cloudformation"] = {
          sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c \"pip3 install 'cfn-lint>=0.76.1'\""
          sh "docker-compose exec -T ${REPOSITORY_NAME} cfn-lint -i E3686"
          }

          parallel tests

          junit allowEmptyResults: true, testResults: 'test-reports/report/*.xml'
          archiveArtifacts artifacts: 'test-reports/coverage/*.xml'
          recordCoverage(tools: [[parser: 'COBERTURA', pattern: 'test-reports/coverage/**/*.xml']])

          withCredentials([usernamePassword(credentialsId: 'S2A_Jenkins', passwordVariable: 'bitbucket_pass', usernameVariable: 'bitbucket_user')]) {
              publishCoverageToBitbucket steps: this, reportType: "cobertura", path: "test-reports/coverage/*.xml", user: "${bitbucket_user}", pass: "${bitbucket_pass}", commit: fn.getCommit()
          }
        }
      }
      stage('bundle') {
        if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def branchName = "${env.BRANCH_NAME}"
          if (branchName == 'master' || branchName == 'test' || branchName == 'demo' || branchName == 'prod') {
            sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c 'mkdir bundle'"
            sh "docker-compose exec -T ${REPOSITORY_NAME} bash -c 'make bundle'"
          }
        }
      }
      stage('upload') {
        if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def branchName = "${env.BRANCH_NAME}"
          if (branchName == 'master' || branchName == 'test' || branchName == 'demo' || branchName == 'prod') {
            sh "docker-compose exec -T ${REPOSITORY_NAME} aws s3 cp bundle s3://${RK_BUCKET}/${S3_PATH} --recursive"
          }
        }
      }
      stage('update stack') {
        if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def branchName = "${env.BRANCH_NAME}"
          if (branchName == 'master' || branchName == 'test' || branchName == 'demo' || branchName == 'prod') {
            def GIT_COMMIT = (sh(script: "git rev-parse HEAD", returnStdout: true)).trim()
            echo '---- cloudfromation: update vpc stack ----'
            sh """docker-compose exec -T ${REPOSITORY_NAME} aws cloudformation deploy \
                --template-file ${CLOUDFORMATION_VPC_TEMPLATE_FILE_NAME} \
                --stack-name ${CLOUDFORMATION_VPC_STACK_NAME} \
                --s3-bucket ${RK_BUCKET} \
                --capabilities CAPABILITY_NAMED_IAM \
                --no-fail-on-empty-changeset \
                --tags 'SERVICE=${SERVICE_NAME}'

            """

            def message_text_s2a_role = fn.getCloudformationExportWithCredentials(
                "${REPOSITORY_NAME}",
                "${S2A_USER}",
                'PerformanceSSMReadOnlyAccessRoleAPIArn'
            )

            echo '---- cloudfromation: update stack ----'
            def SentryReleaseDate = (sh(script: "date +'%F'", returnStdout: true)).trim()
            sh """docker-compose exec -T ${REPOSITORY_NAME} aws cloudformation deploy \
                --template-file ${CLOUDFORMATION_TEMPLATE_FILE_NAME} \
                --stack-name ${CLOUDFORMATION_STACK_NAME} \
                --parameter-overrides \
                    LambdaPerformanceAnalyticsServiceDeploy=${S3_PATH}/${LAMBDA_FKN_PERFORMANCE}-${GIT_COMMIT}.zip \
                    LambdaThresholdDeploy=${S3_PATH}/${LAMBDA_FKN_THRESHOLD}-${GIT_COMMIT}.zip \
                    LambdaKpiServiceDeploy=${S3_PATH}/${LAMBDA_FKN_KPI_SERVICE}-${GIT_COMMIT}.zip \
                    LambdaCustomerSettingsDeploy=${S3_PATH}/${LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE}-${GIT_COMMIT}.zip \
                    LambdaProductsSpeedsDeploy=${S3_PATH}/${LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE}-${GIT_COMMIT}.zip \
                    LambdaUnitsReportServiceDeploy=${S3_PATH}/${LAMBDA_FKN_UNITS_SERVICE}-${GIT_COMMIT}.zip \
                    LambdaConfigGeneratorDeploy=${S3_PATH}/${LAMBDA_CONFIG_GENERATOR}-${GIT_COMMIT}.zip \
                    LambdaCommentsDeploy=${S3_PATH}/${LAMBDA_FKN_COMMENTS}-${GIT_COMMIT}.zip \
                    LambdaErrorListDeploy=${S3_PATH}/${LAMBDA_FKN_ERROR_LIST}-${GIT_COMMIT}.zip \
                    LambdaCheckmatCurveDeploy=${S3_PATH}/${LAMBDA_FKN_CHECKMAT_CURVE}-${GIT_COMMIT}.zip \
                    RdsStackName=${CLOUDFORMATION_RDS_STACK_NAME} \
                    MessageTextCrossAccountSSMRoleARN=${message_text_s2a_role} \
                    SentryReleaseDate=${SentryReleaseDate} \
                --s3-bucket ${RK_BUCKET} \
                --capabilities CAPABILITY_NAMED_IAM \
                --no-fail-on-empty-changeset \
                --tags 'SERVICE=${SERVICE_NAME}'
            """

            echo '---- cloudfromation: update rds stack ----'
            sh """docker-compose exec -T ${REPOSITORY_NAME} aws cloudformation deploy \
                --template-file ${CLOUDFORMATION_RDS_TEMPLATE_FILE_NAME} \
                --stack-name ${CLOUDFORMATION_RDS_STACK_NAME} \
                --parameter-overrides \
                    PerformanceVPCStack=${CLOUDFORMATION_VPC_STACK_NAME} \
                    DatabaseUserCreatorLambdaDeploy=${S3_PATH}/${LAMBDA_DB_USER_CREATOR}-${GIT_COMMIT}.zip \
                    Env=${STAGE} \
                --s3-bucket ${RK_BUCKET} \
                --capabilities CAPABILITY_NAMED_IAM \
                --no-fail-on-empty-changeset \
                --tags 'SERVICE=${SERVICE_NAME}'
            """
            echo '---- cloudfromation: update dynamoDB stack ----'
            sh """docker-compose exec -T ${REPOSITORY_NAME} aws cloudformation deploy \
            --template-file ${CLOUDFORMATION_PERFORMANCE_DYNAMODB_TEMPLATE_FILE_NAME} \
            --stack-name ${CLOUDFORMATION_PERFORMANCE_DYNAMODB_STACK_NAME} \
            --s3-bucket ${RK_BUCKET} \
            --capabilities CAPABILITY_NAMED_IAM \
            --no-fail-on-empty-changeset\
            --tags 'SERVICE=${SERVICE_NAME}'
            """

            echo '---- cloudfromation: remove old lambda versions ----'
            "${LAMBDA_LIST}".split().each{ function_name ->
              if (function_name != LAMBDA_DB_USER_CREATOR) {
                sh "docker-compose exec -T ${REPOSITORY_NAME} python3 deployment/remove_old_lambda_versions.py ${function_name}"
              }
            }
          }
        }
      }
      stage('run migrations') {
        if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def branchName = "${env.BRANCH_NAME}"
          if (branchName == 'master' || branchName == 'test' || branchName == 'demo' || branchName == 'prod') {
            sh """docker-compose exec -T ${REPOSITORY_NAME} aws lambda invoke --function-name ${LAMBDA_FKN_COMMENTS}-migrate --payload '["--config=alembic.ini", "upgrade", "head"]' output.tmp"""
          }
        }
      }
      stage('Deploy Comments Frontend Client') {
        if (!fn.shouldSkipCiJob(params.SKIP_JOB)) {
          def serviceName = "${REPOSITORY_NAME}"
          def branchName = "${env.BRANCH_NAME}"
          def openApiCmd = '/bin/bash -c " git config --global --add safe.directory ' + "${env.WORKSPACE}" + ' && git config --global --add safe.directory /app && cd comments-service && make generate-client"'
          def semanticReleaseCmd = '/bin/bash -c " git config --global --add safe.directory ' + "${env.WORKSPACE}" + ' && git config --global --add safe.directory /app && cd comments-service && npx semantic-release"'
          withCredentials(bindings: [usernamePassword(credentialsId: 'artifactory-s2a-jenkins', usernameVariable: 'PYPI_USER', passwordVariable: 'PYPI_PASS')]) {
                  fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), openApiCmd)
                  fn.runInDockerComposeWithCredentials(serviceName, rk.getRkUser(branchName), semanticReleaseCmd)
          }
        }
      }
      stage('Create SBOM') {
        def branchName = "${env.BRANCH_NAME}"
        if (
          branchName == 'master' ||
          branchName == 'test' ||
          branchName == 'prod'
        ) {
          def commitHash = sh (script: "git log -n 1 --pretty=format:'%H'", returnStdout: true)

          def sbomTasks = [:]
          "${LAMBDA_LIST}".split().each { function ->
              sbomTasks[function] = {
                  new SbomBuilder().build([
                      architecture: 'arm64',
                      sbomVersion: commitHash,
                      sbomName: function,
                      sbomPublisher: 'E18 - Analytics',
                      s3Path: 's2a_perf/performance-analytics-service/' + function,
                      awsStage: ReadyKit.getAwsStage(env.BRANCH_NAME),
                      projectDir: "./" + function
                  ])
              }
          }
          parallel sbomTasks

        } else {
          echo "Skip SBOM for $BRANCH_NAME"
        }
      }
    }
  }
  finally {
    stage('Cleaning') {
      fn.cleanDockerCompose()
      jiraSendBuildInfo site: 'krones-digital.atlassian.net'
      cleanWs()
    }
  }
}
