# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2022-01-25
- update lib-performance-analytics to version to 10.0.0 [PERF-3806]

## [1.0.1] - 2021-11-9

- update lib-performance-analytics (lib-shift) to version to 6.0.1 [PERF-3498]

## [1.0.0] - 2021-11-4

- upgrade to python 3.9

## [0.10.10] - 2021-03-31

### Added

- feature: Downtimes that are not forcing a subsequent change in Classification to be visualized and counted as one single downtime. We just show the first message.

## [0.10.9] - 2021-2-1

### Changed

- bugfix: filtering of stoppage analysis data

## [0.10.8] - 2020-10-14

- add deployment via Makefile
- bugfix: show correct id.message_nr & text (left side) of stop analysis for DMM

## [0.10.7] - 2020-06-18

- fixing bug in sqs-poller which deleted all preclassified_states

## [0.10.6] - 2020-06-17

- update ft2-machine-data-query version to 1.3.1

## [0.10.5] - 2020-06-02

- corrupt speeds will now be fixed in dynamodb
- update ft2-machine-data-query version to 1.3.0

## [0.10.4] - 2020-05-28

- update ft2-cloud-sdk version to 0.3.7 and ft2-machine-data-query version to 1.2.10

## [0.10.3] - 2020-05-27

- Use Proxy Resource for Products and Speeds endpoints

## [0.10.2] - 2020-05-27

- Renamed Path variable for products endpoint from {machine_id} to {machine} because otherwise API Gateway fails with the following error message:
- A sibling ({machine_id}) of this resource already has a variable path part -- only one is allowed.

## [0.10.1] - 2020-05-15

- update ft2-machine-data-query versions to 1.2.9

## [0.10.0] - 2020-04-20

- update ft2-cloud-sdk versions to 0.3.6
- impl. filtering of short states (< 5 sec.) in losses_detailed and preclassified_states

## [0.9.0] - 2020-03-02

- update lib-kpi-calculation to v 1.1.0
- impl. handling of kpi calculation for multiple nominal speeds

## [0.8.1] - 2020-02-19

- update ft2-cloud-sdk versions to 0.2.7

## [0.8.0] - 2020-02-13

- add (optional) checkmat curve functionality as part of the machine report

## [0.7.0] - 2020-02-05

- use new query lib to enable individual timerange selection

## [0.6.5] - 2020-01-28

- add an endpoint for getting all downtime categories and kpi models

## [0.6.4] - 2020-01-22

- add customer settings service
- add endpoint to get the kpi model for a specified customer and line
- add endpoint to create customer configurations for the line

## [0.6.3] - 2020-01-20

- add a specific endpoint to GET downtime categories for a specific kpi model

## [0.6.2] - 2020-01-16

- add CRUD for threshold service
- add possibility to filter stops based on a time range (max, min)
- add possibility to filter stops based on downtime category

## [0.6.1] - 2019-12-10

- add handling of custom failure mode ids

## [0.6.0] - 2019-12-13

Release for **Sprint 16**

## Added

- Blacklisting Backend Service

## [0.5.3] - 2019-11-29

Release for **Sprint 15**.

### Added

- _Performance Analytics Service:_ Add endpoint for machine report at _performance-analytics/machine-report/performance/_

### Changes to the Machine Report Endpoint

Switching from API endpoint _readykit/kpis/_ to _performance-analytics/machine-report/performance/_ will introduce the following updates to the data contract:

- Remove top level node `losses`, use `reduced_machine_tate.ratio` instead (redundant property)
- Remove node `speeds[].speed` in array, use `speeds[].current_speed` instead (redundant property)
- Always provide `losses_detailed[].message`. When no touch message is available, provide default value `{}`
- Always provide `losses_detailed[].start`, `losses_detailed[].end`, and `losses_detailed[].duration`, unit is milliseconds (consistent behavior)
- Change type for `preclassified_states[].start` and `preclassified_states[].end` to from `string` to `int` to stay consistent with entries in `losses_detailed` (consistent behavior)

### Updated

- messages-lib: use environment variables to determine stage, increases speed by not calling STS anymore
- lib-cloud-sdk: handle end time for last item for live view
- added versioning, alias and description to lambdas

## [0.4.0] - 2019-10-11

Release for **Sprint 12**.

### Bug Fixes

### Added

- _Performance Analytics Service:_ Custom Failure Modes
  - Now it is possible to create custom failure modes and assign different touch messages.
  - By default the corresponding message number is used.
- _Failure Mode Manager:_
  - Check whether touch messages are already in use

### Changed

- _Performance Analytics Service:_
  - Sort failure modes ascending when it was updated at.
  - Add message info + description for detailed view in modal
  - Refactored get_messages due to change in get-messages-lib

## [0.3.1] - 2019-09-23

### Bug Fixes

- _Performance Analytics Service:_ Set the default ZAIT property correctly.

## [0.3.0] - 2019-09-13

### Bug Fixes

- _Performance Analytics Service:_ Improve merging of losses list / pre-classification list

### Changed

- _Performance Analytics Service:_ Improve handling of shift reports from DynamoDB
  - Check consistency of data schema on every report
  - Slice response to requested time range
  - Add ZAIT message texts als Failure Mode name

### Added

- Add configurations for _Demo_ and _Customer_ to _Thresholds Service_

## [0.2.0] - 2019-09-05

Release for **Sprint 10**.

### Added

- _Machine details_ method in Performance Analytics Service
- _Thresholds_ method in Thresholds Service
- Check for feature flags. Check whether the performance analytics service is enabled for the user that sends the request.

### Changed

- Performance Analytics Service with _Overview_ method (replaces _Summary_ method)
  - Authorization and mapping templates
  - Handling of incomplete database documents
  - Improve runtime performance

## [0.1.0] 2019-08-22

Initial release for **Sprint 9**.

### Added

- Performance Analytics Service with _Summary_ method
