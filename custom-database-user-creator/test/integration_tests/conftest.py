import json
import os

import boto3
import crhelper
import pytest
from moto import mock_secretsmanager
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

from common import MOCK_DB_CLUSTER_ID, MOCK_DB_ENGINE, MOCK_DB_HOST, MOCK_DB_NAME, MOCK_DB_PORT, MOCK_PASSWORD, MOCK_USERNAME


@pytest.fixture(scope="class")
def secret_manager_client(aws_credentials):
    with mock_secretsmanager() as secret_manager:
        secretsmanager_client = boto3.client("secretsmanager")

        secret = json.dumps(
            {
                'username': MOCK_USERNAME,
                'password': MOCK_PASSWORD,
                'engine': MOCK_DB_ENGINE,
                'host': MOC<PERSON>_DB_HOST,
                'port': MOCK_DB_PORT,
                'dbClusterIdentifier': MOCK_DB_CLUSTER_ID,
            }
        )
        secret_record = secretsmanager_client.create_secret(Name="testing", SecretString=secret)

        os.environ["MASTER_SECRET_ARN"] = "arn:aws:secretsmanager:eu-central-1:123456789012:secret:testing"

        yield secretsmanager_client


@pytest.fixture(autouse=True)
def database_creation(aws_credentials):
    dialect_and_db_name = f"postgresql+psycopg://{MOCK_USERNAME}:{MOCK_PASSWORD}@{MOCK_DB_HOST}:{MOCK_DB_PORT}/{MOCK_DB_NAME}"

    engine = create_engine(dialect_and_db_name)

    with Session(engine) as database_session:
        yield database_session


@pytest.fixture(autouse=False)
def create_event():
    return {
        "RequestType": "Create",
        "ServiceToken": "fake_arn",
        "ResponseURL": "fake_url",
        "StackId": "fake_stack_id",
        "RequestId": "8abe8138-dff9-4465-87e4-9eeac856ebb6",
        "LogicalResourceId": "ClientDatabaseUser",
        "ResourceType": "Custom::DatabaseUser",
        "ResourceProperties": {
            "ServiceToken": "fake_service_token",
            "SecretName": "performanceclusterclientsecret",
            "Username": "performanceclientuser",
        },
    }


@pytest.fixture(autouse=False)
def update_event():
    return {
        "RequestType": "Update",
        "ServiceToken": "fake_arn",
        "ResponseURL": "fake_url",
        "StackId": "fake_stack_id",
        "RequestId": "8abe8138-dff9-4465-87e4-9eeac856ebb6",
        "LogicalResourceId": "ClientDatabaseUser",
        "ResourceType": "Custom::DatabaseUser",
        "ResourceProperties": {
            "ServiceToken": "fake_service_token",
            "SecretName": "performanceclusterclientsecret_new",
            "Username": "performanceclientuser_new",
        },
        "OldResourceProperties": {
            "ServiceToken": "fake_service_token",
            "SecretName": "performanceclusterclientsecret",
            "Username": "performanceclientuser",
        },
    }


@pytest.fixture(autouse=False)
def delete_event():
    return {
        "RequestType": "Delete",
        "ServiceToken": "fake_arn",
        "ResponseURL": "fake_url",
        "StackId": "fake_stack_id",
        "RequestId": "8abe8138-dff9-4465-87e4-9eeac856ebb6",
        "LogicalResourceId": "ClientDatabaseUser",
        "ResourceType": "Custom::DatabaseUser",
        "ResourceProperties": {
            "ServiceToken": "fake_service_token",
            "SecretName": "performanceclusterclientsecret",
            "Username": "performanceclientuser",
        },
    }


@pytest.fixture
def mocked_send_response(mocker, request):
    real_send = crhelper.CfnResource._send

    _send_response = mocker.Mock()

    def mocked_send(self, status=None, reason="", send_response=_send_response):
        real_send(self, status, reason, send_response)

    crhelper.CfnResource._send = mocked_send

    yield _send_response

    crhelper.CfnResource._send = real_send


def get_response_body(mocked_send_response):
    mocked_send_response.assert_called()
    return mocked_send_response.call_args.args[1]
