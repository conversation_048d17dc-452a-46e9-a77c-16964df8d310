import json
from unittest.mock import Mock, patch

import pytest
from botocore.exceptions import ClientError

from common import MOCK_DB_CLUSTER_ID, MOCK_DB_ENGINE, MOCK_DB_HOST, MOCK_DB_NAME, MOCK_DB_PORT


class TestEvent:
    @patch('crhelper.utils._send_response')
    @patch('crhelper.resource_helper.CfnResource._set_timeout', Mock())
    def test_create_event(self, lambda_context, secret_manager_client, create_event, mocked_send_response):

        from lambda_function import lambda_handler

        lambda_handler(create_event, lambda_context)

        response = secret_manager_client.get_secret_value(SecretId="performanceclusterclientsecret")
        user = json.loads(response.get("SecretString"))

        del user["password"]

        assert user == {
            'username': 'performanceclientuser',
            'engine': MOCK_DB_ENGINE,
            'host': MOCK_DB_HOST,
            'port': MOC<PERSON>_DB_PORT,
            'dbClusterIdentifier': MOC<PERSON>_DB_CLUSTER_ID,
        }

    @patch('crhelper.utils._send_response')
    @patch('crhelper.resource_helper.CfnResource._set_timeout', Mock())
    def test_update_event(self, lambda_context, secret_manager_client, create_event, update_event, mocked_send_response):

        from lambda_function import delete_user, lambda_handler

        for event in [create_event, update_event]:
            lambda_handler(event, lambda_context)

        response = secret_manager_client.get_secret_value(SecretId="performanceclusterclientsecret_new")
        user = json.loads(response.get("SecretString"))

        del user["password"]

        assert user == {
            'username': 'performanceclientuser_new',
            'engine': MOCK_DB_ENGINE,
            'host': MOCK_DB_HOST,
            'port': MOCK_DB_PORT,
            'dbClusterIdentifier': MOCK_DB_CLUSTER_ID,
        }

        # cleanup
        delete_user("performanceclientuser_new", "performanceclusterclientsecret_new")

    @patch('crhelper.utils._send_response')
    @patch('crhelper.resource_helper.CfnResource._set_timeout', Mock())
    def test_delete_event(self, lambda_context, secret_manager_client, create_event, delete_event, mocked_send_response):

        from lambda_function import lambda_handler

        for event in (create_event, delete_event):
            lambda_handler(event, lambda_context)

        with pytest.raises(ClientError) as exc_info:
            secret_manager_client.get_secret_value(SecretId="performanceclientuser")

        assert str(exc_info.typename) == "ResourceNotFoundException"
