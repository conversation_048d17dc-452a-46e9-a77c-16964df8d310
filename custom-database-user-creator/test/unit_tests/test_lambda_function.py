# Copyright (c) 2021, Syskron GmbH. All rights reserved.
import json
import os
from unittest.mock import patch

import boto3
import psycopg
import pytest
from botocore.exceptions import ClientError
from moto import mock_secretsmanager

from common import MOCK_DB_CLUSTER_ID, MOCK_DB_ENGINE, MOCK_DB_HOST, MOCK_DB_PORT


@pytest.fixture(autouse=True)
def mock_settings_env_vars():
    with patch.dict(
        os.environ,
        {
            'DB_NAME': 'db_name',
            'DB_ENGINE': 'db_engine',
            'DB_HOST': 'db_host',
            'DB_PORT': 'db_port',
            'DB_CLUSTER_IDENTIFIER': 'db_cluster_identifier',
        },
    ):
        yield


@mock_secretsmanager
def test_create_connection(aws_credentials):
    secretsmanager_client = boto3.client("secretsmanager", "eu-central-1")

    secret = json.dumps({'username': "testing", 'password': "testing"})
    secret_record = secretsmanager_client.create_secret(Name="testing", SecretString=secret)

    os.environ["MASTER_SECRET_ARN"] = "arn:aws:secretsmanager:eu-central-1:123456789012:secret:testing"

    with patch('psycopg.connect') as mock_connection:
        mock_connection.return_value = psycopg.Connection.connection

        from lambda_function import _create_connection

        connection = _create_connection()

        assert connection is not None


@mock_secretsmanager
def test_create_connection_exception_secret_manager_type_error(aws_credentials):
    os.environ["MASTER_SECRET_ARN"] = "fake_arn"

    with patch('lambda_function.secrets_client.get_secret_value') as mock_secretsmanager:
        mock_secretsmanager.return_value = {"SecretString": None}

        from lambda_function import _create_connection

        with pytest.raises(TypeError) as exc_info:
            _create_connection()

        assert str(exc_info.typename) == "TypeError"


@mock_secretsmanager
def test_create_connection_exception_secret_manager_json_error(aws_credentials):
    os.environ["MASTER_SECRET_ARN"] = "fake_arn"

    with patch('lambda_function.secrets_client.get_secret_value') as mock_secretsmanager:
        mock_secretsmanager.return_value = {"SecretString": "wrong_json"}

        from lambda_function import _create_connection

        with pytest.raises(json.decoder.JSONDecodeError) as exc_info:
            _create_connection()

        assert str(exc_info.typename) == "JSONDecodeError"


@mock_secretsmanager
def test_create_connection_exception_secret_manager_client_error(aws_credentials):
    os.environ["MASTER_SECRET_ARN"] = "fake_arn"

    from lambda_function import _create_connection

    with pytest.raises(ClientError) as exc_info:
        _create_connection()

    assert str(exc_info.typename) == "ResourceNotFoundException"


@mock_secretsmanager
def test_create_connection_exception_psycopg(aws_credentials):
    secretsmanager_client = boto3.client("secretsmanager")

    secret = json.dumps(
        {
            'username': "testing",
            'password': "testing",
            'engine': "testing",
            'host': "testing",
            'port': "5432",
            'dbClusterIdentifier': "testing",
        }
    )
    secret_record = secretsmanager_client.create_secret(Name="testing", SecretString=secret)

    os.environ["MASTER_SECRET_ARN"] = "arn:aws:secretsmanager:eu-central-1:123456789012:secret:testing"

    from lambda_function import _create_connection

    with pytest.raises(psycopg.Error) as exc_info:
        _create_connection()

    assert str(exc_info.typename) == "OperationalError"


@mock_secretsmanager
def test_create_user(aws_credentials):
    secretsmanager_client = boto3.client("secretsmanager")

    with patch('lambda_function._create_connection') as mock_create_connection:
        with patch('lambda_function._close_connection') as mock_close_connection:
            with patch('lambda_function.sql_runner') as mock_sql_runner:
                mock_create_connection.return_value = psycopg.Connection.connection
                mock_close_connection.return_value = None
                mock_sql_runner.return_value = "Success"

                from lambda_function import create_user

                create_user('username', 'testing')

                response = secretsmanager_client.get_secret_value(SecretId="testing")
                user = json.loads(response.get("SecretString"))

                del user["password"]

                assert user == {
                    'username': 'username',
                    'engine': MOCK_DB_ENGINE,
                    'host': MOCK_DB_HOST,
                    'port': MOCK_DB_PORT,
                    'dbClusterIdentifier': MOCK_DB_CLUSTER_ID,
                }


@mock_secretsmanager
def test_delete_user(aws_credentials):
    secretsmanager_client = boto3.client("secretsmanager")

    secret = json.dumps(
        {
            'username': "testing",
            'password': "testing",
            'engine': "testing",
            'host': "testing",
            'port': "5432",
            'dbClusterIdentifier': "testing",
        }
    )
    secretsmanager_client.create_secret(Name="testing", SecretString=secret)

    assert secretsmanager_client.get_secret_value(SecretId="testing") is not None

    with patch('lambda_function._create_connection') as mock_create_connection:
        with patch('lambda_function._close_connection') as mock_close_connection:
            with patch('lambda_function.sql_runner') as mock_sql_runner:
                mock_create_connection.return_value = psycopg.Connection.connection
                mock_close_connection.return_value = None
                mock_sql_runner.return_value = "Success"

                from lambda_function import delete_user

                delete_user('username', 'testing')

                with pytest.raises(ClientError) as exc_info:
                    secretsmanager_client.get_secret_value(SecretId="testing")
                    assert str(exc_info.typename) == "ResourceNotFoundException"
