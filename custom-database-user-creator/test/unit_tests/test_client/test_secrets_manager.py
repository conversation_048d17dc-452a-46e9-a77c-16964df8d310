from mock import <PERSON><PERSON><PERSON>


def test_create():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret_client_mock.exceptions.InvalidRequestException = Exception
    secret = SecretsManagerSecret(secret_client_mock)
    secret.create('name', '{\'value\': 1}')

    secret_client_mock.create_secret.assert_called_once_with(
        Name='name', Tags=[{"Key": "SERVICE", "Value": "s2a-performance"}], SecretString='{\'value\': 1}'
    )


def test_create_retry():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret_client_mock.exceptions.InvalidRequestException = Exception
    secret_client_mock.create_secret.side_effect = [Exception('Error'), 'Success']
    secret = SecretsManagerSecret(secret_client_mock)
    response = secret.create('name', '{\'value\': 1}')

    assert secret_client_mock.create_secret.call_count == 2
    assert response == 'Success'


def test_delete():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret = SecretsManagerSecret(secret_client_mock)
    secret.name = 'name'
    secret.delete(True)

    secret_client_mock.delete_secret.assert_called_once_with(SecretId='name', ForceDeleteWithoutRecovery=True)


def test_get_random_password():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret_client_mock.get_random_password.return_value = {'RandomPassword': 'abc'}
    secret = SecretsManagerSecret(secret_client_mock)
    response = secret.get_random_password(pw_length=16)

    secret_client_mock.get_random_password.assert_called_once_with(PasswordLength=16, ExcludeCharacters='%+~`#$&*()|[{]}:;<>?!\'/@\"\\')
    assert response == 'abc'


def test_get_value():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret_client_mock.get_secret_value.return_value = 'abc'
    secret = SecretsManagerSecret(secret_client_mock)
    secret.name = 'name'
    response = secret.get_value()

    secret_client_mock.get_secret_value.assert_called_once_with(SecretId='name')
    assert response == 'abc'


def test_describe():
    from client.secrets_manager import SecretsManagerSecret

    secret_client_mock = MagicMock()
    secret_client_mock.describe_secret.return_value = 'abc'
    secret = SecretsManagerSecret(secret_client_mock)
    response = secret.describe('name')

    secret_client_mock.describe_secret.assert_called_once_with(SecretId='name')
    assert response == 'abc'
    assert secret.name == 'name'
