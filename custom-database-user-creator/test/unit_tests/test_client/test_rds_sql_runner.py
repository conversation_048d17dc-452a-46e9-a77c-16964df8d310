from unittest.mock import patch

from mock import MagicMock
from psycopg.errors import <PERSON><PERSON><PERSON>


def test_execute_query():
    from client.rds_sql_runner import sql_runner

    pg_client_mock = MagicMock()
    pg_client_mock.execute.side_effect = ['Success']
    response = sql_runner(pg_client_mock, 'EXECUTE ORDER 66')

    assert pg_client_mock.execute.call_count == 1
    assert response == 'Success'
