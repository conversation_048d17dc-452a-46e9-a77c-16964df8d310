import os
import site
from dataclasses import dataclass

import pytest

# we set our site dir to src to have proper package names
MODULE_DIR_PATH = os.path.dirname(os.path.realpath(__file__))
source_dir = os.path.join(MODULE_DIR_PATH, '..', 'src')
test_dir = os.path.join(MODULE_DIR_PATH, '..', 'test')
site.addsitedir(source_dir)
site.addsitedir(test_dir)


from common import MOCK_DB_CLUSTER_ID, MOCK_DB_ENGINE, MOCK_DB_HOST, MOCK_DB_NAME, MOCK_DB_PORT


@pytest.fixture(scope="session")
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_STAGE"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["AWS_REGION"] = "eu-central-1"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"


@pytest.fixture(scope="session")
def lambda_context():
    @dataclass
    class LambdaContext:
        function_name: str = "downtime-service-dummy"
        memory_limit_in_mb: int = 128
        invoked_function_arn: str = "arn:aws:lambda:eu-central-1:1233456789:function:dummy"
        aws_request_id: str = "52fdfc07-2182-154f-163f-5f0f9a621d72"

    return LambdaContext()


@pytest.fixture(autouse=True)
def db_credentials():
    """
    Mocked DB Credentials for moto.
    """

    os.environ["DB_NAME"] = MOCK_DB_NAME
    os.environ["DB_ENGINE"] = MOCK_DB_ENGINE
    os.environ["DB_HOST"] = MOCK_DB_HOST
    os.environ["DB_PORT"] = MOCK_DB_PORT
    os.environ["DB_CLUSTER_IDENTIFIER"] = MOCK_DB_CLUSTER_ID
