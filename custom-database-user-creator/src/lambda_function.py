# Copyright (c) 2020, Syskron GmbH. All rights reserved.
# pylint: disable=wrong-import-position

# System import
import json
import os

from aws_lambda_powertools import Logger
import boto3
import psycopg
from botocore.exceptions import ClientError
from crhelper import CfnResource

from client.rds_sql_runner import sql_runner
from client.secrets_manager import SecretsManagerSecret

LOGGER = Logger()

helper = CfnResource()
secrets_client = boto3.client("secretsmanager")

# env variables
MASTER_SECRET_ARN = os.environ["MASTER_SECRET_ARN"]
DB_NAME = os.environ["DB_NAME"]
DB_ENGINE = os.environ["DB_ENGINE"]
DB_HOST = os.environ["DB_HOST"]
DB_PORT = os.environ["DB_PORT"]
DB_CLUSTER_IDENTIFIER = os.environ["DB_CLUSTER_IDENTIFIER"]


def _create_connection():
    try:
        response = secrets_client.get_secret_value(SecretId=MASTER_SECRET_ARN)
        db_info = json.loads(response.get("SecretString"))
    except ClientError as error:
        LOGGER.exception("Impossible to retrieve the dbinfo")
        raise error
    except TypeError as error:
        LOGGER.warning("Invalid type")
        raise error
    except json.decoder.JSONDecodeError as error:
        LOGGER.warning("Impossible to parse json")
        raise error

    try:
        url = (
            f'postgresql://{db_info.get("username")}:{db_info.get("password")}@'
            + f"{DB_HOST}:{DB_PORT}/{DB_NAME}"
        )
        db_connection = psycopg.connect(conninfo=url, autocommit=True)
    except psycopg.Error as error:
        LOGGER.warning("DB Error: %s", error)
        raise error

    return db_connection


def _close_connection(db_conn: psycopg.Connection):
    if not db_conn.closed:
        db_conn.close()


def lambda_handler(event, context):  # pylint: disable=unused-argument
    LOGGER.debug("REQUEST RECEIVED:\n %s", json.dumps(event))
    helper(event, context)


@helper.create
def create(event, context):  # pylint: disable=unused-argument
    username = event["ResourceProperties"].get("Username")
    secretname = event["ResourceProperties"].get("SecretName")
    validate_args(username, secretname)
    create_user(username, secretname)


@helper.update
def update(event, context):  # pylint: disable=unused-argument
    username = event["ResourceProperties"].get("Username")
    secretname = event["ResourceProperties"].get("SecretName")
    old_username = event["OldResourceProperties"].get("Username")
    old_secretname = event["OldResourceProperties"].get("SecretName")
    delete_user(old_username, old_secretname)
    create_user(username, secretname)


@helper.delete
def delete(event, context):  # pylint: disable=unused-argument
    username = event["ResourceProperties"].get("Username")
    secretname = event["ResourceProperties"].get("SecretName")
    delete_user(username, secretname)


def validate_args(username, secretname):
    if not username or not secretname:
        raise ValueError("Bad request. Parameters missing.")


def create_user(username, secretname):
    # generate secret
    new_secret = SecretsManagerSecret(secrets_client)
    password = new_secret.get_random_password()

    # create SQL User
    statements = (
        f"CREATE USER {username} WITH PASSWORD '{password}';",
        f"GRANT CONNECT ON DATABASE {DB_NAME} TO {username};",
        f"GRANT ALL PRIVILEGES ON SCHEMA public TO {username};",
        f"GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO {username};",
        f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO {username};",
    )
    conn = _create_connection()
    for statement in statements:
        sql_runner(conn, statement)

    secret_value = generate_secret_value(username, password)
    new_secret.create(secretname, secret_value)

    # add secret information to resource
    secret_properties = new_secret.describe()
    helper.Data["SecretArn"] = secret_properties.get("ARN")
    helper.Data["SecretName"] = secret_properties.get("Name")

    _close_connection(conn)


def delete_user(username, secretname):
    # delete SQL User
    statements = (
        "ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE ALL "
        + f"PRIVILEGES ON TABLES FROM {username};",
        f"REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM {username};",
        f"REVOKE ALL PRIVILEGES ON SCHEMA public FROM {username};",
        f"REVOKE CONNECT ON DATABASE {DB_NAME} FROM {username};",
        f"DROP USER {username};",
    )

    conn = _create_connection()
    for statement in statements:
        sql_runner(conn, statement)

    # delete secret
    secret = SecretsManagerSecret(secrets_client)
    secret.name = secretname
    secret.delete(True)

    _close_connection(conn)


def generate_secret_value(username, password):
    return json.dumps(
        {
            "username": username,
            "password": password,
            "engine": DB_ENGINE,
            "host": DB_HOST,
            "port": DB_PORT,
            "dbClusterIdentifier": DB_CLUSTER_IDENTIFIER,
        }
    )
