from aws_lambda_powertools import Logger
import psycopg

LOGGER = Logger()


def sql_runner(db_conn: psycopg.Connection, statement: str):
    """
    Creates a function that runs a SQL statement on an Amazon Aurora cluster.
    Because Amazon Aurora is serverless, the first time it is called the cluster might
    not be ready and will raise a BadRequestException. The runner function catches the
    exception, waits, and retries.
    Runs SQL statements in the specified Amazon Aurora cluster.

    :param db_conn: DB Connection to DB.
    :param statement: The SQL statement to run.
    """

    try:
        return db_conn.execute(statement)
    except psycopg.Error as exception:
        LOGGER.warning("DB Error: %s", exception)
        raise exception
