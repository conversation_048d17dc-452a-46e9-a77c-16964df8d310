import time

from aws_lambda_powertools import Logger
from botocore.exceptions import ClientError

LOGGER = Logger()


class SecretsManagerSecret:
    """Encapsulates Secrets Manager functions."""

    def __init__(self, secretsmanager_client):
        """
        :param secretsmanager_client: A Boto3 Secrets Manager client.
        """
        self.secretsmanager_client = secretsmanager_client
        self.name = None

    def _clear(self):
        self.name = None

    def create(self, name, secret_value):
        """
        Creates a new secret. The secret value can be a string or bytes.

        :param name: The name of the secret to create.
        :param secret_value: The value of the secret.
        :return: Metadata about the newly created secret.
        """
        self._clear()
        tries = 10
        response = None
        while tries > 0:
            try:
                kwargs = {"Name": name, "Tags": [{"Key": "SERVICE", "Value": "s2a-performance"}]}
                if isinstance(secret_value, str):
                    kwargs["SecretString"] = secret_value
                elif isinstance(secret_value, bytes):
                    kwargs["SecretBinary"] = secret_value
                response = self.secretsmanager_client.create_secret(**kwargs)
                self.name = name
                LOGGER.info("Created secret %s.", name)
                break
            except self.secretsmanager_client.exceptions.InvalidRequestException as exception:
                LOGGER.exception(
                    "Got InvalidRequestException. This occurs when you're deleting "
                    "and recreating a secret with the same name. "
                    "Secrets Manager is not ready yet. Waiting and trying again..."
                )
                LOGGER.exception("InvalidRequestException: %s", exception)
                time.sleep(1)
                tries -= 1
            except ClientError:
                LOGGER.exception("Couldn't create secret %s.", name)
                raise
        return response

    def describe(self, name=None):
        """
        Gets metadata about a secret.

        :param name: The name of the secret to load. If `name` is None, metadata about
                     the current secret is retrieved.
        :return: Metadata about the secret.
        """
        if self.name is None and name is None:
            raise ValueError
        if name is None:
            name = self.name
        self._clear()
        try:
            response = self.secretsmanager_client.describe_secret(SecretId=name)
            self.name = name
            LOGGER.info("Got secret metadata for %s.", name)
        except ClientError:
            LOGGER.exception("Couldn't get secret metadata for %s.", name)
            raise

        return response

    def get_value(self, stage=None):
        """
        Gets the value of a secret.

        :param stage: The stage of the secret to retrieve. If this is None, the
                      current stage is retrieved.
        :return: The value of the secret. When the secret is a string, the value is
                 contained in the `SecretString` field. When the secret is bytes,
                 it is contained in the `SecretBinary` field.
        """
        if self.name is None:
            raise ValueError

        try:
            kwargs = {"SecretId": self.name}
            if stage is not None:
                kwargs["VersionStage"] = stage
            response = self.secretsmanager_client.get_secret_value(**kwargs)
            LOGGER.info("Got value for secret %s.", self.name)
        except ClientError:
            LOGGER.exception("Couldn't get value for secret %s.", self.name)
            raise

        return response

    def get_random_password(self, pw_length=16):
        """
        Gets a randomly generated password.

        :param pw_length: The length of the password.
        :return: The generated password.
        """
        try:
            response = self.secretsmanager_client.get_random_password(
                PasswordLength=pw_length, ExcludeCharacters="%+~`#$&*()|[{]}:;<>?!'/@\"\\"
            )
            password = response["RandomPassword"]
            LOGGER.info("Got random password.")
        except ClientError:
            LOGGER.exception("Couldn't get random password.")
            raise

        return password

    def delete(self, without_recovery):
        """
        Deletes the secret.

        :param without_recovery: Permanently deletes the secret immediately when True;
                                 otherwise, the deleted secret can be restored within
                                 the recovery window. The default recovery window is
                                 30 days.
        """
        if self.name is None:
            raise ValueError

        try:
            self.secretsmanager_client.delete_secret(
                SecretId=self.name, ForceDeleteWithoutRecovery=without_recovery
            )
            LOGGER.info("Deleted secret %s.", self.name)
            self._clear()
        except ClientError:
            LOGGER.exception("Deleted secret %s.", self.name)
            raise
