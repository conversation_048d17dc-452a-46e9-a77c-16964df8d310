#POETRY

[tool.poetry]
name = "custom-database-user-creator"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
crhelper = "*"
psycopg = {extras = ["binary"], version = "*"}
aws-lambda-powertools = "^2"

[tool.poetry.group.dev.dependencies]
SQLAlchemy = "*"
SQLAlchemy-Utils = "*"
pre-commit = "*"
boto3 = "^1.35"
pylint = "^3"
pytest = "^7"
pytest-cov = "*"
pytest-mock = "*"
mock = "*"
moto = "^4"
black = "^23"
flake8 = "^6"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"


#BLACK

[tool.black]
line-length = 100

#PYTEST

[tool.pytest.ini_options]
addopts= " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes"
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::ImportWarning",
    "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = "./src"

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 71
