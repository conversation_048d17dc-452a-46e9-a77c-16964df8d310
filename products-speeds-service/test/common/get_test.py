# Copyright (c) 2020, Syskron GmbH. All rights reserved.

import json
import logging
import sys
from json import JSONDecodeError
from typing import Any

import botocore
import pytest
from machine_data_query.models.speeds import (
    DESIGN_SPEEDS,
    SET_SPEEDS,
    SPEEDS,
    SpeedCounterItem,
    SpeedItem,
    Speeds,
)
from unittest.mock import Mock, patch
from moto import mock_aws
from performance_analytics.models.shared_models import LineSettings, MachineSetting, Settings

LOGGER = logging.getLogger(__name__)

QUERY_MACHINE_DATA_MOCK = Mock()
BOTO_MOCK = Mock()
DYNAMO_CONFIGURATIONS = Mock()


def _create_speeds_objects(document: dict[str, Any]) -> tuple[Speeds, Speeds, Speeds]:
    machine_document = list(document.values())[0]
    time_from = machine_document["time_from"]
    time_to = machine_document["time_to"]
    design_speed_items = [SpeedItem(**item) for item in machine_document.get("design_speeds", [])]
    set_speed_items = [SpeedItem(**item) for item in machine_document.get("set_speeds", [])]
    speeds_items = [SpeedCounterItem(**item) for item in machine_document.get("speeds", [])]

    return (
        Speeds(time_from=time_from, time_to=time_to, items=speeds_items, type=SPEEDS),
        Speeds(time_from=time_from, time_to=time_to, items=design_speed_items, type=DESIGN_SPEEDS),
        Speeds(time_from=time_from, time_to=time_to, items=set_speed_items, Type=SET_SPEEDS),
    )


def _read_json_file(file_name: str) -> dict:
    with open(file_name) as file_obj:
        file_data = file_obj.read()
    return json.loads(file_data)


@pytest.fixture()
def query_machine_data_and_speeds():
    QUERY_MACHINE_DATA_MOCK.reset_mock()
    return QUERY_MACHINE_DATA_MOCK


def boto_mock():
    BOTO_MOCK.reset_mock(),
    return BOTO_MOCK


def setup_module(module):
    if "common.get" in sys.modules:
        del sys.modules["common.get"]
    if "common" in sys.modules:
        del sys.modules["common"]
    module.patcher = patch.dict(
        "sys.modules",
        {
            "machine_data_query.query_machine_data": QUERY_MACHINE_DATA_MOCK,
            "machine_data_query.query_speeds": QUERY_MACHINE_DATA_MOCK,
        },
    )
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


MOCK_PRODUCT_TEXTS = {
    "150021": "Sort 1",
    "5": "Doppelweizenbock 0.5L",
}

MOCK_LINE_SETTINGS = LineSettings(
    kpi_model="opi",
    account="test-account",
    line_id="test-line-id",
    created_at=*************,
    updated_at=*************,
    settings=Settings(
        minor_stop_config=5,
        machine_settings={
            "65ea1a06-2698-45eb-baad-3a3e5fc25b73": MachineSetting(nominal_speed=25000),
            "65ea1a06-2698-45eb-baad-3a3e5fc25b74": MachineSetting(nominal_speed=1337),
        },
    ),
)


@pytest.mark.parametrize(
    "input",
    [
        (botocore.exceptions.ClientError({"Error": {"Code": "NoSuchKey"}}, "name")),
        (botocore.exceptions.ClientError({"Error": {"Code": "NoSuchBucket"}}, "name")),
        (botocore.exceptions.ClientError({"Error": {"Code": "MariusIsSuperSmart"}}, "name")),
        (JSONDecodeError("", "", 0)),
        (UnicodeDecodeError("", b"", 1, 2, "")),
    ],
)
@patch("boto3.client", return_value=BOTO_MOCK)
def test_get_product_texts_error_return_empty_texts(boto_mock, input):  # pylint:
    # disable=unused-argument
    BOTO_MOCK.get_object.return_value = None
    BOTO_MOCK.get_object.side_effect = input

    # ensure @patch is patching S3_CLIENT in get.py
    if "common.get" in sys.modules:
        del sys.modules["common.get"]

    from common.get import _get_product_texts

    # act
    result = _get_product_texts("test-account", "65ea1a06-2698-45eb-baad-3a3e5fc25b73")

    # assert
    assert result == {}


@patch("common.get._get_product_texts", return_value=MOCK_PRODUCT_TEXTS)
def test_get_products(text_mock, query_machine_data_and_speeds):  # pylint: disable=unused-argument
    query_machine_data_and_speeds.query_machine_data.return_value = (
        _read_json_file("test/res/products_query_data.json"),
        {"quality_problems": {}, "documents_with_duplicates": {}},
    )

    from common.get import get_products

    # act
    result = get_products(
        "test-account",
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "*************",
        "*************",
        True,
    )

    # assert
    assert result == {
        "products": [
            {
                "start": *************,
                "end": *************,
                "product_id": 150021,
                "product_text": "Sort 1",
            },
            {
                "start": *************,
                "end": *************,
                "product_id": 5,
                "product_text": "Doppelweizenbock 0.5L",
            },
            {
                "start": *************,
                "end": *************,
                "product_id": 404,
                "product_text": 'No text for product="404"',
            },
        ]
    }


def test_get_products_without_texts(
    query_machine_data_and_speeds,
):  # pylint: disable=unused-argument
    query_machine_data_and_speeds.query_machine_data.return_value = (
        _read_json_file("test/res/products_query_data.json"),
        {
            "quality_problems": {},
            "documents_with_duplicates": {},
        },
    )

    from common.get import get_products

    # act
    with patch("common.get._get_product_texts", return_value={}):
        result = get_products(
            "test-account",
            "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
            "*************",
            "*************",
            True,
        )

    # assert
    assert result == {
        "products": [
            {
                "start": *************,
                "end": *************,
                "product_id": 150021,
                "product_text": 'No text for product="150021"',
            },
            {
                "start": *************,
                "end": *************,
                "product_id": 5,
                "product_text": 'No text for product="5"',
            },
            {
                "start": *************,
                "end": *************,
                "product_id": 404,
                "product_text": 'No text for product="404"',
            },
        ]
    }


@patch("common.get._get_product_texts", return_value=MOCK_PRODUCT_TEXTS)
def test_get_products_when_data_is_none(
    text_mock, query_machine_data_and_speeds
):  # pylint: disable=unused-argument
    query_machine_data_and_speeds.query_machine_data.return_value = (
        {},
        {
            "quality_problems": {},
            "documents_with_duplicates": {},
        },
    )

    from common.get import get_products

    # act
    result = get_products(
        "test-account",
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "*************",
        "*************",
        True,
    )

    # assert
    assert result is None


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds(config_mock, query_machine_data_and_speeds):  # pylint: disable=unused-argument
    document = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][0]
    query_machine_data_and_speeds.query_speeds.return_value = _create_speeds_objects(document)
    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )
    # assert
    assert result == {
        "design_speeds": [
            {"speed": 10000, "start": *************, "end": *************, "ongoing": False},
            {"speed": 20000, "start": *************, "end": *************, "ongoing": False},
        ],
        "set_speeds": [
            {"speed": 10000, "start": *************, "end": *************, "ongoing": False},
            {"speed": 18000, "start": *************, "end": *************, "ongoing": False},
        ],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_w_fallback(
    config_mock, query_machine_data_and_speeds
):  # pylint: disable=unused-argument
    document = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][1]
    fallback_document = _read_json_file("test/res/fallback_speeds_query_data.json")
    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document),
        _create_speeds_objects(fallback_document),
    ]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
    )
    # assert
    assert result == {
        "set_speeds": [{"speed": 0, "start": *************, "end": *************}],
        "design_speeds": [{"speed": 25000, "start": *************, "end": *************}],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_nominal_speed_is_none(
    config_mock, query_machine_data_and_speeds
):  # pylint: disable=unused-argument
    document1 = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][1]
    document2 = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][2]
    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document1),
        _create_speeds_objects(document2),
    ]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )
    # assert
    assert result == {
        "design_speeds": [{"speed": 25000, "start": *************, "end": *************}],
        "set_speeds": [{"speed": 0, "start": *************, "end": *************}],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_has_fallback_configured_nominal_speed_in_shift_document(
    config_mock, query_machine_data_and_speeds
):  # pylint: disable=unused-argument
    document = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][1]
    fallback_document = _read_json_file("test/res/fallback_speeds_query_data.json")
    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document),
        _create_speeds_objects(fallback_document),
    ]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )
    # assert
    assert result == {
        "design_speeds": [{"speed": 25000, "start": *************, "end": *************}],
        "set_speeds": [{"speed": 0, "start": *************, "end": *************}],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_has_fallback_configured_nominal_speed_as_default_for_customer_settings(
    config_mock,
    query_machine_data_and_speeds,
):  # pylint: disable=unused-argument
    document = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][1]
    fallback_document = _read_json_file("test/res/fallback_fallback_speeds_query_data.json")

    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document),
        _create_speeds_objects(fallback_document),
    ]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )
    # assert
    assert result == {
        "design_speeds": [{"speed": 25000, "start": *************, "end": *************}],
        "set_speeds": [{"speed": 0, "start": *************, "end": *************}],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_has_configured_nominal_speed_in_customer_settings(
    config_mock,
    query_machine_data_and_speeds,
):  # pylint: disable=unused-argument
    document1 = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][3]
    document2 = _read_json_file("test/res/speeds_query_data_without_nominal_speeds.json")
    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document1),
        _create_speeds_objects(document2),
    ]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b74",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )
    # assert
    assert result == {
        "design_speeds": [{"speed": 1337, "start": *************, "end": *************}],
        "set_speeds": [{"speed": 0, "start": *************, "end": *************}],
        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b74",
        "speeds": [{"speed": 0, "end": *************, "start": *************}],
    }


@patch("common.get._get_customer_settings_configuration", return_value=MOCK_LINE_SETTINGS)
def test_get_speeds_when_data_is_none(
    config_mock, query_machine_data_and_speeds
):  # pylint: disable=unused-argument
    query_machine_data_and_speeds.query_speeds.side_effect = [(None, None, None)]

    from common.get import get_speeds

    # act
    result = get_speeds(
        "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
        "test-account",
        "test-line-id",
        "*************",
        "*************",
        True,
    )

    # assert
    assert result is None


@mock_aws
def test_get_customer_settings_configuration_raises_httpexception_404(
    query_machine_data_and_speeds,
):
    document = _read_json_file("test/res/design_set_speeds_query_data.json")["tests"][1]
    document2 = _read_json_file("test/res/speeds_query_data_without_nominal_speeds.json")
    query_machine_data_and_speeds.query_speeds.side_effect = [
        _create_speeds_objects(document),
        _create_speeds_objects(document2),
    ]

    from common.get import get_speeds

    # act
    with pytest.raises(Exception) as exception:
        get_speeds(
            "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
            "test-account",
            "test-line-id",
            "*************",
            "*************",
            True,
        )

    # Assert
    assert exception.typename == "HTTPException"
    assert exception.value.status_code == 404


def test_determine_max():
    from common.get import determine_max
    from common.models.speeds_model import Speed

    items = [Speed(start=1, speed=1), Speed(start=2, speed=10), Speed(start=3, speed=5)]
    max = determine_max(items)

    assert max == Speed(start=2, speed=10)


def test_determine_average():
    from common.get import determine_average
    from common.models.speeds_model import Speed

    items = [
        Speed(start=1, end=2, duration=1, speed=8),
        Speed(start=2, end=3, duration=1, speed=10),
        Speed(start=3, end=10, duration=7, speed=9),
    ]
    avg = determine_average(items)

    assert avg == 9
