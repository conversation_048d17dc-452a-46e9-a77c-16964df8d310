# System import
import json
import os
import site

import boto3

# Library import
import pytest
from fastapi import Request
from moto import mock_aws

# we set our site dir to src to have proper package names
MODULE_DIR_PATH = os.path.dirname(os.path.realpath(__file__))
source_dir = os.path.join(MODULE_DIR_PATH, "..", "src")
site.addsitedir(source_dir)

TESTING = "testing"


@pytest.fixture
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = TESTING
    os.environ["AWS_SECRET_ACCESS_KEY"] = TESTING
    os.environ["AWS_SECURITY_TOKEN"] = TESTING
    os.environ["AWS_SESSION_TOKEN"] = TESTING
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["SQS_QUEUENAME"] = TESTING
    os.environ["SQS_URL"] = "https://eu-central-1.queue.amazonaws.com/" + TESTING
    os.environ["AWS_STAGE"] = TESTING
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"


_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["scope-1", "scope-2"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps(
            {
                "userId": "some-user-id",
                "username": "some-user-name",
                "login": "some-login",
                "groups": ["group-1", "site-manager"],
            }
        ),
        "account": json.dumps({"accountId": "readykit-replay", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture
def aws_request_mock():
    event = {
        "aws.event": {"requestContext": _request_context},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    yield request


@pytest.fixture
def dynamo_db_tables_mock(aws_credentials):
    with mock_aws():
        db_client = boto3.resource("dynamodb", region_name="eu-central-1")
        rk_performance_customer_settings = db_client.create_table(
            TableName="rk-performance-customer-settings",
            KeySchema=[
                {"AttributeName": "account", "KeyType": "HASH"},
                {
                    "AttributeName": "line_id",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "account", "AttributeType": "S"},
                {"AttributeName": "line_id", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        rk_downtime_categories = db_client.create_table(
            TableName="rk-downtime-categories",
            KeySchema=[{"AttributeName": "kpi_model", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "kpi_model", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        performance_data = db_client.create_table(
            TableName="performance-data",
            KeySchema=[
                {"AttributeName": "rk_eq_id", "KeyType": "HASH"},
                {
                    "AttributeName": "time_from",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "rk_eq_id", "AttributeType": "S"},
                {"AttributeName": "time_from", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        yield {
            "rk_performance_customer_settings": rk_performance_customer_settings,
            "rk_downtime_categories": rk_downtime_categories,
            "performance-data": performance_data,
        }


@pytest.fixture
def sample_arguments_with_aws_event(aws_event_mock):
    return {
        "aws_event": aws_event_mock,
        "line-id": "test-line-id",
        "machine_id": "test-machine-id",
        "time_from": 1,
        "time_to": 5,
    }
