# Copyright (c) 2020, Syskron GmbH. All rights reserved.

# system import
import json
import logging

import pytest
from fastapi import status
from fastapi.testclient import TestClient

# library import
from unittest.mock import patch
from moto import mock_aws

from common.constants import PRODUCT_TEXTS

LOGGER = logging.getLogger(__name__)

DEFAULT_START = "*************"
DEFAULT_END = "*************"

MOCK_TRANSLATIONS = {"1": "Sort 1", "2": "Very Good Weizen 0.5L"}

MOCK_PRODUCTS = {
    "products": [
        {"product_id": 1, "start": "*************"},
        {"product_id": 2, "start": "1580212800000"},
    ]
}

MOCK_PRODUCTS_PERFECT_BEGIN_END = {
    "products": [
        {"product_id": 1, "start": DEFAULT_START},
        {"product_id": 2, "start": DEFAULT_END},
    ]
}

MOCK_PRODUCTS_UNFINISHED_PAST = {
    "products": [
        {"product_id": 1, "start": "*************"},
    ]
}

MOCK_PRODUCTS_UNFINISHED_PAST_OLDER = {
    "products": [
        {"product_id": 1337, "start": "*************"},
    ]
}

MOCK_PRODUCTS_UNFINISHED_FUTURE = {
    "products": [
        {"product_id": 1, "start": "*************"},
    ]
}

MOCK_PRODUCTS_UNFINISHED_FUTURE_NEWER = {
    "products": [
        {"product_id": 1337, "start": "*************"},
    ]
}

MOCK_ACCOUNT = "readykit-replay"
SAMPLE_MACHINE_ID = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"


@pytest.fixture
def aws_event_mock_product(aws_request_mock):
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch("api.endpoints.products.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(
            aws_request_mock.scope.get("aws.event").get("requestContext")
        )
        event_mock.return_value = properties
        yield event_mock


@mock_aws
def test_get_products_cut(aws_credentials, aws_event_mock_product, dynamo_db_tables_mock):
    """
    This test is there to see if data returned at all
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl()
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": int(DEFAULT_END),
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut(aws_credentials, aws_event_mock_product, dynamo_db_tables_mock):
    """
    This test is there to see if data returned at all
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(cut_times=0)
    insert_sample_data_in_dynamo_perfect_begin_end(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": int(DEFAULT_END),
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            }
        ]
    }

    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut_extend_first_product_to_past(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the first product in the base document started after the requested from
    then the we need to query back until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(time_from=int(DEFAULT_START) - 1, cut_times=0)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_past(dynamo_db_tables_mock)
    # insert multiple elements to see if correct one is selected from dynamodb
    insert_data_with_unfinished_product_past_older(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": *************,
            },
            {
                "end": int(DEFAULT_END),
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
                "ongoing": True,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_cut_doesnt_extend_first_product_to_past(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the first product in the base document started after the requested from
    then the we need to query back until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    query_time_from = int(DEFAULT_START) - 1
    url = prepareUrl(time_from=query_time_from, cut_times=1)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_past(dynamo_db_tables_mock)
    # insert multiple elements to see if correct one is selected from dynamodb
    insert_data_with_unfinished_product_past_older(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": query_time_from,
            },
            {
                "end": int(DEFAULT_END),
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut_get_end_for_last_product(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the last product in the base document is started before the requested
    to then the we need to query into the future until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(cut_times=False)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    # insert multiple elements to see if correct one is selected from dynamodb
    # insert_data_with_unfinished_product_future_newer(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": *************,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_cut_get_end_for_last_product_doesnt_set_ongoing(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the last product in the base document is started before the requested
    to then the we need to query into the future until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(cut_times=1)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    # insert multiple elements to see if correct one is selected from dynamodb
    insert_data_with_unfinished_product_future_newer(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": int(DEFAULT_END),
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut_get_next_product_without_ongoing_if_next_product_exists(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the last product in the base document is started before the requested
    to then the we need to query into the future until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(to=1580224700000, cut_times=0)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    # insert multiple elements to see if correct one is selected from dynamodb
    insert_data_with_unfinished_product_future_newer(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": *************,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
            {
                "end": *************,
                "product_id": 1,
                "product_text": "Sort 1",
                "start": *************,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut_select_products_from_past_and_future(
    aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the base products are bigger then start and smaller and to we need to
    query in both directions to find the product before the first and the end date for the last one
    """
    from lambda_function import app

    client = TestClient(app)
    query_time_from = 1580205500000
    query_time_to = 1580224700000
    url = prepareUrl(time_from=query_time_from, to=query_time_to, cut_times=0)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_past(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(MOCK_PRODUCTS_UNFINISHED_PAST["products"][0]["start"]),
            },
            {
                "end": *************,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
            {
                "end": 1580224700000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": *************,
                "ongoing": True,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_cut_select_products_from_past_and_future(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the base products are bigger then start and smaller and to we need to
    query in both directions to find the product before the first and the end date for the last one
    """
    from lambda_function import app

    client = TestClient(app)
    query_time_from = 1580205500000
    query_time_to = 1580224700000
    url = prepareUrl(time_from=query_time_from, to=query_time_to, cut_times=1)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_past(dynamo_db_tables_mock)
    insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": query_time_from,
            },
            {
                "end": *************,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
            {
                "end": query_time_to,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": *************,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_no_cut_no_future_element_set_ongoing(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the last product in the base document is started before the requested
    to then the we need to query into the future until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(to=1580224700000, cut_times=0)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    # insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": 1580224700000,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
                "ongoing": True,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


@mock_aws
def test_get_products_cut_no_next_element_doesnt_set_ongoing(
    aws_credentials, aws_event_mock_product, dynamo_db_tables_mock
):
    """
    This test is testing if the last product in the base document is started before the requested
    to then the we need to query into the future until it finds another document
    """
    from lambda_function import app

    client = TestClient(app)
    url = prepareUrl(to=1580224700000, cut_times=1)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock)
    # insert_data_with_unfinished_product_future(dynamo_db_tables_mock)
    insert_product_translations()
    response = client.get(url)
    actual_result = json.loads(response.text)

    expected_products = {
        "products": [
            {
                "end": 1580212800000,
                "product_id": 1,
                "product_text": MOCK_TRANSLATIONS.get("1"),
                "start": int(DEFAULT_START),
            },
            {
                "end": 1580224700000,
                "product_id": 2,
                "product_text": MOCK_TRANSLATIONS.get("2"),
                "start": 1580212800000,
            },
        ]
    }
    # Assert
    assert actual_result == expected_products


def prepareUrl(
    line: str = None,
    machine: str = None,
    time_from: int = None,
    to: int = None,
    cut_times: bool = True,
) -> str:
    """
    Get a valid URL for request
    """
    base_path = "/v1/performance-analytics/products"
    line_id = line or "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = machine or SAMPLE_MACHINE_ID
    time_from = time_from or DEFAULT_START
    time_to = to or DEFAULT_END
    query_params = f"time_from={time_from}&time_to={time_to}" f"&cut_times={cut_times}"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"
    return url


def insert_product_translations():
    import boto3

    from conftest import TESTING

    s3_client = boto3.client("s3", region_name="us-east-1")
    s3_client.create_bucket(Bucket="rk-zait-" + TESTING)

    """
    Insert mock translations for texts
    """
    bucket_folder = f"account={MOCK_ACCOUNT}/machine={SAMPLE_MACHINE_ID}"
    file_name = f"{bucket_folder}/{PRODUCT_TEXTS}.json"

    s3_client.put_object(
        Bucket="rk-zait-testing", Key=file_name, Body=json.dumps(MOCK_TRANSLATIONS)
    )


def insert_sample_data_in_dynamo(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(TableName="performance-data", Item=create_perf_data_item())


def insert_sample_data_in_dynamo_perfect_begin_end(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(
        TableName="performance-data",
        Item=create_perf_data_item(
            start="*************",
            end="*************",
            products=MOCK_PRODUCTS_PERFECT_BEGIN_END.get("products"),
        ),
    )


def insert_data_with_unfinished_product_past(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(
        TableName="performance-data",
        Item=create_perf_data_item(
            start="1580204600000",
            end="*************",
            products=MOCK_PRODUCTS_UNFINISHED_PAST.get("products"),
        ),
    )
    # add more elements


def insert_data_with_unfinished_product_past_older(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(
        TableName="performance-data",
        Item=create_perf_data_item(
            start="1570200000000",
            end="1570206000000",
            products=MOCK_PRODUCTS_UNFINISHED_PAST_OLDER.get("products"),
        ),
    )
    # add more elements


def insert_data_with_unfinished_product_future(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(
        TableName="performance-data",
        Item=create_perf_data_item(
            start="*************",
            end="1580224600000",
            products=MOCK_PRODUCTS_UNFINISHED_FUTURE.get("products"),
        ),
    )


def insert_data_with_unfinished_product_future_newer(dynamo_db_tables_mock) -> None:
    """
    Insert sample data into DB to be selected
    """
    perf_db = dynamo_db_tables_mock.get("performance-data")
    perf_db.put_item(
        TableName="performance-data",
        Item=create_perf_data_item(
            start="*************",
            end="*************",
            products=MOCK_PRODUCTS_UNFINISHED_FUTURE_NEWER.get("products"),
        ),
    )


def create_perf_data_item(
    start: str = str(DEFAULT_START),
    end: str = str(DEFAULT_END),
    products=MOCK_PRODUCTS.get("products"),
):
    """
    Create object for performance database
    """
    # Example: 'readykit-replay_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_product_type'
    item = {
        "rk_eq_id": MOCK_ACCOUNT + "_" + SAMPLE_MACHINE_ID + "_product_type",
        "time_from": start,
        "time_to": end,
        "product_type": products,
    }
    return item


def test_products_get_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from lambda_function import app

    # Prepare URL
    base_path = "/v1/performance-analytics/products"
    line_id = "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=*************&timeTo=*************"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}
