# Copyright (c) 2020, Syskron GmbH. All rights reserved.

# system import
import json
import logging
import os
import tempfile
import time

import pytest
from fastapi import status
from fastapi.testclient import TestClient

# library import
from unittest.mock import patch
from test_constants import (
    DESIGN_SPEEDS_CONST,
    MULT<PERSON>LE_MACHINE_FULL_RESPONSE,
    MU<PERSON><PERSON>LE_MACHINE_FULL_RESPONSE_ONLY_RAW,
    MULTIPLE_MACHINE_MERGED_SPEEDS_FULL_REQUEST,
    SAMPLE_MACHINE_ID,
    SAMPLE_MACHINE_ID2,
    SET_SPEEDS_CONST,
    SPEED_25_1,
    SPEED_45_1,
    SPEEDS_EXPECTED_CUT,
    SPEEDS_EXPECTED_MULTIPLE_MACHINE,
    SPEEDS_EXPECTED_UNCUT,
    create_speed,
)

from common.constants import AggregationOptions
from common.models.speeds_model import SpeedResponse

LOGGER = logging.getLogger(__name__)


@pytest.fixture
def aws_event_mock_speed(aws_request_mock):
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch("api.endpoints.speeds.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(
            aws_request_mock.scope.get("aws.event").get("requestContext")
        )
        event_mock.return_value = properties
        yield event_mock


def test_lambda_handler_call_get_speeds_cut(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrl(cut_times=True)
    insert_sample_data_in_dynamo(dynamo_db_tables_mock, insert_last_item=True)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == SPEEDS_EXPECTED_CUT


def test_lambda_handler_call_get_speeds_uncut(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrl()
    insert_sample_data_in_dynamo(dynamo_db_tables_mock, insert_last_item=False)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == SPEEDS_EXPECTED_UNCUT


def mocked_process_start(self):
    return self.run()


def mocked_process_join(self, timeout: int):
    pass


@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_lambda_handler_call_get_speeds_multiple_machines(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines()
    insert_sample_data_in_dynamo(
        dynamo_db_tables_mock, include_current_speed=True, insert_last_item=True
    )
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result == SPEEDS_EXPECTED_MULTIPLE_MACHINE


@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_lambda_handler_call_get_speeds_multiple_machines_with_multiple_speeds(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines()

    design_speeds_item = create_perf_data_item(
        "design_speeds",
        1620331200000,
        SAMPLE_MACHINE_ID,
        DESIGN_SPEEDS_CONST,
    )

    set_speeds_item = create_perf_data_item(
        "set_speeds",
        1620331200000,
        SAMPLE_MACHINE_ID,
        SET_SPEEDS_CONST,
    )

    speeds_item = create_perf_data_item(
        "speeds",
        1620331200000,
        SAMPLE_MACHINE_ID,
        SPEED_45_1,
    )

    speeds_item_merge = create_perf_data_item(
        "speeds",
        1620341200000,
        SAMPLE_MACHINE_ID,
        SPEED_25_1,
    )

    speeds_item_last = create_perf_data_item(
        "speeds",
        1820331200000,
        SAMPLE_MACHINE_ID,
        SPEED_45_1,
    )

    items = [design_speeds_item, set_speeds_item, speeds_item, speeds_item_merge, speeds_item_last]

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item.get("speeds").get("raw") == [
            {"speed": 45000.0, "end": 1620341200000, "start": 1620332200000, "ongoing": False},
            {"speed": 25000.0, "end": 1620350000000, "start": 1620341200000, "ongoing": False},
        ]


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            {"speed": 45000.0, "end": 1620400000000, "start": 1620332200000},
        ),
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620341200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 25000, "1620341200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            {"speed": 39454.28, "end": 1620400000000, "start": 1620332200000},
        ),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_avg(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(
        time_from=1620332200000, to=1620400000000, options=[AggregationOptions.AVERAGE]
    )

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item.get("speeds").get("average") == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            [{"speed": 45000.0, "end": 1620400000000, "ongoing": True, "start": 1620332200000}],
        ),
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620341200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 25000, "1620341200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
                (  # have a last speed due to ongoing flag
                    "speeds",
                    1820360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1820360000000"),
                ),
            ],
            [
                {"speed": 45000.0, "end": 1620341200000, "start": 1620332200000, "ongoing": False},
                {"speed": 25000.0, "end": 1620360000000, "start": 1620341200000, "ongoing": False},
                {"speed": 45000.0, "end": 1620400000000, "start": 1620360000000, "ongoing": False},
            ],
        ),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_with_multiple_speeds_merged(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(time_from=1620332200000, to=1620400000000)

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item.get("speeds").get("raw") == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
                (
                    "speeds",
                    1620370000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620370000000"),
                ),
            ],
            {"speed": 45000.0, "ongoing": True, "end": 1620400000000, "start": 1620370000000},
        ),
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620341200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 25000, "1620341200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            {"speed": 45000.0, "ongoing": True, "end": 1620400000000, "start": 1620360000000},
        ),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_latest(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(to=1620400000000, options=[AggregationOptions.LATEST])

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item.get("speeds").get("latest") == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
                (
                    "speeds",
                    1620370000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            {"end": 1620360000000, "speed": 45000.0, "start": 1620331200000, "ongoing": False},
        )
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_latest_not_ongoing(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(to=1620360000000, options=[AggregationOptions.LATEST])

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item.get("speeds").get("latest") == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "design_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "set_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            MULTIPLE_MACHINE_MERGED_SPEEDS_FULL_REQUEST,
        ),
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "design_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # have a last set_speed in mock due to ongoing
                    "set_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620341200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 25000, "1620341200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
                (  # put last item in due to ongoing flag
                    "speeds",
                    1820360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1820360000000"),
                ),
            ],
            MULTIPLE_MACHINE_FULL_RESPONSE,
        ),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_all_types(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(
        time_from=1620332200000,
        to=1620400000000,
        options=[
            AggregationOptions.AVERAGE,
            AggregationOptions.LATEST,
            AggregationOptions.MAX,
            AggregationOptions.RAW,
        ],
    )

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "design_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "set_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
            ],
            MULTIPLE_MACHINE_MERGED_SPEEDS_FULL_REQUEST,
        )
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_all_types_with_option_str(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(
        time_from=1620332200000,
        to=1620400000000,
        options=[
            AggregationOptions.AVERAGE,
            AggregationOptions.LATEST,
            AggregationOptions.MAX,
            AggregationOptions.RAW,
        ],
        as_options_str=True,
    )

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item == expected


@pytest.mark.parametrize(
    "input, expected",
    [
        (
            [
                (
                    "design_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "design_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("design_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "set_speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1620331200000"),
                ),
                (  # put last item in due to ongoing flag
                    "set_speeds",
                    1820331200000,
                    SAMPLE_MACHINE_ID,
                    ("set_speeds", "speed", 45000, "1820331200000"),
                ),
                (
                    "speeds",
                    1620331200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620331200000"),
                ),
                (
                    "speeds",
                    1620341200000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 25000, "1620341200000"),
                ),
                (
                    "speeds",
                    1620360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1620360000000"),
                ),
                (  # put last item in due to ongoing flag
                    "speeds",
                    1820360000000,
                    SAMPLE_MACHINE_ID,
                    ("speeds", "current_speed", 45000, "1820360000000"),
                ),
            ],
            MULTIPLE_MACHINE_FULL_RESPONSE_ONLY_RAW,
        ),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_no_options_result_in_raw_returned(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, input, expected
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(time_from=1620332200000, to=1620400000000, options=[])

    items = []
    for item in input:
        items.append(create_perf_data_item(item[0], item[1], item[2], create_speed(*item[3])))

    insert_sample_data_in_dynamo(dynamo_db_tables_mock, True, items)
    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    for item in actual_result:
        assert item == expected


@pytest.mark.parametrize(
    "options_str",
    [
        ("latest"),
        ("latest,"),
        ("latest,raw"),
        ("latest,raw,"),
        ("latest,raw,ignored"),
        ("latest,raw,,,,,,"),
        ("ignored,,"),
    ],
)
@patch("multiprocessing.Process.start", new=mocked_process_start)
@patch("multiprocessing.Process.join", new=mocked_process_join)
def test_get_speeds_multiple_machines_options_str_doesnt_fail(
    aws_credentials, aws_event_mock_speed, dynamo_db_tables_mock, options_str
):
    """Test whether trend analysis is called as well as its result is returned."""
    from lambda_function import app

    client = TestClient(app)
    url = prepareSpeedsUrlMultipleMachines(
        time_from=1620332200000, to=1620400000000, options_str=options_str
    )

    insert_configuration(dynamo_db_tables_mock)
    LOGGER.info("URL: %s", url)
    response = client.get(url)
    actual_result = json.loads(response.text)

    # Assert
    assert actual_result is not None


def prepareSpeedsUrl(
    line: str = None,
    machine: str = None,
    time_from: int = None,
    to: int = None,
    cut_times: bool = False,
) -> str:
    """
    Get a valid URL for request
    """
    base_path = "/v1/performance-analytics/speeds"
    line_id = line or "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = machine or SAMPLE_MACHINE_ID
    time_from = time_from or 1620332200000
    time_to = to or 1620350000000
    query_params = f"&time_from={time_from}" f"&time_to={time_to}" f"&cut_times={cut_times}"

    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    return url


def prepareSpeedsUrlMultipleMachines(
    line: str = None,
    machine: str = None,
    time_from: int = None,
    to: int = None,
    cut_times: bool = False,
    options: [AggregationOptions] = [],
    as_options_str: bool = False,
    options_str: str = None,
) -> str:
    """
    Get a valid URL for request
    """
    base_path = "/v1/performance-analytics/speeds"
    line_id = line or "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    if machine is not None:
        machine_ids = f"{machine}, {machine}"
    else:
        machine_ids = f"{SAMPLE_MACHINE_ID}, {SAMPLE_MACHINE_ID}"
    time_from = time_from or 1620332200000
    time_to = to or 1620350000000

    query_params = (
        f"machineIds={machine_ids}&timeFrom={time_from}&timeTo={time_to}&cut_times" f"={cut_times}"
    )

    path_suffix = ""

    query_options = ""
    if as_options_str is False:
        if AggregationOptions.AVERAGE in options:
            query_options += f"&options=avg"
        if AggregationOptions.LATEST in options:
            query_options += f"&time={time_to}"
            query_options += f"&options=latest"
        if AggregationOptions.MAX in options:
            query_options += f"&options=max"
        if AggregationOptions.RAW in options:
            query_options += f"&options=raw"
    else:
        counter = 0
        if len(options) > 0:
            query_options += "&optionsStr="
        if AggregationOptions.AVERAGE in options:
            query_options += "avg"
            counter += counter
        if AggregationOptions.LATEST in options:
            if counter < 1:
                query_options += ","
            query_options += "latest"
            counter += counter
        if AggregationOptions.MAX in options:
            if counter < 1:
                query_options += ","
            query_options += "max"
            counter += counter
        if AggregationOptions.RAW in options:
            if counter < 1:
                query_options += ","
            query_options += "raw"

    if options_str is not None:
        query_options = f"&optionsStr={options_str}"

    query_params += query_options

    url = f"{base_path}/{line_id}{path_suffix}?{query_params}"

    return url


def prepareUrl(
    line: str = None,
    machine_ids: str = None,
    time_from: int = None,
    to: int = None,
    cut_times: bool = False,
) -> str:
    """
    Get a valid URL for request
    """
    base_path = "/v1/performance-analytics/speeds"
    line_id = line or "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_ids = machine_ids or f"{SAMPLE_MACHINE_ID}, {SAMPLE_MACHINE_ID2}"
    time_from = time_from or 1620332200000
    time_to = to or 1620350000000
    query_params = (
        f"&machine_ids={machine_ids}&time_from={time_from}&"
        f"time_to={time_to}"
        f"&cut_times={cut_times}"
    )

    url = f"{base_path}/{line_id}/?{query_params}"

    return url


def insert_sample_data_in_dynamo(
    dynamo_db_tables_mock, include_current_speed=False, items=None, insert_last_item: bool = False
) -> None:
    """
    Insert sample data into DB to be selected
    """
    if items is None:
        items = create_perf_data_items(SAMPLE_MACHINE_ID, include_current_speed, insert_last_item)
        items.extend(create_perf_data_items(SAMPLE_MACHINE_ID2, include_current_speed))
    perf_db = dynamo_db_tables_mock.get("performance-data")

    for item in items:
        perf_db.put_item(TableName="performance-data", Item=item)


def insert_configuration(dynamo_db_tables_mock) -> None:
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item={
            "account": "readykit-replay",
            "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
            "kpi_model": "opi",
            "created_at": *************,
            "updated_at": *************,
            "settings": {
                "minor_stop_config": 5,
                "machine_settings": {
                    "65ea1a06-2698-45eb-baad-3a3e5fc25b74": {"nominal_speed": 1337}
                },
            },
        },
    )


def create_perf_data_item(
    suffix: str,
    time_from: int,
    machineId: str,
    data,
):
    """
    Create object for performance database
    """
    # 'readykit-replay_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_design_speeds'
    item = {"rk_eq_id": "readykit-replay_" + machineId + "_" + suffix, "time_from": str(time_from)}
    item.update(data)
    return item


def create_perf_data_items(
    machineId: str = SAMPLE_MACHINE_ID, current_speed: bool = False, insert_last_item: bool = False
):
    """
    Create object for performance database
    """
    # 'readykit-replay_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a_design_speeds'
    data_items = []

    design_speeds_item = create_perf_data_item(
        "design_speeds", 1620331200000, machineId, DESIGN_SPEEDS_CONST
    )

    set_speeds_item = create_perf_data_item(
        "set_speeds", 1620331200000, machineId, SET_SPEEDS_CONST
    )

    data_items.extend([design_speeds_item, set_speeds_item])

    if insert_last_item:
        design_speeds_item_last = create_perf_data_item(
            "design_speeds", 1820331200000, machineId, DESIGN_SPEEDS_CONST
        )
        set_speeds_item_last = create_perf_data_item(
            "set_speeds", 1820331200000, machineId, SET_SPEEDS_CONST
        )
        speeds_item_last = create_perf_data_item("speeds", 1820331200000, machineId, SPEED_45_1)

        data_items.extend([design_speeds_item_last, set_speeds_item_last, speeds_item_last])

    if current_speed:
        speeds_item = create_perf_data_item("speeds", 1620331200000, machineId, SPEED_45_1)
        data_items.append(speeds_item)

        return data_items

    return data_items


def parallel_function(machine_id) -> SpeedResponse:
    if machine_id == "ERROR":
        raise RuntimeError("ERROR")
    if machine_id.startswith("TIMEOUT"):
        time.sleep(10)
    return SpeedResponse(equipment_id=machine_id)


def test_run_parallel():
    from common.parallel import run_parallel

    response = run_parallel(parallel_function, [], ["1", "2"])

    assert response is not None
    assert len(response) == 2
    assert SpeedResponse(equipment_id="1") in response
    assert SpeedResponse(equipment_id="2") in response


def test_run_parallel_exception():
    from common.parallel import run_parallel

    response = run_parallel(parallel_function, [], ["ERROR", "2"])

    assert response is not None
    assert len(response) == 2
    for speed_response in response:
        if speed_response.equipment_id == "ERROR":
            assert speed_response.error is not None
        if speed_response.equipment_id == "2":
            assert speed_response.error is None


def test_run_parallel_timeout():
    from common.parallel import run_parallel

    start_time = time.time()

    response = run_parallel(
        parallel_function, [], ["1", "2", "TIMEOUT1", "3", "TIMEOUT2"], timeout=3
    )

    diff = time.time() - start_time

    assert response is not None
    assert len(response) == 5
    assert diff < 10
    for speed_response in response:
        if speed_response.equipment_id.startswith("TIMEOUT"):
            assert speed_response.error is not None
        else:
            assert speed_response.error is None


def test_run_in_process():
    from common.parallel import run_in_process

    _, temp_file = tempfile.mkstemp()
    try:
        run_in_process(parallel_function, "1", [], temp_file)

        with open(temp_file, encoding="utf-8") as tf:
            response = SpeedResponse.parse_raw(tf.read())
            assert response == SpeedResponse(equipment_id="1")

    finally:
        os.remove(temp_file)


def test_run_in_process_exception():
    from common.parallel import run_in_process

    _, temp_file = tempfile.mkstemp()
    try:
        run_in_process(parallel_function, "ERROR", [], temp_file)

        with open(temp_file, encoding="utf-8") as tf:
            response = SpeedResponse.parse_raw(tf.read())
            assert response == SpeedResponse(equipment_id="ERROR", error="ERROR")

    finally:
        os.remove(temp_file)


def test_speeds_get_multiple_equipments_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from lambda_function import app

    # Prepare URL
    base_path = "/v1/performance-analytics/speeds"
    line_id = "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    query_params = "timeFrom=1710028800000&timeTo=1710892800000"
    url = f"{base_path}/{line_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}


def test_speeds_get_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from lambda_function import app

    # Prepare URL
    base_path = "/v1/performance-analytics/speeds"
    line_id = "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=1710028800000&timeTo=1710892800000"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}
