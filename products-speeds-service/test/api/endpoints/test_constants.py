def create_speed(type: str, id: str, value: int, start: str) -> dict:
    """
    Create a Speed document
    """

    item = {}
    speed = {}
    speed[id] = value
    speed['start'] = start
    item[type] = [speed]

    return item


DESIGN_SPEEDS_CONST = create_speed('design_speeds', 'speed', 45000, '1620331200000')

SET_SPEEDS_CONST = create_speed('set_speeds', 'speed', 45000, '1620331200000')

SPEED_45_1 = create_speed('speeds', 'current_speed', 45000, '1620331200000')

SPEED_45_3 = create_speed('speeds', 'current_speed', 45000, '1620342200000')

SPEED_45_5 = create_speed('speeds', 'current_speed', 45000, '1620343200000')

SPEED_25_1 = create_speed('speeds', 'current_speed', 25000, '1620341200000')

SPEEDS_EXPECTED_UNCUT = {
    'design_speeds': [{'end': 1620350000000, 'ongoing': True, 'speed': 45000.0, 'start': 1620331200000}],
    'set_speeds': [{'end': 1620350000000, 'ongoing': True, 'speed': 45000.0, 'start': 1620331200000}],
}

SPEEDS_EXPECTED_CUT = {
    'design_speeds': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
    'set_speeds': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
}

SPEEDS_EXPECTED_MULTIPLE_MACHINE = [
    {
        'designSpeed': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
        'equipmentId': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
        'setSpeeds': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
        'speeds': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
    },
    {
        'designSpeed': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
        'equipmentId': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
        'setSpeeds': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
        'speeds': {'raw': [{'end': 1620350000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}]},
    },
]

SPEED_EXPECTED_MULTIPLE_MACHINE = [
    {
        'designSpeed': [{'end': 1620350000000, 'speed': 45000, 'start': 1620332200000}],
        'equipment_id': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
        'setSpeeds': [{'end': 1620350000000, 'speed': 45000, 'start': 1620332200000}],
        'speeds': [{'speed': 45000, 'end': 1620350000000, 'start': 1620332200000}],
    },
    {
        'designSpeed': [{'end': 1620350000000, 'speed': 45000, 'start': 1620332200000}],
        'equipment_id': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
        'setSpeeds': [{'end': 1620350000000, 'speed': 45000, 'start': 1620332200000}],
        'speeds': [{'speed': 45000, 'end': 1620350000000, 'start': 1620332200000}],
    },
]

SAMPLE_MACHINE_ID = '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a'
SAMPLE_MACHINE_ID2 = '5fcd1cee-73b9-4fd2-804a-1a04aa27e48b'

MULTIPLE_MACHINE_FULL_RESPONSE = {
    'designSpeed': {
        'average': {'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000},
        'raw': [
            {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
        'latest': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000,
                   'ongoing': False},
        'max': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
    },
    'equipmentId': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
    'setSpeeds': {
        'average': {'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000},
        'raw': [
            {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
        'latest': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000,
                   'ongoing': False},
        'max': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
    },
    'speeds': {
        'average': {'end': 1620400000000, 'speed': 39454.28, 'start': 1620332200000},
        'raw': [
            {'duration': 9000000, 'end': 1620341200000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
            {'duration': 18800000, 'end': 1620360000000, 'speed': 25000.0, 'start': 1620341200000, 'ongoing': False},
            {'duration': 40000000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620360000000, 'ongoing': False},
        ],
        'latest': {'duration': 40000000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620360000000,
                   'ongoing': False},
        'max': {'duration': 9000000, 'end': 1620341200000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
    },
}

MULTIPLE_MACHINE_FULL_RESPONSE_ONLY_RAW = {
    'designSpeed': {
        'raw': [{'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
    },
    'equipmentId': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
    'setSpeeds': {
        'raw': [{'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
    },
    'speeds': {
        'raw': [
            {'end': 1620341200000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
            {'end': 1620360000000, 'speed': 25000.0, 'start': 1620341200000, 'ongoing': False},
            {'end': 1620400000000, 'speed': 45000.0, 'start': 1620360000000, 'ongoing': False},
        ],
    },
}

MULTIPLE_MACHINE_MERGED_SPEEDS_FULL_REQUEST = {
    'designSpeed': {
        'average': {'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000},
        'raw': [
            {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
        'latest': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000,
                   'ongoing': False},
        'max': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
    },
    'equipmentId': '5fcd1cee-73b9-4fd2-804a-1a04aa27e48a',
    'setSpeeds': {
        'average': {'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000},
        'raw': [
            {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False}],
        'latest': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000,
                   'ongoing': False},
        'max': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000, 'ongoing': False},
    },
    'speeds': {
        'average': {'end': 1620400000000, 'speed': 45000.0, 'start': 1620332200000},
        'raw': [
            {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'ongoing': True, 'start': 1620332200000}],
        'latest': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'ongoing': True,
                   'start': 1620332200000},
        'max': {'duration': 67800000, 'end': 1620400000000, 'speed': 45000.0, 'ongoing': True, 'start': 1620332200000},
    },
}
