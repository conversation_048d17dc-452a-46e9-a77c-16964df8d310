# Products-Speeds-Service

Returns product ids with matching product name for a given time_from.. Also returns Set- &
Designspeeds for a given time_from.

# GET Products

``` https://api.rk.share2act-dev.io/v1/performance-analytics/products/<line-id>/<machine_id>?time_from=<utc_time_in_ms>&time_to=<utc_time_in_ms> ```

## Parameters

### time_from
UTC time in ms
### time_to
UTC time in ms
### cut_times
cut times (True or False)

## Return value

```
{
    "products": [{
            "start": 1580205600000,
            "product_id": 1,
            "product_text": "001 ZGB 500  001 Piwo Tatra Mocne",
            "end": 1580212800000
        }, {
            "start": 1580212800000,
            "product_id": 2,
            "product_text": "001 ZGB 500  002 Piwo Kaper",
            "end": 1580223600000
        }, {
            "start": 1580223600000,
            "product_id": 1001,
            "product_text": "No text for product="1001"",
            "end": 1580234400000
        }
    ]
}
```

# GET Set- & Designspeed

``` https://api.rk.share2act-dev.io/v1/performance-analytics/speeds/<line-id>/<machine_id>?time_from=<utc_time_in_ms>&time_to=<utc_time_in_ms> ```

## Parameters

### time_from
UTC time in ms
### time_to
UTC time in ms
### cut_times
cut times (True or False)


## Return value

```
{
    "set_speeds": [{
            "start": 1580205600000,
            "speed": 30000.0,
            "end": 1580223600000
        }, {
            "start": 1580223600000,
            "speed": 45000.0,
            "end": 1580230800000
        }, {
            "start": 1580230800000,
            "speed": 55000.0,
            "end": 1580234400000
        }
    ],
    "design_speeds": [{
            "start": 1580205600000,
            "speed": 65000.0,
            "end": 1580223600000
        }, {
            "start": 1580223600000,
            "speed": 55000.0,
            "end": 1580234400000
        }
    ]
}
```

# GET Set- & Designspeed and Speeds 

``` https://api.rk.share2act-dev.io/v1/performance-analytics/speeds/<line_id>?machineIds=<machine_id1>,<machine_id2>&timeFrom=<utc_time_in_ms>&optionsStr=raw,max,avg,latest ```

## Parameters

### timeFrom
UTC time in ms
### timeTo
UTC time in ms, optional, default timeFrom + 8h
### machineIds
comma separated list of machine ids
### options
one raw,max,avg,latest (can be set several time)
### optionsStr
comma separated list of options (raw,max,avg,latest)
### cutTimes
cut times (True or False)
## Return value

```
[
    {
        "equipmentId": "machine_id",
        "designSpeed": {
            "raw": [
                {
                    "speed": 25000.0,
                    "start": 1653854400000,
                    "end": 1653883200000,
                    "duration": 28800000
                }
            ],
            "average": {
                "speed": 25000.0,
                "start": 1653854400000,
                "end": 1653883200000
            },
            "max": {
                "speed": 25000.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            },
            "latest": {
                "speed": 25000.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            }
        },
        "setSpeeds": {
            "raw": [
                {
                    "speed": 0.0,
                    "start": 1653854400000,
                    "end": 1653883200000,
                    "duration": 28800000
                }
            ],
            "average": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000
            },
            "max": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            },
            "latest": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            }
        },
        "speeds": {
            "raw": [
                {
                    "speed": 0.0,
                    "start": 1653854400000,
                    "end": 1653883200000,
                    "duration": 28800000
                }
            ],
            "average": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000
            },
            "max": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            },
            "latest": {
                "speed": 0.0,
                "start": 1653854400000,
                "end": 1653883200000,
                "duration": 28800000
            }
        }
    }
]
```
