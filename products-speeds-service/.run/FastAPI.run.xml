<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="FastAPI" type="Python.FastAPI">
    <option name="file" value="$PROJECT_DIR$/performance-analytics-kpi-service/src/lambda_function.py" />
    <module name="performance-analytics-service" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/performance-analytics-kpi-service/src" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <EXTENSION ID="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" />
        <ENTRY IS_ENABLED="false" PARSER="env" PATH="src/.env" />
      </ENTRIES>
    </EXTENSION>
    <EXTENSION ID="software.aws.toolkits.jetbrains.core.execution.PythonAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="true" />
    </EXTENSION>
    <option name="launchJavascriptDebuger" value="false" />
    <method v="2" />
  </configuration>
</component>