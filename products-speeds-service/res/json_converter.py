import json

import boto3
import botocore

from common.logger import Logger

LOGGER = Logger()

S3_CLIENT = boto3.client("s3")
BUCKET_NAME = "rk-zait-dev"
ACCOUNT = "readykit-replay"
FILE_NAME_TO_LOAD = "BCM_TypelistArea01.txt"

DRYRUN = True  # set to False to upload to S3


def _convert(machine_ids):
    product_texts = {}

    for machine_id in machine_ids:
        parsed_product_text = _load_and_convert(machine_id)
        product_texts[machine_id] = parsed_product_text

    return product_texts


def _upload(machine_ids, product_texts):
    for machine_id in machine_ids:
        _upload_to_s3(machine_id, product_texts.get(machine_id, {}))


def _load_and_convert(machine_id):
    """Converts txt item from S3 to json"""

    folder = f"{ACCOUNT}/{machine_id}"
    file_name = f"{folder}/{FILE_NAME_TO_LOAD}"

    LOGGER.debug("Get file json from S3")

    content = None

    try:
        bucket_object = S3_CLIENT.get_object(Bucket=BUCKET_NAME, Key=file_name)
        content = bucket_object["Body"].read().decode("utf-16")

    except botocore.exceptions.ClientError as exception:
        error_code = exception.response["Error"]["Code"]
        if error_code == "NoSuchKey":
            # Key not found
            LOGGER.warning("No File named %s", file_name)
            LOGGER.warning("See error code: %s", error_code)
        elif error_code == "NoSuchBucket":
            # Key not found
            LOGGER.warning('No Bucket named "%s"', BUCKET_NAME)
            LOGGER.warning("See error code: %s", error_code)
        else:
            # Something else has gone wrong.
            LOGGER.warning("Something went wrong for %s", file_name)
            LOGGER.warning("See error code: %s", error_code)
        return

    LOGGER.debug("Convert file")

    while content.endswith("\n") or content.endswith("\r"):
        content = content[:-1]

    kv_dict = {}

    my_list = content.split("\n")
    for item in my_list:
        key_value_list = item.split("]   ")

        while key_value_list[0].startswith("[") or key_value_list[0].startswith("0"):
            key_value_list[0] = key_value_list[0][1:]

        while key_value_list[1].endswith("\r"):
            key_value_list[1] = key_value_list[1][:-1]

        kv_dict[key_value_list[0]] = key_value_list[1]

    result_json = json.dumps(kv_dict, indent=4)

    return result_json


def _get_machine_ids():
    id_list = []
    client = boto3.client("s3")
    result = client.list_objects(Bucket=BUCKET_NAME, Prefix=f"{ACCOUNT}/", Delimiter="/")
    common_prefixes = result.get("CommonPrefixes")

    # filter out 'mapping_not_found' item
    string_to_exclude = "_mapping_not_found_"
    filtered_list = [
        prefix for prefix in common_prefixes if string_to_exclude not in prefix.get("Prefix")
    ]

    id_list = []
    for item in filtered_list:
        machine_id = item.get("Prefix").split("/")[1]
        id_list.append(machine_id)

    return id_list


def _upload_to_s3(machine_id, json):
    if DRYRUN:
        print("DRY RUN!")
        print(f"{json} to account={ACCOUNT}/machine={machine_id}/product_texts.json")
        print()
    else:
        S3_CLIENT.put_object(
            Body=json,
            Bucket=BUCKET_NAME,
            Key=f"account={ACCOUNT}/machine={machine_id}/product_texts.json",
        )


# pylint: disable=invalid-name
if __name__ == "__main__":
    machine_ids = _get_machine_ids()
    product_texts = _convert(machine_ids)
    _upload(machine_ids, product_texts)
