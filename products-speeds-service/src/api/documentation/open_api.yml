openapi: 3.0.2
info:
  title: FastAPI
  version: 0.1.0
paths:
  '/v1/performance-analytics/products/{line_id}/{machine_id}':
    get:
      summary: Products Get
      description: API entrypoint for getting a product list
      operationId: >-
        products_get_v1_performance_analytics_products__line_id___machine_id__get
      parameters:
        - required: true
          schema:
            title: Line Id
            type: string
          name: line_id
          in: path
        - required: true
          schema:
            title: Machine Id
            type: string
          name: machine_id
          in: path
        - required: true
          schema:
            title: Time From
            type: integer
          name: time_from
          in: query
        - required: true
          schema:
            title: Time To
            type: integer
          name: time_to
          in: query
        - required: false
          schema:
            title: Cut Times
            type: boolean
            default: true
          name: cut_times
          in: query
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  '/v1/performance-analytics/speeds/{line_id}':
    get:
      summary: Speeds Get Multiple Equipments
      description: API entrypoint for getting speeds (designed and set speeds)
      operationId: >-
        speeds_get_multiple_equipments_v1_performance_analytics_speeds__line_id__get
      parameters:
        - required: true
          schema:
            title: Line Id
            type: string
          name: line_id
          in: path
        - required: true
          schema:
            title: Timefrom
            type: integer
          name: timeFrom
          in: query
        - description: defaults to timeFrom plus 8h
          required: false
          schema:
            title: Timeto
            type: integer
            description: defaults to timeFrom plus 8h
          name: timeTo
          in: query
        - required: false
          schema:
            type: array
            items:
              $ref: '#/components/schemas/AggregationOptions'
          name: options
          in: query
        - description: Comma separated options (overwrites single options)
          required: false
          schema:
            title: Optionsstr
            type: string
            description: Comma separated options (overwrites single options)
          name: optionsStr
          in: query
        - description: Comma separated machine ids
          required: true
          schema:
            title: Machineids
            type: string
            description: Comma separated machine ids
          name: machineIds
          in: query
        - required: false
          schema:
            title: Cuttimes
            type: boolean
            default: true
          name: cutTimes
          in: query
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                title: >-
                  Response Speeds Get Multiple Equipments V1 Performance
                  Analytics Speeds  Line Id  Get
                type: array
                items:
                  $ref: '#/components/schemas/SpeedResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  '/v1/performance-analytics/speeds/{line_id}/{machine_id}':
    get:
      summary: Speeds Get
      description: API entrypoint for getting speeds (designed and set speeds)
      operationId: speeds_get_v1_performance_analytics_speeds__line_id___machine_id__get
      parameters:
        - required: true
          schema:
            title: Line Id
            type: string
          name: line_id
          in: path
        - required: true
          schema:
            title: Machine Id
            type: string
          name: machine_id
          in: path
        - required: true
          schema:
            title: Time From
            type: integer
          name: time_from
          in: query
        - required: true
          schema:
            title: Time To
            type: integer
          name: time_to
          in: query
        - required: false
          schema:
            title: Cut Times
            type: boolean
            default: true
          name: cut_times
          in: query
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LegacySpeedResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    AggregationOptions:
      title: AggregationOptions
      enum:
        - avg
        - latest
        - max
        - raw
      type: string
      description: Options for speeds to aggregate them
    CollectiveSpeedModel:
      title: CollectiveSpeedModel
      type: object
      properties:
        raw:
          title: Raw
          type: array
          items:
            $ref: '#/components/schemas/Speed'
        average:
          $ref: '#/components/schemas/Speed'
        max:
          $ref: '#/components/schemas/Speed'
        latest:
          $ref: '#/components/schemas/Speed'
        error:
          title: Error
          type: string
      description: Speed values with additional aggregations
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    LegacySpeedResponse:
      title: LegacySpeedResponse
      type: object
      properties:
        design_speeds:
          title: Design Speeds
          type: array
          items:
            $ref: '#/components/schemas/Speed'
          default: [ ]
        set_speeds:
          title: Set Speeds
          type: array
          items:
            $ref: '#/components/schemas/Speed'
          default: [ ]
      description: Speeds response
    Product:
      title: Product
      required:
        - product_id
        - start
        - product_text
      type: object
      properties:
        product_id:
          title: Product Id
          type: integer
        start:
          title: Start
          type: integer
        end:
          title: End
          type: integer
        product_text:
          title: Product Text
          type: string
        ongoing:
          title: Ongoing
          type: boolean
      description: Product
    ProductResponse:
      title: ProductResponse
      type: object
      properties:
        products:
          title: Products
          type: array
          items:
            $ref: '#/components/schemas/Product'
          default: [ ]
      description: Product response
    Speed:
      title: Speed
      required:
        - speed
        - start
      type: object
      properties:
        speed:
          title: Speed
          type: number
        start:
          title: Start
          type: integer
        end:
          title: End
          type: integer
        duration:
          title: Duration
          type: integer
        ongoing:
          title: Ongoing
          type: boolean
      description: Speed model
    SpeedResponse:
      title: SpeedResponse
      required:
        - equipmentId
      type: object
      properties:
        equipmentId:
          title: Equipmentid
          type: string
        error:
          title: Error
          type: string
        designSpeed:
          $ref: '#/components/schemas/CollectiveSpeedModel'
        setSpeeds:
          $ref: '#/components/schemas/CollectiveSpeedModel'
        speeds:
          $ref: '#/components/schemas/CollectiveSpeedModel'
      description: Speeds response
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            type: string
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
