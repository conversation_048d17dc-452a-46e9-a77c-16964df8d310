# System import
from http import HTTPStatus

from aws_lambda_powertools import Lo<PERSON>, Tracer
from aws_xray_sdk.core import patch_all

# Library import
from fastapi import APIRouter, HTTPException, Request, Depends
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from lib_cloud_sdk.api_gateway.responses import build_response
from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

# Local import
from common.get import get_products
from common.models import product_model

patch_all()

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


@router.get(
    "/products/{line_id}/{machine_id}",
    response_model=product_model.ProductResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def products_get(
    request: Request,
    line_id: str,  # pylint: disable=unused-argument
    machine_id: str,
    time_from: int,
    time_to: int,
    cut_times: bool | None = True,
):  # pylint: disable=too-many-positional-arguments
    """
    API entrypoint for getting a product list
    """
    LOGGER.info('Calling get_products endpoint with event: "%s".', request.scope.get("aws.event"))
    try:
        properties = CommonShare2ActProperties(
            request.scope.get("aws.event", {}).get("requestContext")
        )

        item = get_products(properties.account, machine_id, time_from, time_to, cut_times)
        item = product_model.ProductResponse(**item)
        LOGGER.debug("selected products: %s", item)

    except HTTPException as http_excep:
        LOGGER.warning(
            "products_get failed: %s, event=%s",
            http_excep,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "products_get failed: %s=%s, event=%s",
            type(excep),
            excep,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise excep

    if item is None:
        return build_response(HTTPStatus.NOT_FOUND, None)
    return item
