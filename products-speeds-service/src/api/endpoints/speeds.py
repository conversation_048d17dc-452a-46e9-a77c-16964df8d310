# System import
from aws_lambda_powertools import Logger, Tracer
from aws_xray_sdk.core import patch_all

# Library import
from fastapi import APIRouter, HTTPException, Query, Request, Response, status, Depends

# Local import
from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

from lib_cloud_sdk.util.common import validate_fast_api_timerange

from common import constants
from common.constants import DESIGN_SPEEDS, SET_SPEEDS, AggregationOptions
from common.get import get_speeds, get_speeds_with_aggregations
from common.models.speeds_model import LegacySpeedResponse, SpeedResponse
from common.parallel import run_parallel

patch_all()

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


@router.get(
    "/speeds/{line_id}",
    response_model=list[SpeedResponse],
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def speeds_get_multiple_equipments(
    request: Request,  # pylint: disable=unused-argument
    response: Response,
    line_id: str,
    time_from: int = Query(..., alias="timeFrom"),
    time_to: int = Query(None, alias="timeTo", description="defaults to timeFrom plus 8h"),
    options: list[constants.AggregationOptions] = Query(None),
    options_str: str = Query(
        None, description="Comma separated options (overwrites single options)", alias="optionsStr"
    ),
    machine_ids: str = Query(..., description="Comma separated machine ids", alias="machineIds"),
    cut_times: bool = Query(True, alias="cutTimes"),
):  # pylint: disable=too-many-positional-arguments, too-many-locals, too-many-arguments
    """
    API entrypoint for getting speeds (designed and set speeds)
    """
    LOGGER.info('Calling get_speeds endpoint with event: "%s".', request.scope.get("aws.event"))
    try:
        properties = CommonShare2ActProperties(
            request.scope.get("aws.event", {}).get("requestContext")
        )

        if options is None and options_str is None:
            options = [AggregationOptions.RAW]

        if options_str is not None:
            options = [item.strip() for item in options_str.split(",")]

        separated_machine_ids = [item.strip() for item in machine_ids.split(",")]

        responses = run_parallel(
            get_speeds_with_aggregations,
            (properties.account, line_id, time_from, time_to, cut_times, options),
            separated_machine_ids,
        )

        if check_for_error(responses):
            response.status_code = status.HTTP_206_PARTIAL_CONTENT
        LOGGER.debug("selected speeds: %s", responses)

    except HTTPException as http_excep:
        LOGGER.warning(
            "speeds_get failed: %s, event=%s",
            http_excep,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "speeds_get failed: %s=%s, event=%s",
            type(excep),
            excep,
            request.scope.get("aws.event"),
            exc_info=True,
        )
        raise excep

    return responses


@router.get(
    "/speeds/{line_id}/{machine_id}",
    response_model=LegacySpeedResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def speeds_get(
    request: Request,
    line_id: str,
    machine_id: str,
    time_from: int,
    time_to: int,
    cut_times: bool | None = True,
    # pylint: disable=unused-argument
):  # pylint: disable=too-many-positional-arguments
    """
    API entrypoint for getting speeds (designed and set speeds)
    """
    LOGGER.info('Calling get_speeds endpoint with event: "%s".', request.scope.get("aws.event"))
    try:
        properties = CommonShare2ActProperties(
            request.scope.get("aws.event", {}).get("requestContext")
        )

        item = get_speeds(machine_id, properties.account, line_id, time_from, time_to, cut_times)

        LOGGER.debug("selected speeds: %s", item)

    except HTTPException as http_excep:
        LOGGER.error("speeds_get failed: %s", http_excep)
        raise http_excep
    except Exception as excep:
        LOGGER.error("speeds_get failed: %s=%s", type(excep), excep)
        raise excep

    return LegacySpeedResponse(
        design_speeds=item.get(DESIGN_SPEEDS, []),
        set_speeds=item.get(SET_SPEEDS, []),
    ).model_dump(exclude_none=True)


def check_for_error(responses: [SpeedResponse]) -> bool:
    response: SpeedResponse
    for response in responses:
        if response.error is not None:
            return True

        # pylint: disable=too-many-boolean-expressions
        if (
            (response.design_speeds and response.design_speeds.error is not None)
            or (response.set_speeds and response.set_speeds.error is not None)
            or (response.speeds and response.speeds.error is not None)
        ):
            return True

    return False
