from enum import Enum

SERVICE_NAME = "products-speeds-service"

PRODUCT_TEXTS = "product_texts"

PRODUCTS = "products"
PRODUCT_TYPE = "product_type"
SET_SPEEDS = "set_speeds"
DESIGN_SPEEDS = "design_speeds"
CURRENT_SPEED = "current_speed"
SPEEDS = "speeds"


class AggregationOptions(str, Enum):
    """
    Options for speeds to aggregate them
    """

    AVERAGE = "avg"
    LATEST = "latest"
    MAX = "max"
    RAW = "raw"
