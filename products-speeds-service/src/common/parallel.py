import os
import time
from contextlib import suppress
from multiprocessing import Process
from pathlib import Path
from random import sample
from string import ascii_letters, digits

from aws_lambda_powertools import Logger, Tracer

from common.models.speeds_model import SpeedResponse

LOGGER = Logger()

TRACER = Tracer()


# pylint: disable=too-many-locals
@TRACER.capture_method(capture_response=False)
def run_parallel(method, param: tuple, machine_ids: [str], timeout: int = 25) -> list:
    """
    Run method parallel with processes for each machine_id in machine_ids using machine_id
    and (param) as parameters
    Processes are killed when the timeout is reached
    """

    old_work_dir = os.getcwd()
    processes, temp_files = [], []
    try:
        # start a process for each machine_id, which writes result into a temp_file
        processes, temp_files = start_processes(machine_ids, method, param)

        # make sure that all processes have finished
        wait_on_processes(processes, timeout)

        # collect responses from processes
        responses = collect_responses(machine_ids, temp_files)
    finally:
        cleanup(processes, temp_files)
        os.chdir(old_work_dir)
    return responses


@TRACER.capture_method(capture_response=False)
def start_processes(machine_ids, method, param):
    """
    Run method parallel with processes for each machine_id in machine_ids using machine_id
    and (param) as parameters
    """
    processes = []
    temp_files = []
    temp_dir = "/tmp"
    os.chdir(temp_dir)
    # create a process per instance
    for machine_id in machine_ids:
        # create a queue for communication
        temp_file = (
            Path(temp_dir)
            .joinpath(machine_id + "_" + "".join(sample(ascii_letters + digits, 10)) + ".json")
            .as_posix()
        )
        temp_files.append(temp_file)
        # create the process, pass instance and connection
        processes.append(
            Process(
                name=machine_id,
                daemon=True,
                target=run_in_process,
                args=[method, machine_id, param, temp_file],
            )
        )
    # start all processes
    for process in processes:
        process.start()
    return processes, temp_files


@TRACER.capture_method(capture_response=False)
def wait_on_processes(processes, timeout):
    """
    wait on processes, processes are killed when the timeout is reached
    """
    for process in processes:
        if timeout > 0:
            LOGGER.debug(f"waiting {timeout} seconds for process {process.name}...")
            start_time = time.time()
            process.join(timeout=timeout)
            timeout -= time.time() - start_time
        else:
            LOGGER.warning(f"timeout reached, killing process {process.name}")
            process.kill()


@TRACER.capture_method(capture_response=False)
def collect_responses(machine_ids, temp_files):
    """
    collect responses from temp_files created by processes
    """
    responses = []
    payload_size = 0
    for temp_file in temp_files:
        if Path(temp_file).is_file():
            with open(temp_file, encoding="utf-8") as tfile:
                file_content = tfile.read()
                file_size = len(file_content)
                # check for 6MB payload limit
                if file_content and file_size > 0 and (payload_size + file_size < 6291556):
                    payload_size += file_size
                    try:
                        responses.append(SpeedResponse.parse_raw(file_content))
                    except BaseException as exc:
                        LOGGER.error(f"response ({file_content}) conversion failed with {exc}")
                else:
                    LOGGER.warning(
                        f"ignored {temp_file}: file_size is {file_size}, "
                        + f"payload_size before {payload_size}"
                    )
    for machine_id in set(machine_ids) ^ {item.equipment_id for item in responses}:
        responses.append(
            SpeedResponse(
                equipment_id=machine_id,
                error="no data retrieved within timeout, "
                + "try to reduce time range and/or number of machine ids",
            )
        )
        LOGGER.warning(f"process for machine id {machine_id} did not deliver data in time")
    return responses


@TRACER.capture_method(capture_response=False)
def cleanup(processes, temp_files):
    """
    cleanup processes and temp_files
    """
    for process in processes:
        with suppress(BaseException):
            process.terminate()
            process.kill()
            process.close()
    for temp_file in temp_files:
        Path(temp_file).unlink(missing_ok=True)


@TRACER.capture_method(capture_response=False)
def run_in_process(method, machine_id, param: tuple, temp_file):
    """
    Run method with parameters machine_id, (param)
    and write resulting SpeedResponse as json to temp_file
    """
    try:
        response: SpeedResponse = method(machine_id, *param)
    except BaseException as exc:
        LOGGER.error(f"process for method {method} failed for machine id {machine_id} with {exc}")
        response = SpeedResponse(equipment_id=machine_id, error=str(exc))
    old_work_dir = os.getcwd()
    try:
        os.chdir("/tmp")
        with open(temp_file, "w", encoding="utf-8") as tfile:
            data = response.json(exclude_none=True)
            tfile.write(data)
            tfile.close()
            del tfile
    except BaseException as exc:
        LOGGER.error(f"process for method {method} failed for machine id {machine_id} with {exc}")
    finally:
        os.chdir(old_work_dir)
        del old_work_dir
