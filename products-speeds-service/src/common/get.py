# Copyright (c) 2020, Syskron GmbH. All rights reserved.

import decimal
import json
import operator
import os
from json import JSONDecodeError
from typing import Any

import boto3
import botocore
from aws_lambda_powertools import Logger, Tracer
from fastapi import HTTPException, status
from machine_data_query.merge_documents import (
    MERGE_TYPE_SUMMARIZE,
    MERGE_TYPE_TAKE_FIRST,
    MERGE_TYPE_TAKE_LAST,
)
from machine_data_query.products_speeds_service_utils import (
    DYNAMODB_TABLE_NAME,
    PRODUCTS_MERGE_CONFIG,
)
from machine_data_query.query_machine_data import query_machine_data
from machine_data_query.query_speeds import query_speeds
from performance_analytics.clients.line_settings.db_client import LineSettingsClient
from performance_analytics.clients.line_settings.exceptions.error import LineSettingsDbClientError
from performance_analytics.clients.line_settings.exceptions.not_found import (
    LineSettingsNotFoundError,
)

from common.constants import (
    DESIGN_SPEEDS,
    PRODUCT_TEXTS,
    PRODUCT_TYPE,
    SET_SPEEDS,
    SPEEDS,
    AggregationOptions,
)
from common.models.speeds_model import (
    CollectiveSpeedModel,
    QuerySpeedResponse,
    Speed,
    SpeedResponse,
)

LOGGER = Logger()

S3_CLIENT = boto3.client("s3")

STAGE = os.environ.get("AWS_STAGE", "dev")
BUCKET_NAME = "rk-zait-"

TRACER = Tracer()

PRODUCTS_PREPROCESS_CONFIG = {
    "time_property_keys": ["time_from", "time_to"],
    "time_list_keys": ["product_type"],
    "timestamps": {
        "end": "time_to",
    },
}

SPEEDS_PREPROCESS_CONFIG = {
    "time_property_keys": ["time_from", "time_to"],
    "time_list_keys": [
        {"key": "speeds", "trim_end": 0},
        # speeds are backwards so the amount of speeds go from
        # last item until the start time of the current item
        {"key": "design_speeds"},
        {"key": "set_speeds"},
    ],
    "timestamps": {"end": "time_to"},
}

FALLBACK_SPEEDS_PREPROCESS_CONFIG = {
    "time_property_keys": ["time_from", "time_to"],
    "time_list_keys": [
        {"key": "speeds", "trim_end": 0},
        # speeds are backwards so the amount of speeds go from
        # last item until the start time of the current item
        {"key": "design_speeds"},
        {"key": "set_speeds"},
    ],
    "timestamps": {
        "end": "time_to",
    },
}

FALLBACK_SPEEDS_MERGE_CONFIG = {
    "group_by_keys": [
        "eq_id",
    ],
    "merge_rules": [
        {
            "source_key": "time_from",
            "type": MERGE_TYPE_TAKE_FIRST,
        },
        {
            "source_key": "time_to",
            "type": MERGE_TYPE_TAKE_LAST,
        },
        {
            "source_keys": ["time_from", "time_to"],
            "target_key": "document_summary",
            "type": MERGE_TYPE_SUMMARIZE,
        },
        {
            "source_key": "customer",
            "type": MERGE_TYPE_TAKE_FIRST,
        },
        {
            "source_key": "eq_id",
            "type": MERGE_TYPE_TAKE_FIRST,
        },
        {
            "source_key": "nominal_speed",
            "type": MERGE_TYPE_TAKE_FIRST,
        },
    ],
}


@TRACER.capture_method(capture_response=False)
def _query_products(args, machine_id):
    args["merge_config"] = PRODUCTS_MERGE_CONFIG
    args["preprocess_config"] = PRODUCTS_PREPROCESS_CONFIG
    args["data_keys"] = ["product_type"]
    response, _ = query_machine_data(args)
    machine_document = None
    if response is not None and isinstance(response, dict):
        machine_document = response.get(machine_id, None)

    return machine_document


@TRACER.capture_method(capture_response=False)
def _query_speeds(
    time_from: int, time_to: int, account: str, machine_id: str, cut_times: bool
) -> dict[str, Any] | None:
    speeds, design_speeds, set_speeds = query_speeds(
        time_from,
        time_to,
        account,
        machine_id,
        skip_speed_correction=False,
        query_current_speeds=True,
        query_design_speeds=True,
        query_set_speeds=True,
        cut_on_boundaries=cut_times,
    )
    machine_document = None
    if design_speeds and set_speeds:
        machine_document = {
            "time_from": time_from,
            "time_to": time_to,
            "customer": account,
            "equipment_id": machine_id,
            "design_speeds": [item.model_dump(exclude_none=True) for item in design_speeds.items],
            "set_speeds": [item.model_dump(exclude_none=True) for item in set_speeds.items],
            "speeds": [item.model_dump(exclude_none=True) for item in speeds.items],
        }
    return machine_document


@TRACER.capture_method(capture_response=False)
def _get_product_texts(account, machine_id):
    """Returns product texts by machine_id"""

    if not STAGE:
        LOGGER.error("No AWS_STAGE provided in environment variables!!!")
        return None, None

    full_bucket_name = BUCKET_NAME + STAGE
    bucket_folder = f"account={account}/machine={machine_id}"
    file_name = f"{bucket_folder}/{PRODUCT_TEXTS}.json"

    texts = {}

    LOGGER.debug("Get product texts json for %s", file_name)

    try:
        result = S3_CLIENT.get_object(Bucket=full_bucket_name, Key=file_name)
        content = result["Body"].read()
        texts = json.loads(content)

    except botocore.exceptions.ClientError as exception:
        error_code = exception.response.get("Error", {}).get("Code")

        if error_code in ("NoSuchKey", "AccessDenied"):
            # Key not found
            LOGGER.warning("Product texts not found for %s", file_name)
        elif error_code == "NoSuchBucket":
            # Bucket not found
            LOGGER.warning('No Bucket named "%s"', full_bucket_name)
        else:
            # Something else has gone wrong.
            LOGGER.warning("Something went wrong for %s", file_name)

        LOGGER.warning("See error code: %s", error_code)

    except JSONDecodeError as json_error:
        LOGGER.warning("JsonDecodeError: %s", json_error, exc_info=True)

    except UnicodeDecodeError as unicode_error:
        LOGGER.warning("UnicodeDecodeError: %s", unicode_error, exc_info=True)

    return texts


@TRACER.capture_method(capture_response=False)
def _process_list(
    document, list_names, item_name, extra=None, extra_name="", extra_fallback=""
):  # pylint: disable=too-many-positional-arguments
    processed_list = {}

    for list_name in list_names:
        processed_list[list_name] = []
        last_value = -1
        for item in document.get(list_name, []):
            try:
                item[item_name] = _decimal_default(item.get(item_name, 0))
                if item[item_name] == last_value:
                    processed_list[list_name][-1]["end"] = item["end"]
                    continue
                if extra is not None:
                    item[extra_name] = extra.get(
                        str(item.get(item_name, 0)), f'{extra_fallback}="{item.get(item_name, 0)}"'
                    )
            except TypeError:
                LOGGER.exception("TypeError in %s", item_name)
                continue
            processed_list[list_name].append(item)
            last_value = item[item_name]

    return processed_list


@TRACER.capture_method(capture_response=False)
def _get_customer_settings_configuration(account, line_id):
    try:
        line_settings_client = LineSettingsClient()
        return line_settings_client.get_line_settings(account, line_id)
    except LineSettingsNotFoundError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(exc)) from exc
    except LineSettingsDbClientError as exc:
        raise HTTPException(status_code=500, detail=str(exc)) from exc


@TRACER.capture_method(capture_response=False)
def _process_speeds_fallback_fallback(processed_speeds, document, account, line_id, machine_id):
    configuration = _get_customer_settings_configuration(account, line_id)

    if not processed_speeds.get(DESIGN_SPEEDS):
        default_design_speed = 0
        try:
            default_design_speed = int(
                configuration.settings.machine_settings[machine_id].nominal_speed
            )
        except BaseException:
            default_design_speed = 25000

        processed_speeds[DESIGN_SPEEDS] = [
            {
                "speed": default_design_speed,
                "start": document["time_from"],
                "end": document["time_to"],
            }
        ]

    if not processed_speeds.get(SET_SPEEDS):
        processed_speeds[SET_SPEEDS] = [
            {"speed": 0, "start": document["time_from"], "end": document["time_to"]}
        ]

    if not processed_speeds.get(SPEEDS):
        processed_speeds[SPEEDS] = [
            {"current_speed": 0, "start": document["time_from"], "end": document["time_to"]}
        ]

    return processed_speeds


# pylint: disable=no-else-return
def _decimal_default(obj):
    if isinstance(obj, decimal.Decimal):
        # Check whether decimal is integer
        if obj.to_integral_value() == obj:
            result = int(obj)
        else:
            result = float(obj)
        return result
    elif isinstance(obj, int):
        return obj
    else:
        raise TypeError(f'Could not parse type="{type(obj)}" of "{obj}"')


@TRACER.capture_method(capture_response=False)
def get_products(account, machine_id, time_from, time_to, cut_times: bool = False):
    """
    retrieve the products
    """
    response = {}

    args = {}
    args["table_name"] = DYNAMODB_TABLE_NAME
    args["account"] = account
    args["machine_ids"] = [machine_id]
    args["start"] = time_from
    args["cut_config"] = {"cut_start_end_to_timeframe": cut_times}
    if time_to:
        args["end"] = time_to

    response = _query_products(args, machine_id)
    LOGGER.debug("response: %s", response)
    if response is None:
        return None

    texts = _get_product_texts(account, machine_id)

    product_types = {}
    fallback_text = "No text for product"
    processed_product_types = _process_list(
        response, [PRODUCT_TYPE], "product_id", texts, "product_text", fallback_text
    )
    LOGGER.debug("processed_product_types: %s", processed_product_types)
    # NOTE: get rid of products, call it also product_type
    product_types["products"] = processed_product_types.get("product_type")

    return product_types


@TRACER.capture_method(capture_response=False)
def get_speeds(
    machine_id, account, line_id, time_from, time_to, cut_times: bool = False
):  # pylint: disable=too-many-positional-arguments
    """
    retrieve the speeds
    """
    response = _query_speeds(time_from, time_to, account, machine_id, cut_times)
    if response is None:
        return None

    processed_speeds = _process_list(response, [DESIGN_SPEEDS, SET_SPEEDS], "speed")
    processed_current_speeds = _process_list(response, [SPEEDS], "current_speed")

    processed_speeds[SPEEDS] = processed_current_speeds.get(SPEEDS, [])

    processed_speeds = _process_speeds_fallback_fallback(
        processed_speeds, response, account, line_id, machine_id
    )

    processed_speeds[SPEEDS] = [
        Speed(
            speed=item.get("current_speed"),
            start=item.get("start"),
            end=item.get("end", None),
            duration=item.get("duration", None),
            ongoing=item.get("ongoing", None),
        )
        for item in processed_speeds.get(SPEEDS)
    ]

    return QuerySpeedResponse(
        equipment_id=machine_id,
        design_speeds=processed_speeds.get(DESIGN_SPEEDS, []),
        set_speeds=processed_speeds.get(SET_SPEEDS, []),
        speeds=processed_speeds.get(SPEEDS, []),
    ).model_dump(exclude_none=True)


# pylint: disable=too-many-arguments
@TRACER.capture_method(capture_response=False)
def get_speeds_with_aggregations(  # pylint: disable=too-many-positional-arguments
    machine_id, account, line_id, time_from, time_to, cut_times: bool, options: [str]
) -> SpeedResponse:
    """
    retrieve speeds over time
    """
    if len(options) == 1 and AggregationOptions.LATEST in options:
        # Optimize query if only latest is wanted. Query lib is making sure always the "active"
        # item is returned so querying 1 ms is enough
        speeds = get_speeds(machine_id, account, line_id, time_to - 1, time_to, False)
    else:
        speeds = get_speeds(machine_id, account, line_id, time_from, time_to, cut_times)

    response: SpeedResponse = SpeedResponse(
        equipment_id=machine_id,
        design_speeds=CollectiveSpeedModel(raw=speeds.get(DESIGN_SPEEDS)),
        set_speeds=CollectiveSpeedModel(raw=speeds.get(SET_SPEEDS)),
        speeds=CollectiveSpeedModel(raw=speeds.get(SPEEDS)),
    )

    if AggregationOptions.AVERAGE in options:
        response.design_speeds.average = Speed(
            speed=determine_average(response.design_speeds.raw), start=time_from, end=time_to
        )
        response.set_speeds.average = Speed(
            speed=determine_average(response.set_speeds.raw), start=time_from, end=time_to
        )
        response.speeds.average = Speed(
            speed=determine_average(response.speeds.raw), start=time_from, end=time_to
        )
    if AggregationOptions.LATEST in options:
        response.design_speeds.latest = response.design_speeds.raw[-1] or None
        response.set_speeds.latest = response.set_speeds.raw[-1] or None
        response.speeds.latest = response.speeds.raw[-1] or None

    if AggregationOptions.MAX in options:
        response.design_speeds.max = determine_max(response.design_speeds.raw)
        response.set_speeds.max = determine_max(response.set_speeds.raw)
        response.speeds.max = determine_max(response.speeds.raw)

    if AggregationOptions.RAW not in options:
        response.design_speeds.raw = None
        response.set_speeds.raw = None
        response.speeds.raw = None

    return response


@TRACER.capture_method(capture_response=False)
def determine_max(items: None or list[Speed]) -> Speed or None:
    """
    Determine max value of items
    """
    return max(items, key=operator.attrgetter("speed"))


@TRACER.capture_method(capture_response=False)
def determine_average(items: None or list[Speed]) -> Speed or None:
    """
    Determine the average of a list of items with the properties start, end and duration
    """
    if items is None:
        return None

    weights = 0
    numerator = 0
    for item in items:
        item.duration = (item.end - item.start) or 0
        weights += item.duration
        numerator += item.speed * item.duration

    return round(numerator / weights, 2)
