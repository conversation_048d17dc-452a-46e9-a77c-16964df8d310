# Library import
from pydantic import BaseModel, Field, ConfigDict


class Speed(BaseModel):
    """
    Speed model
    """

    speed: float
    start: int
    end: int | None = None
    duration: int | None = None
    ongoing: bool | None = None


class CollectiveSpeedModel(BaseModel):
    """
    Speed values with additional aggregations
    """

    raw: list[Speed] | None = None
    average: Speed | None = None
    max: Speed | None = None
    latest: Speed | None = None
    error: str | None = None


class QuerySpeedResponse(BaseModel):
    """
    Speeds response
    """

    equipment_id: str
    design_speeds: list[Speed] | None = None
    set_speeds: list[Speed] | None = None
    speeds: list[Speed] | None = None


class LegacySpeedResponse(BaseModel):
    """
    Speeds response
    """

    design_speeds: list[Speed] = []
    set_speeds: list[Speed] = []


class SpeedResponse(BaseModel):
    """
    Speeds response
    """

    model_config = ConfigDict(populate_by_name=True)

    equipment_id: str = Field(alias="equipmentId")
    error: str | None = None
    design_speeds: CollectiveSpeedModel | None = Field(default=None, alias="designSpeed")
    set_speeds: CollectiveSpeedModel | None = Field(default=None, alias="setSpeeds")
    speeds: CollectiveSpeedModel | None = None
