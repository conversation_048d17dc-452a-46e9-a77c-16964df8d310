#POETRY

[tool.poetry]
name = "products-speeds-service"
version = "0.1.0"
description = ""
authors = ["Syskron - Performance Team"]

[[tool.poetry.source]]
name = "syskron-jfrog"
url = "https://syskronx.jfrog.io/syskronx/api/pypi/pypi/simple"
priority = "primary"

[[tool.poetry.source]]
name = "PyPI"
priority = "explicit"

[tool.poetry.dependencies]
python = "~3.13"
requests = "*"
wrapt = "^1"
aws-xray-sdk = "^2"
lib-sysxds-common = "^0"
fastapi = "^0"
mangum = "^0"
pydantic = "^2"
aws-lambda-powertools = "^2"
lib-performance-analytics = "^32"
ft2-cloud-sdk = "^16"
Secweb = "*"


[tool.poetry.group.dev.dependencies]
boto3 = "^1.35"
mock = "*"
pre-commit = "*"
pylint = "^3"
pytest = "^8"
pytest-cov = "*"
black = "^24"
flake8 = "^7"
moto = "*"
httpx = "*"
uvicorn = "^0"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

#BLACK

[tool.black]
line-length = 100

#PYTEST

[tool.pytest.ini_options]
# addopts = " -p no:cacheprovider -vv -rf --strict --durations 10 --color yes"
filterwarnings = [
  "error",
  "ignore::DeprecationWarning",
  "ignore::PendingDeprecationWarning",
  "ignore::ImportWarning",
  "ignore::pytest.PytestUnraisableExceptionWarning"
]
log_level = "DEBUG"
log_format = "%(asctime)s %(levelname)s %(message)s"
log_date_format = "%Y-%m-%d %H:%M:%S"
pythonpath = "./src"

#COVERAGE

[tool.coverage.run]
branch = true
omit = [
  "test/*",
  "*/__init__.py",
  "*/_version.py",
]

[tool.coverage.report]
precision = 2
fail_under = 90
