# Performance Analytics KPI Service

## Overview

### Get Machine-Report for customer

```json
/performance-analytics/machine-report/performance/<line_id>/<machine_id>?time_from=<utc_timestamp_in_ms>&time_to=<utc_timestamp_in_ms>
```

### Get Machine-Report for Insight model

```json
/performance-analytics/machine-report/performance/<line_id>/<machine_id>?time_from=<utc_timestamp_in_ms>?insight_model=<bool>
```

## Debugging kpi client on local (w/ Postman)

1. The common share2act properties don't work since we are not running through the real api gateway with real requests. Instead we are running through our local FastAPI instance. So its needed to disable this code part and mock all necessary data in the [code:](src/api/endpoints/legacy_machine_report.py)
    ```
    properties = CommonShare2ActProperties(request.scope.get('aws.event', {}).get('requestContext')) 
    ```
   Change `properties.account` to the account name you are going to query
2. [Run FastAPI on your machine](https://krones-digital.atlassian.net/wiki/spaces/DEV/pages/********/How+to+use+FastAPI+locally)
3. Find the request url:
![s2a-request-url-kpi.png](doc%2Fs2a-request-url-kpi.png)
4. Change hostname with localhost (127.0.0.1) and the corresponding port number FastAPI running on.Then, paste it to the Postman
