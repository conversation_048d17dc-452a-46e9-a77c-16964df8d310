import os

import yaml
from fastapi.openapi.utils import get_openapi

from lambda_function import app

oas_schema = get_openapi(title="performance_analytics-kpi-service", version="1", routes=app.routes)

doc_path = os.path.join(os.getcwd(), "src/api/documentation/open_api.yml")
with open(file=doc_path, mode="w", encoding="UTF-8") as oas_yml:
    oas_yml.write(yaml.dump(oas_schema))
    oas_yml.close()
