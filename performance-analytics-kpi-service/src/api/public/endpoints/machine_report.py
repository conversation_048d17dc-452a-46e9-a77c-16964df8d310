from aws_lambda_powertools import Logger, Tracer
from aws_xray_sdk.core import patch_all
from fastapi import APIRouter, Depends, Request, Query, HTTPException, status, Path

from lib_cloud_sdk.util.common import validate_fast_api_timerange
from lib_dtm_client.clients.query_handler import Query<PERSON>andler
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
from lib_kpi_config_client.models.api_models import KpiResult, ApiKpiModelTimeValidity
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelScope
from performance_analytics.models.camel_case_model import CamelCaseModel
from performance_analytics.models.kpi_models import ReducedMachineStateProperties
from performance_analytics.models.shared_models import DEFAULT_NOMINAL_SPEED

from common.join_time_series import JoinTimeS<PERSON>
from common.kpi_client import K<PERSON><PERSON><PERSON>
from common.line_settings import get_line_settings

patch_all()
LOGGER = Logger()
router = APIRouter()
TRACER = Tracer()


class MachineReportPublicAPIResponse(CamelCaseModel):
    account: str
    equipment_id: str
    kpi_model_id: str
    time_from: int
    time_to: int
    units_produced: int = 0
    units_defect: int = 0
    kpi_result: KpiResult = {}
    reduced_machine_state: ReducedMachineStateProperties = {}


# pylint: disable=too-many-positional-arguments, too-many-arguments, too-many-locals
@router.get(
    path="/machine-reports/{line_id}/{machine_id}",
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def machine_report(  # pylint: disable=too-many-statements,too-many-branches
    request: Request,
    line_id: str = Path(..., description="The id of the line."),
    machine_id: str = Path(..., description="The id of the machine."),
    customer: str = Query(..., description="The account of the machine."),
    kpi_model_id: str = Query(description="Id of the kpi model."),
    time_from: int = Query(..., description="Represents the start time in Epoch milliseconds."),
    time_to: int = Query(..., description="Represents the end time in Epoch milliseconds."),
    line_kpi: int = Query(
        0,
        description="Parameter which determines whether we should calculate line kpis.",
    ),
    language: str = Query("en", description="Language to be used for resolving touch messages."),
    with_children: bool | None = Query(
        True,
        description="Parameter which determines whether we should query the children as well.",
    ),
    apply_manual_changes: bool | None = Query(
        True,
        description="Set to true if manual changes should be applied already or false if "
        + "original values should not be overwritten",
    ),
):
    """Get Machine Report for a given machine and timeframe."""
    LOGGER.info(
        "Calling machine_report Public API endpoint with event: %s",
        request.scope.get("aws.event", {}),
    )

    kpi_model = None
    kpi_config_api_client = KpiConfigApiClient()
    assigned_kpi_models_in_timerange: list[ApiKpiModelTimeValidity] = (
        kpi_config_api_client.get_kpi_models_assigned_in_timerange(
            account=customer, line_id=line_id, start=time_from, end=time_to
        )
    )
    for model in assigned_kpi_models_in_timerange:
        if model.kpi_model_id == kpi_model_id:
            kpi_model = KpiModel(
                kpi_model_id=model.kpi_model_id, kpi_model_type=model.kpi_model_type
            )

    if not kpi_model:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="kpi model not assigned to this line in the given timerange",
        )

    query_handler = QueryHandler()
    downtimes_and_speeds = query_handler.get_downtimes_with_speeds_for_machine(
        account=customer,
        machine_id=machine_id,
        time_from=time_from,
        time_to=time_to,
        kpi_model=kpi_model,
        cut_start_end_to_timeframe=1,
        with_children=with_children,
        language=language,
        apply_manual_changes=apply_manual_changes,
        line_kpi=line_kpi,
        line_id=line_id,
    )
    for key in ["speeds", "design_speeds", "set_speeds"]:
        for speed in downtimes_and_speeds[key]:
            speed["ongoing"] = 1 if speed.get("ongoing", False) else None

    time_series = JoinTimeSeries()
    downtimes_and_speeds["joined_time_series"] = time_series.join(downtimes_and_speeds)
    line_settings = get_line_settings(account=customer, line_id=line_id)

    machine_setting = (
        line_settings.settings.machine_settings.get(machine_id)
        if line_settings.settings.machine_settings
        else None
    )
    if machine_setting:
        nominal_speed = machine_setting.nominal_speed
    else:
        nominal_speed = DEFAULT_NOMINAL_SPEED
        LOGGER.warning(
            "No nominalspeed configured for",
            extra={
                "account": line_settings.account,
                "line_id": line_settings.line_id,
                "machine_id": machine_id,
                "default_nominal_speed": DEFAULT_NOMINAL_SPEED,
            },
        )

    kpi_client = KpiClient(downtimes_and_speeds, nominal_speed)
    kpi_result = kpi_client.calculate_kpis(
        kpi_model=kpi_model,
        kpi_model_scope=KpiModelScope.LINE if line_kpi else KpiModelScope.MACHINE,
        account=customer,
    )

    return MachineReportPublicAPIResponse.model_validate(
        {
            "account": customer,
            "equipment_id": machine_id,
            "kpi_model_id": kpi_model.kpi_model_id,
            "time_from": time_from,
            "time_to": time_to,
            "units_produced": downtimes_and_speeds["units_produced"],
            "units_defect": downtimes_and_speeds["units_defect"],
            "kpi_result": kpi_result["kpi_result"],
            "reduced_machine_state": ReducedMachineStateProperties.model_validate(
                kpi_result["reduced_downtimes"]
            ),
        }
    )
