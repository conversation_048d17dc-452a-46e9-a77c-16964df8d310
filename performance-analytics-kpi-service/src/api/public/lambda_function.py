from Secweb import <PERSON><PERSON><PERSON><PERSON>
from Secweb.CacheControl import CacheControl
from aws_lambda_powertools import Tracer
from fastapi import FastAP<PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from mangum import Mangum

from lib_cloud_sdk.util.sentry.init_fastapi import init_sentry
from lib_public_api_utilities.error_handling.gravitee_error_handler import (
    add_exception_handlers,
)
from .endpoints import machine_report  # pylint: disable=import-error


TRACER = Tracer()

init_sentry()

app = FastAPI(
    version="1.0.0",
    title="Performance Analytics KPI Service Public API",
)


router = APIRouter()
router.include_router(machine_report.router, prefix="/v1/performance-public-api-mvp")

app.include_router(router)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token"],
)
add_exception_handlers(app)

SecWeb(app=app, Option={"corp": "same-site"})
app.add_middleware(CacheControl, Option={"no-cache": True})

lambda_handler = Mangum(app)
