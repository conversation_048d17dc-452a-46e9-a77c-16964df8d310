{"openapi": "3.1.0", "info": {"title": "Performance Analytics KPI Service Public API", "version": "1.0.0"}, "paths": {"/v1/performance-public-api-mvp/machine-reports/{line_id}/{machine_id}": {"get": {"summary": "Machine Report", "description": "Get Machine Report for a given machine and timeframe.", "operationId": "machine_report_v1_performance_public_api_mvp_machine_reports__line_id___machine_id__get", "parameters": [{"name": "line_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The id of the line.", "title": "Line Id"}, "description": "The id of the line."}, {"name": "machine_id", "in": "path", "required": true, "schema": {"type": "string", "description": "The id of the machine.", "title": "Machine Id"}, "description": "The id of the machine."}, {"name": "customer", "in": "query", "required": true, "schema": {"type": "string", "description": "The account of the machine.", "title": "Customer"}, "description": "The account of the machine."}, {"name": "kpi_model_id", "in": "query", "required": true, "schema": {"type": "string", "description": "Id of the kpi model.", "title": "Kpi Model Id"}, "description": "Id of the kpi model."}, {"name": "time_from", "in": "query", "required": true, "schema": {"type": "integer", "description": "Represents the start time in Epoch milliseconds.", "title": "Time From"}, "description": "Represents the start time in Epoch milliseconds."}, {"name": "time_to", "in": "query", "required": true, "schema": {"type": "integer", "description": "Represents the end time in Epoch milliseconds.", "title": "Time To"}, "description": "Represents the end time in Epoch milliseconds."}, {"name": "line_kpi", "in": "query", "required": false, "schema": {"type": "integer", "description": "Parameter which determines whether we should calculate line kpis.", "default": 0, "title": "Line Kpi"}, "description": "Parameter which determines whether we should calculate line kpis."}, {"name": "language", "in": "query", "required": false, "schema": {"type": "string", "description": "Language to be used for resolving touch messages.", "default": "en", "title": "Language"}, "description": "Language to be used for resolving touch messages."}, {"name": "with_children", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Parameter which determines whether we should query the children as well.", "default": true, "title": "With Children"}, "description": "Parameter which determines whether we should query the children as well."}, {"name": "apply_manual_changes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Set to true if manual changes should be applied already or false if original values should not be overwritten", "default": true, "title": "Apply Manual Changes"}, "description": "Set to true if manual changes should be applied already or false if original values should not be overwritten"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}