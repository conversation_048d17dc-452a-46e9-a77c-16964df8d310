components:
  schemas:
    BaseDowntime:
      properties:
        category:
          default: undefined
          title: Category
          type: string
        classification:
          default: undefined
          title: Classification
          type: string
        duration:
          default: 0
          title: Duration
          type: integer
        end:
          default: 0
          title: End
          type: integer
        endUncut:
          anyOf:
          - type: integer
          - type: 'null'
          title: End Uncut
        manualChanges:
          anyOf:
          - $ref: '#/components/schemas/ManualChanges'
          - type: 'null'
        rawValues:
          anyOf:
          - $ref: '#/components/schemas/RawValues'
          - type: 'null'
        reasonCodeId:
          anyOf:
          - type: string
          - type: 'null'
          title: Reasoncodeid
        start:
          default: 0
          title: Start
          type: integer
        startUncut:
          anyOf:
          - type: integer
          - type: 'null'
          title: Start Uncut
      title: BaseDowntime
      type: object
    Count:
      properties:
        categories:
          additionalProperties:
            type: integer
          default: {}
          title: Categories
          type: object
        classifications:
          additionalProperties:
            type: integer
          default: {}
          title: Classifications
          type: object
      title: Count
      type: object
    CurrentSpeed:
      properties:
        currentSpeed:
          default: 0
          title: Currentspeed
          type: integer
        end:
          default: 0
          title: End
          type: integer
        start:
          default: 0
          title: Start
          type: integer
        unitsDefect:
          anyOf:
          - type: integer
          - type: 'null'
          title: Unitsdefect
        unitsProduced:
          anyOf:
          - type: integer
          - type: 'null'
          title: Unitsproduced
        unitsTotal:
          anyOf:
          - type: integer
          - type: 'null'
          title: Unitstotal
      title: CurrentSpeed
      type: object
    DmmMessage:
      description: DmmMessage
      properties:
        dmmMessageId:
          title: Dmmmessageid
          type: integer
        startTime:
          anyOf:
          - type: integer
          - type: 'null'
          title: Starttime
      title: DmmMessage
      type: object
    Duration:
      properties:
        categories:
          additionalProperties:
            type: integer
          default: {}
          title: Categories
          type: object
        classifications:
          additionalProperties:
            type: integer
          default: {}
          title: Classifications
          type: object
      title: Duration
      type: object
    Grouped:
      properties:
        days:
          additionalProperties:
            $ref: '#/components/schemas/KpiResult'
          title: Days
          type: object
      required:
      - days
      title: Grouped
      type: object
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          title: Detail
          type: array
      title: HTTPValidationError
      type: object
    Kpi:
      description: Data model for one KPI.
      properties:
        duration:
          anyOf:
          - type: integer
          - type: 'null'
          title: Duration
        name:
          title: Name
          type: string
        number:
          anyOf:
          - type: number
          - type: 'null'
          title: Number
        percent:
          anyOf:
          - type: number
          - type: 'null'
          title: Percent
      required:
      - name
      title: Kpi
      type: object
    KpiResult:
      description: Data model for the result of the kpi calculation.
      properties:
        additionalKpis:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Additionalkpis
        meanTimes:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Meantimes
        quotients:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Quotients
        speeds:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Speeds
        unitTypes:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Unittypes
        units:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Units
        waterfallBases:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Waterfallbases
        waterfallLosses:
          anyOf:
          - items:
              $ref: '#/components/schemas/Kpi'
            type: array
          - type: 'null'
          title: Waterfalllosses
      title: KpiResult
      type: object
    MachineReportResponse:
      properties:
        account:
          title: Account
          type: string
        downtimes:
          default: []
          items:
            $ref: '#/components/schemas/ReportDowntime'
          title: Downtimes
          type: array
        equipmentId:
          title: Equipmentid
          type: string
        kpiModelId:
          title: Kpimodelid
          type: string
        kpiResult:
          $ref: '#/components/schemas/KpiResult'
          default: {}
        reducedMachineState:
          $ref: '#/components/schemas/ReducedMachineStateProperties'
          default: {}
        speeds:
          default: []
          items:
            $ref: '#/components/schemas/CurrentSpeed'
          title: Speeds
          type: array
        timeFrom:
          title: Timefrom
          type: integer
        timeTo:
          title: Timeto
          type: integer
        unitsDefect:
          default: 0
          title: Unitsdefect
          type: integer
        unitsProduced:
          default: 0
          title: Unitsproduced
          type: integer
      required:
      - account
      - equipmentId
      - kpiModelId
      - timeFrom
      - timeTo
      title: MachineReportResponse
      type: object
    ManualChanges:
      properties:
        category:
          anyOf:
          - type: string
          - type: 'null'
          title: Category
        classification:
          anyOf:
          - type: string
          - type: 'null'
          title: Classification
        systemRelevance:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Systemrelevance
        timestealer:
          anyOf:
          - $ref: '#/components/schemas/Timestealer'
          - type: 'null'
      title: ManualChanges
      type: object
    MessageModel:
      type: object
    Ratio:
      properties:
        categories:
          additionalProperties:
            type: number
          default: {}
          title: Categories
          type: object
        classifications:
          additionalProperties:
            type: number
          default: {}
          title: Classifications
          type: object
      title: Ratio
      type: object
    RawValues:
      description: Response data model for tuple of raw values
      properties:
        aggregatedState:
          default: -1
          title: Aggregatedstate
          type: integer
        mode:
          default: -1
          title: Mode
          type: integer
        program:
          default: -1
          title: Program
          type: integer
        state:
          default: -1
          title: State
          type: integer
      title: RawValues
      type: object
    ReducedMachineStateProperties:
      properties:
        count:
          anyOf:
          - $ref: '#/components/schemas/Count'
          - type: 'null'
        duration:
          anyOf:
          - $ref: '#/components/schemas/Duration'
          - type: 'null'
        overallDuration:
          default: 0
          title: Overallduration
          type: integer
        ratio:
          anyOf:
          - $ref: '#/components/schemas/Ratio'
          - type: 'null'
      title: ReducedMachineStateProperties
      type: object
    ReportDowntime:
      properties:
        category:
          default: undefined
          title: Category
          type: string
        classification:
          default: undefined
          title: Classification
          type: string
        downtimes:
          anyOf:
          - items:
              $ref: '#/components/schemas/BaseDowntime'
            type: array
          - type: 'null'
          title: Downtimes
        duration:
          default: 0
          title: Duration
          type: integer
        end:
          default: 0
          title: End
          type: integer
        endUncut:
          anyOf:
          - type: integer
          - type: 'null'
          title: End Uncut
        manualChanges:
          anyOf:
          - $ref: '#/components/schemas/ManualChanges'
          - type: 'null'
        message:
          anyOf:
          - $ref: '#/components/schemas/ResponseUnifiedMessage'
          - type: 'null'
        rawValues:
          anyOf:
          - $ref: '#/components/schemas/RawValues'
          - type: 'null'
        reasonCodeId:
          anyOf:
          - type: string
          - type: 'null'
          title: Reasoncodeid
        start:
          default: 0
          title: Start
          type: integer
        startUncut:
          anyOf:
          - type: integer
          - type: 'null'
          title: Start Uncut
      title: ReportDowntime
      type: object
    ResponseUnifiedMessage:
      properties:
        attention:
          anyOf:
          - type: string
          - type: 'null'
          title: Attention
        configId:
          title: Configid
          type: string
        dataSourceId:
          anyOf:
          - type: string
          - type: 'null'
          title: Datasourceid
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        diagnosis:
          anyOf:
          - type: string
          - type: 'null'
          title: Diagnosis
        equipmentId:
          anyOf:
          - type: string
          - type: 'null'
          title: Equipmentid
        instruction:
          anyOf:
          - type: string
          - type: 'null'
          title: Instruction
        keyword:
          anyOf:
          - type: string
          - type: 'null'
          title: Keyword
        lastChange:
          anyOf:
          - type: integer
          - type: 'null'
          title: Lastchange
        messageClass:
          anyOf:
          - type: string
          - type: 'null'
          title: Messageclass
        messageConfig:
          anyOf:
          - type: object
          - type: 'null'
          title: Messageconfig
        messageNr:
          anyOf:
          - type: string
          - type: 'null'
          title: Messagenr
        messageText:
          anyOf:
          - type: string
          - type: 'null'
          title: Messagetext
        messageType:
          title: Messagetype
          type: string
        status:
          anyOf:
          - type: string
          - type: 'null'
          title: Status
      required:
      - configId
      - messageType
      title: ResponseUnifiedMessage
      type: object
    Time:
      properties:
        end_time:
          title: End Time
          type: string
        start_time:
          title: Start Time
          type: string
      required:
      - start_time
      - end_time
      title: Time
      type: object
    Timestealer:
      description: Response data model for Timestealer
      properties:
        causingClassification:
          anyOf:
          - type: string
          - type: 'null'
          title: Causingclassification
        equipmentId:
          title: Equipmentid
          type: string
        message:
          anyOf:
          - $ref: '#/components/schemas/MessageModel'
          - type: 'null'
        probability:
          title: Probability
          type: string
        startTime:
          anyOf:
          - type: integer
          - type: 'null'
          title: Starttime
      required:
      - equipmentId
      - probability
      title: Timestealer
      type: object
    TrendAnalysisBody:
      properties:
        kpi_model_id:
          title: Kpi Model Id
          type: string
        language:
          title: Language
          type: string
        line:
          title: Line
          type: string
        line_kpi:
          default: 0
          title: Line Kpi
          type: integer
        machines:
          items:
            type: string
          title: Machines
          type: array
        time:
          $ref: '#/components/schemas/Time'
        timezone:
          default: Etc/UTC
          title: Timezone
          type: string
      required:
      - machines
      - time
      - language
      - line
      - kpi_model_id
      title: TrendAnalysisBody
      type: object
    TrendAnalysisResponse:
      properties:
        kpiModelId:
          title: Kpimodelid
          type: string
        machineIds:
          additionalProperties:
            $ref: '#/components/schemas/Grouped'
          default: {}
          title: Machineids
          type: object
      required:
      - kpiModelId
      title: TrendAnalysisResponse
      type: object
    UnifiedMessage:
      description: UnifiedMessage
      properties:
        endTime:
          anyOf:
          - type: integer
          - type: 'null'
          title: Endtime
        forceClosed:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Forceclosed
        instanceId:
          anyOf:
          - type: string
          - type: 'null'
          title: Instanceid
        isDowntimeCause:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Isdowntimecause
        keyword:
          anyOf:
          - type: string
          - type: 'null'
          title: Keyword
        messageClass:
          anyOf:
          - type: string
          - type: 'null'
          title: Messageclass
        messageId:
          anyOf:
          - type: string
          - type: 'null'
          title: Messageid
        sourceId:
          anyOf:
          - type: string
          - type: 'null'
          title: Sourceid
        startTime:
          anyOf:
          - type: integer
          - type: 'null'
          title: Starttime
      title: UnifiedMessage
      type: object
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          title: Location
          type: array
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
      required:
      - loc
      - msg
      - type
      title: ValidationError
      type: object
    ZaitMessage:
      description: ZaitMessage
      properties:
        keyword:
          anyOf:
          - type: string
          - type: 'null'
          title: Keyword
        messageNr:
          anyOf:
          - type: integer
          - type: 'null'
          title: Messagenr
        startTime:
          anyOf:
          - type: integer
          - type: 'null'
          title: Starttime
        subsystemNr:
          anyOf:
          - type: integer
          - type: 'null'
          title: Subsystemnr
      title: ZaitMessage
      type: object
info:
  title: performance_analytics-kpi-service
  version: '1'
openapi: 3.1.0
paths:
  /v1/performance-analytics/machine-report/v2/{line_id}/{machine_id}:
    get:
      description: Get a report for a machine or a line within a specified timerange
        for a specific kpi model.
      operationId: machine_report_v1_performance_analytics_machine_report_v2__line_id___machine_id__get
      parameters:
      - in: path
        name: line_id
        required: true
        schema:
          title: Line Id
          type: string
      - description: The id of the machine.
        in: path
        name: machine_id
        required: true
        schema:
          description: The id of the machine.
          title: Machine Id
          type: string
      - description: Represents the start time in Epoch.
        in: query
        name: time_from
        required: true
        schema:
          description: Represents the start time in Epoch.
          title: Time From
          type: integer
      - description: Represents the end time in Epoch.
        in: query
        name: time_to
        required: true
        schema:
          description: Represents the end time in Epoch.
          title: Time To
          type: integer
      - description: Parameter which determines whether we should calculate line kpis.
        in: query
        name: line_kpi
        required: false
        schema:
          default: 0
          description: Parameter which determines whether we should calculate line
            kpis.
          title: Line Kpi
          type: integer
      - description: Language to be used for resolving touch messages.
        in: query
        name: language
        required: false
        schema:
          default: en
          description: Language to be used for resolving touch messages.
          title: Language
          type: string
      - description: Id of the kpi model.
        in: query
        name: kpi_model_id
        required: true
        schema:
          description: Id of the kpi model.
          title: Kpi Model Id
          type: string
      - description: Parameter which determines whether we should query the children
          as well.
        in: query
        name: with_children
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Parameter which determines whether we should query the children
            as well.
          title: With Children
      - description: Set to true if manual changes should be applied already or false
          if original values should not be overwritten
        in: query
        name: apply_manual_changes
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Set to true if manual changes should be applied already or
            false if original values should not be overwritten
          title: Apply Manual Changes
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineReportResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Machine Report
  /v1/performance-analytics/trend-analysis/v2:
    post:
      description: Returns a trend analysis of Kpis for specified machines and timerange.
      operationId: trend_analysis_v1_performance_analytics_trend_analysis_v2_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrendAnalysisBody'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendAnalysisResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Trend Analysis
