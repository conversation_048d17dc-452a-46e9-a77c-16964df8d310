from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import timed<PERSON><PERSON>
from aws_lambda_powertools import Logger
from dateutil import parser, tz
from fastapi import APIRouter, HTTPException, Request, status
from lib_dtm_client.clients.query_handler import Query<PERSON>andler
from performance_analytics.models.trend_analysis_models import (
    TrendAnalysisBody,
    TrendAnalysisResponse,
)


from common.format_result import format_day_results_to_trend_analysis_response
from common.trend_kpi import get_trend_kpis
from dependencies.kpi_model import get_validated_kpi_model
from dependencies.line_settings import LineSettingsFromBody

LOGGER = Logger()

router = APIRouter()


@router.post("/v2", response_model=TrendAnalysisResponse)
def trend_analysis(
    body: TrendAnalysisBody, request: Request, line_settings: LineSettingsFromBody
):  # pylint: disable=too-many-locals
    """
    Returns a trend analysis of Kpis for specified machines and timerange.
    """
    try:
        LOGGER.info(
            "Calling trend-analysis endpoint with event.",
            extra={"event": request.scope.get("aws.event")},
        )

        local_tz = tz.gettz(body.timezone)
        start_time = parser.parse(body.time.start_time).replace(tzinfo=local_tz)
        end_time = parser.parse(body.time.end_time).replace(tzinfo=local_tz)
        machines = body.machines
        kpi_model = get_validated_kpi_model(
            request,
            body.line,
            body.kpi_model_id,
            int(start_time.timestamp() * 1000),
            int(end_time.timestamp() * 1000),
        )
        if not kpi_model:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="kpi model not assigned to this line in the given timerange",
            )
        LOGGER.debug(
            "Called for",
            extra={
                "kpi_model": kpi_model,
                "account": line_settings.account,
                "line_id": line_settings.line_id,
                "timezone": local_tz,
            },
        )

        query_handler = QueryHandler()

        days_with_downtimes = []

        for machine in machines:
            start_of_day = int(start_time.timestamp() * 1000)

            days = []
            while start_of_day <= int(end_time.timestamp() * 1000):  # Results include last day.
                end_of_day = int(start_of_day + timedelta(days=1).total_seconds() * 1000)
                days.append((start_of_day, end_of_day))
                start_of_day = end_of_day

            with ThreadPoolExecutor(max_workers=7) as executor:
                future_to_downtimes_with_speeds = {
                    executor.submit(
                        query_handler.get_downtimes_with_speeds_for_machine,
                        line_settings.account,
                        machine,
                        start,
                        end,
                        kpi_model,
                        with_children=False,
                        line_kpi=body.line_kpi,
                        line_id=body.line,
                    ): (start, end)
                    for start, end in days
                }
                for future in as_completed(future_to_downtimes_with_speeds):
                    day = future_to_downtimes_with_speeds[future]

                    if future.exception() is not None:
                        LOGGER.error(
                            "error while querying timerange (%s, %s)",
                            day[0],  # start of day
                            day[1],  # end of day
                        )
                        raise SystemError(
                            "error while querying downtimes with speeds"
                        ) from future.exception()

                    data = future.result()

                    days_with_downtimes.append(data)

        day_results = get_trend_kpis(days_with_downtimes, kpi_model, line_settings, body.line_kpi)

        return format_day_results_to_trend_analysis_response(day_results, kpi_model.kpi_model_id)

    except HTTPException as http_excep:
        LOGGER.warning(
            "trend_analysis failed",
            extra={
                "raised_exception": http_excep,
                "event": request.scope.get("aws.event"),
            },
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "trend_analysis failed",
            extra={
                "exception_type": type(excep),
                "raised_exception": excep,
                "event": request.scope.get("aws.event"),
            },
            exc_info=True,
        )
        raise excep
