from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, HTTPException, Path, Query, Request, status, Depends

from lib_cloud_sdk.util.common import validate_fast_api_timerange
from lib_cloud_sdk.processing.steps import SetMinorStopStep
from lib_kpi_config_client.models.enums import KpiModelScope
from lib_dtm_client.clients.query_handler import QueryHandler
from performance_analytics.fast_api.dependencies.s2a_properties import (
    Share2ActProperties,
)
from performance_analytics.fast_api.other.document_processor import DocumentProcessor
from performance_analytics.models.shared_models import (
    DEFAULT_NOMINAL_SPEED,
    MachineReportResponse,
)


from common.constants import DOWNTIMES, KPI_MODEL_INSIGHT
from common.join_time_series import JoinTimeSeries
from common.kpi_client import KpiClient
from common.utilities import create_report_downtimes_from_downtimes
from dependencies.kpi_model import get_validated_kpi_model
from dependencies.line_settings import LineSettingsFromParameters


LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


# pylint: disable=too-many-positional-arguments
@router.get(
    "/v2/{line_id}/{machine_id}",
    response_model=MachineReportResponse,
    response_model_exclude_none=True,
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def machine_report(  # pylint: disable=too-many-statements,too-many-branches
    request: Request,
    properties: Share2ActProperties,
    line_settings: LineSettingsFromParameters,
    line_id: str = Path(..., description="The id of the line."),
    machine_id: str = Path(..., description="The id of the machine."),
    time_from: int = Query(..., description="Represents the start time in Epoch."),
    time_to: int = Query(..., description="Represents the end time in Epoch."),
    line_kpi: int = Query(
        0,
        description="Parameter which determines whether we should calculate line kpis.",
    ),
    language: str = Query("en", description="Language to be used for resolving touch messages."),
    kpi_model_id: str = Query(
        description="Id of the kpi model.",
    ),
    with_children: bool | None = Query(
        True,
        description="Parameter which determines whether we should query the children as well.",
    ),
    apply_manual_changes: bool | None = Query(
        True,
        description="Set to true if manual changes should be applied already or false if "
        + "original values should not be overwritten",
    ),
):  # pylint: disable=too-many-arguments, too-many-locals
    """
    Get a report for a machine or a line within a specified timerange for a specific kpi model.
    """

    try:
        LOGGER.info(
            "Calling machine-report endpoint with event.",
            extra={"event": request.scope.get("aws.event")},
        )
        kpi_model = get_validated_kpi_model(
            request,
            line_id,
            kpi_model_id,
            time_from,
            time_to,
        )
        if not kpi_model:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="kpi model not assigned to this line in the given timerange",
            )

        LOGGER.debug(
            "Called for",
            extra={
                "kpi_model": kpi_model,
                "account": line_settings.account,
                "line_id": line_settings.line_id,
            },
        )

        query_handler = QueryHandler()

        downtimes_and_speeds = query_handler.get_downtimes_with_speeds_for_machine(
            account=properties.account,
            machine_id=machine_id,
            time_from=time_from,
            time_to=time_to,
            kpi_model=kpi_model,
            cut_start_end_to_timeframe=1,
            with_children=with_children,
            language=language,
            apply_manual_changes=apply_manual_changes,
            line_kpi=line_kpi,
            line_id=line_id,
        )

        for key in ["speeds", "design_speeds", "set_speeds"]:
            for speed in downtimes_and_speeds[key]:
                if speed.get("ongoing", False):
                    speed["ongoing"] = 1
                else:
                    speed["ongoing"] = None

        document_processor = DocumentProcessor()

        time_series = JoinTimeSeries()
        downtimes_and_speeds["joined_time_series"] = time_series.join(downtimes_and_speeds)

        if kpi_model_id != KPI_MODEL_INSIGHT:
            minor_stop_duration = line_settings.settings.minor_stop_config * 60 * 1000
            if minor_stop_duration > 0:
                minor_stop_step = SetMinorStopStep(minor_stop_duration)
                downtimes_and_speeds[DOWNTIMES] = list(
                    document_processor.preprocess_data(
                        [minor_stop_step], downtimes_and_speeds[DOWNTIMES]
                    )
                )

        machine_setting = (
            line_settings.settings.machine_settings.get(machine_id)
            if line_settings.settings.machine_settings
            else None
        )
        if machine_setting:
            nominal_speed = machine_setting.nominal_speed
        else:
            nominal_speed = DEFAULT_NOMINAL_SPEED
            LOGGER.warning(
                "No nominalspeed configured for",
                extra={
                    "account": line_settings.account,
                    "line_id": line_settings.line_id,
                    "machine_id": machine_id,
                    "default_nominal_speed": DEFAULT_NOMINAL_SPEED,
                },
            )

        kpi_client = KpiClient(downtimes_and_speeds, nominal_speed)
        kpi_result = kpi_client.calculate_kpis(
            kpi_model=kpi_model,
            kpi_model_scope=KpiModelScope.LINE if line_kpi else KpiModelScope.MACHINE,
            account=properties.account,
        )
        downtimes_and_speeds["kpi_result"] = kpi_result["kpi_result"]

        # backward compatibility
        downtimes_and_speeds["account"] = downtimes_and_speeds.pop("account_id")
        downtimes_and_speeds["kpi_model_id"] = downtimes_and_speeds.pop("kpi_model")
        downtimes_and_speeds["reduced_machine_state"] = kpi_result["reduced_downtimes"]
        downtimes_and_speeds["downtimes"] = create_report_downtimes_from_downtimes(
            downtimes_and_speeds[DOWNTIMES]
        )

    except HTTPException as http_excep:
        LOGGER.warning(
            "machine_report failed",
            extra={
                "raised_exception": http_excep,
                "event": request.scope.get("aws.event"),
            },
            exc_info=True,
        )
        raise http_excep
    except Exception as excep:
        LOGGER.warning(
            "machine_report failed",
            extra={
                "exception_type": type(excep),
                "raised_exception": excep,
                "event": request.scope.get("aws.event"),
            },
            exc_info=True,
        )
        raise excep

    return MachineReportResponse.model_validate(downtimes_and_speeds)
