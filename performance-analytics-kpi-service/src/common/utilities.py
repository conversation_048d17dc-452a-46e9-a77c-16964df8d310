from typing import Any

from lib_dtm_common.models.api_models import ResponseChildDowntime
from performance_analytics.models.machine_report_models import (
    BaseDowntime,
    RawValues,
    ReportDowntime,
)

from common.constants import (
    CATEGORY,
    CLASSIFICATION,
    DURATION,
    END,
    MA<PERSON>AL_CHANGES,
    RAW_VALUES,
    START,
    UNCUT_END,
    UNCUT_START,
)


def create_report_downtimes_from_downtimes(
    downtimes: list[dict[str, Any]],
) -> list[ReportDowntime]:
    """
    Creates `ReportDowntime`s from `ResponseDowntime`s that get passed in as `dict`.

    Args:
        downtimes (list[dict[str, Any]]): `ResponseDowntime`s to be converted.

    Returns:
        list[ReportDowntime]: List of `ReportDowntime`s.
    """
    return [
        ReportDowntime(
            category=downtime.get(CATEGORY),
            classification=downtime.get(CLASSIFICATION),
            start=downtime.get(START),
            start_uncut=downtime.get(UNCUT_START),
            end=downtime.get(END),
            end_uncut=downtime.get(UNCUT_END),
            duration=downtime.get(DURATION),
            raw_values=RawValues(**rvs) if (rvs := downtime.get(RAW_VALUES)) else None,
            message=downtime.get("message"),
            downtimes=create_base_losses_from_downtime_children(downtime.get("children")),
            reason_code_id=downtime.get("reason_code_id"),
            manual_changes=downtime.get(MANUAL_CHANGES),
        )
        for downtime in downtimes
    ]


def create_base_losses_from_downtime_children(
    children: [ResponseChildDowntime],
) -> list[BaseDowntime] | None:
    # do not add single child
    if not children or len(children) <= 1:
        return None
    base_downtime = []
    for child in children:
        base_downtime.append(
            BaseDowntime(
                category=child.get(CATEGORY),
                classification=child.get(CLASSIFICATION),
                start=child.get(START),
                start_uncut=child.get(UNCUT_START),
                end=child.get(END),
                end_uncut=child.get(UNCUT_END),
                duration=child.get(DURATION),
                raw_values=RawValues(**child.get(RAW_VALUES)) if child.get(RAW_VALUES) else None,
                manual_changes=child.get(MANUAL_CHANGES),
            )
        )
    return base_downtime
