# System import
from typing import Any

# Library import
from performance_analytics.models.shared_models import Document

DEFAULT_CONFIG = {
    "downtimes": {
        "classification": "classification",
        "category": "category",
        "raw_values.aggregated_state": "aggregated_state",
        "raw_values.state": "state",
        "raw_values.mode": "mode",
        "raw_values.program": "program",
        "message": "message",
    },
    "speeds": {"current_speed": "current_speed"},
    "design_speeds": {"speed": "design_speed"},
    "set_speeds": {"speed": "set_speed"},
}


class JoinTimeSeries:
    """
    Joins multiple time series into single one. If data is missing values are set to None.
    config (optional): Arrays that will be joined.
    Defined keys will be renamed and added to the result.
    Avoid key conflicts in resulting time series! Defaults to DEFAULT_CONFIG.
    """

    def __init__(self, config: dict[str, Any] = None):
        self._config = config or DEFAULT_CONFIG

    def join(self, machine_data_and_speeds: Document) -> list[dict[str, Any]]:
        """
        Args:
            machine_data_and_speeds: Result from query lib. Elements of each time series must
            contain 'start' and 'end' key. Data may not have gaps.

        Returns:
            [array]: Time series that contains all configured values from config.
            Start and end are adjusted. Missing data is set to None.
        """

        result = []
        # transform data
        concatenated_list, last_items = self._transform_input_data(machine_data_and_speeds)
        current_values = self._initialize_current_values()

        start = 0
        end = 0

        for i, item in enumerate(concatenated_list):
            start = item.get("start")
            current_values = self._set_done_lists_to_none(start, current_values, last_items)
            current_key = item.get("__source__")

            for source_name, target_name in self._config[current_key].items():
                source_value = self._get_from_item(item, source_name)
                current_values[current_key][target_name] = (
                    source_value
                    if source_value is not None
                    else current_values[current_key][target_name]
                )  # pylint: disable=line-too-long

            # handle last element
            if i + 1 == len(concatenated_list):
                end = item.get("end")
                result = self.append_current_item(result, start, end, current_values)
            elif concatenated_list[i + 1].get("start") > start:
                end = concatenated_list[i + 1].get("start")
                result = self.append_current_item(result, start, end, current_values)

        result = self._append_last_items(result, last_items, end, current_values)

        return result

    def _transform_input_data(self, machine_data_and_speeds: Document) -> list[dict[str, Any]]:
        concatenated_list_unsorted = []
        last_items_unsorted = []
        for key in self._config.keys():
            if key not in machine_data_and_speeds.keys():
                machine_data_and_speeds[key] = []
            for item in machine_data_and_speeds[key]:
                concatenated_list_unsorted.append({"__source__": key, **item})
            if len(machine_data_and_speeds[key]) > 0:
                last_item = machine_data_and_speeds[key][-1]
                last_items_unsorted.append({"__source__": key, **last_item})
        concatenated_list = sorted(concatenated_list_unsorted, key=lambda k: k["start"])
        last_items = sorted(last_items_unsorted, key=lambda k: k["end"])

        return concatenated_list, last_items

    def _initialize_current_values(self) -> dict[str, Any]:
        current_values = {}
        for key, value in self._config.items():
            current_values[key] = {}
            for target_name in value.values():
                current_values[key][target_name] = None
        return current_values

    def _set_done_lists_to_none(
        self, start: int, current_values: dict[str, Any], last_items: list[dict[str, Any]]
    ) -> dict[str, Any]:
        for item in last_items:
            key = item["__source__"]
            if item["end"] <= start:
                for target_name in self._config[key].values():
                    current_values[key][target_name] = None
        return current_values

    def _append_last_items(
        self,
        result: list[dict[str, Any]],
        last_items: list[dict[str, Any]],
        end: int,
        current_values: dict[str, Any],
    ) -> list[dict[str, Any]]:
        for item in last_items:
            if item["end"] <= end:
                continue
            start = end
            end = item["end"]
            current_values = self._set_done_lists_to_none(start, current_values, last_items)
            result = self.append_current_item(result, start, end, current_values)
        return result

    @staticmethod
    def append_current_item(
        array: list[dict[str, Any]], start: int, end: int, current_values: dict[str, Any]
    ) -> list[dict[str, Any]]:
        item = {"start": start, "end": end}

        for key in current_values.keys():
            item = {**item, **current_values[key]}
        array.append(item)
        return array

    @staticmethod
    def _get_from_item(item, source_name) -> str | None:
        """
        Return the item value or None
        :param item: item data
        :param source_name: item key
        :return: item value or None if item is None
        """
        value = item
        path = source_name.split(".")
        for attribute in path:
            value = value.get(attribute) if value else None
        return value
