from fastapi import HTTPException, status
from performance_analytics.clients.line_settings.db_client import LineSettingsClient
from performance_analytics.clients.line_settings.exceptions.error import LineSettingsDbClientError
from performance_analytics.clients.line_settings.exceptions.not_found import (
    LineSettingsNotFoundError,
)
from performance_analytics.models.shared_models import LineSettings


def get_line_settings(account: str, line_id: str) -> LineSettings:
    try:
        line_settings_client = LineSettingsClient()
        return line_settings_client.get_line_settings(account, line_id)
    except LineSettingsNotFoundError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(exc)) from exc
    except LineSettingsDbClientError as exc:
        raise HTTPException(status_code=500, detail=str(exc)) from exc
