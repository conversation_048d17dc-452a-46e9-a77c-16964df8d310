# System import
from copy import deepcopy

# Library import
from aws_lambda_powertools import Logger, Tracer
from kpi_calculation.calculate_kpi import calculate_kpi, calculate_kpi_for_multiple_documents
from lib_kpi_config_client.database.config_type_db_client import KpiModelConfigDatabaseClient
from lib_kpi_config_client.models.api_models import KpiResult
from lib_kpi_config_client.models.enums import KpiModelScope, KpiModelType
from lib_kpi_config_client.models.config_types import KpiModel

from machine_data_split.split_document import split_document
from performance_analytics.models.shared_models import Document
from performance_analytics.utility.utils import get_design_speeds_and_split_times

LOGGER = Logger()
TRACER = Tracer()


class KpiClient:
    def __init__(
        self,
        machine_data_and_speeds: Document,
        default_nominal_speed: int,
    ):
        self._machine_data_and_speeds = machine_data_and_speeds
        self._default_nominal_speed = default_nominal_speed
        self._kpi_model_config_database_client = KpiModelConfigDatabaseClient()

    @TRACER.capture_method(capture_response=False)
    def calculate_kpis(
        self, kpi_model: KpiModel, kpi_model_scope: KpiModelScope, account: str
    ) -> KpiResult:
        """
        Calculate KPIs for a kpi model.
        If the document has multiple design speeds then calculate KPIs based on design speeds
        and return a merged kpi result.
        """

        LOGGER.info(
            "Calculate KPIs for kpi_model.",
            extra={
                "kpi_model": kpi_model.kpi_model_id,
                "kpi_model_scope": kpi_model_scope,
            },
        )

        kpi_model_config_container = (
            self._kpi_model_config_database_client.get_kpi_model_kpi_definition(
                kpi_model_id=kpi_model.kpi_model_id,
                account=account if kpi_model.kpi_model_type == KpiModelType.CUSTOM else None,
            )
        )

        if kpi_model_config_container:
            kpi_model_config = kpi_model_config_container.config

            documents, design_speeds = self._split_document_based_on_design_speed(
                self._default_nominal_speed
            )

            if len(documents) >= 2 and design_speeds:
                # if the actual query time_from/time_to is after the time_from/time_to we get
                # from splitting design_speeds we need to set the time_from/time_to to the
                # actual query time to not mess up calculation
                documents[0]["time_from"] = max(
                    documents[0]["time_from"], self._machine_data_and_speeds["time_from"]
                )
                documents[-1]["time_to"] = min(
                    documents[-1]["time_to"], self._machine_data_and_speeds["time_to"]
                )

                calculation_result = calculate_kpi_for_multiple_documents(
                    documents=documents,
                    design_speeds=design_speeds,
                    kpi_model_config=kpi_model_config,
                    scope=kpi_model_scope,
                )
                # we need the eq_id and the time from to be returned
                # from kpi lib for the Trend Analysis
                calculation_result["eq_id"] = (
                    documents[0].get("equipment_id") or documents[0]["eq_id"]
                )
                calculation_result["time_from"] = documents[0]["time_from"]

            else:
                calculation_result = calculate_kpi(
                    machine_data_and_speeds=deepcopy(self._machine_data_and_speeds),
                    kpi_model_config=kpi_model_config,
                    scope=kpi_model_scope,
                )

            return calculation_result

        raise ValueError("no kpi model definition found for kpi model")

    @TRACER.capture_method(capture_response=False)
    def _split_document_based_on_design_speed(
        self, default_nominal_speed: int
    ) -> tuple[list[Document], list[int]]:
        split_times, design_speeds = get_design_speeds_and_split_times(
            self._machine_data_and_speeds, default_nominal_speed
        )
        documents = []
        if len(split_times) > 2:
            documents = split_document(self._machine_data_and_speeds, split_times)
        else:
            # Set design as nominal speed because it is needed for KPI calculation
            if (
                "nominal_speed" not in self._machine_data_and_speeds.keys()
                or self._machine_data_and_speeds["nominal_speed"] is None
            ):
                if design_speeds and len(design_speeds) == 1 and design_speeds[0] is not None:
                    self._machine_data_and_speeds["nominal_speed"] = design_speeds[0]
                else:
                    self._machine_data_and_speeds["nominal_speed"] = default_nominal_speed

        return documents, design_speeds
