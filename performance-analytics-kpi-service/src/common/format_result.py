from lib_kpi_config_client.models.api_models import Kpi<PERSON><PERSON>ult
from performance_analytics.models.shared_models import Document
from performance_analytics.models.trend_analysis_models import (
    Grouped,
    TrendAnalysisResponse,
)


def format_day_results_to_trend_analysis_response(
    day_results: list[Document], kpi_model: str
) -> TrendAnalysisResponse:
    response = TrendAnalysisResponse(kpi_model_id=kpi_model, machine_ids={})
    for day_result in day_results:
        kpi_result_obj = day_result["kpi_result"]
        response = _add_day_to_trend_analysis_response(response, day_result, kpi_result_obj)
    return response


def _add_day_to_trend_analysis_response(
    response: TrendAnalysisResponse,
    result: Document,
    kpi_result_obj: KpiResult,
) -> TrendAnalysisResponse:
    machine_id = result["equipment_id"]
    day_start = result["time_from"]
    response = _add_grouped_to_trend_analysis_response(response, machine_id)
    response.machine_ids[machine_id].days[day_start] = kpi_result_obj
    return response


def _add_grouped_to_trend_analysis_response(
    response: TrendAnalysisResponse, machine_id: str
) -> TrendAnalysisResponse:
    if (
        machine_id not in response.machine_ids.keys()
    ):  # pylint: disable=consider-iterating-dictionary
        grouped = Grouped(days={})
        response.machine_ids[machine_id] = grouped
    return response
