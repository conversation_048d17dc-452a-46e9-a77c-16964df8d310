from copy import deepcopy

from aws_lambda_powertools import Logger, Tracer
from lib_cloud_sdk.processing.steps import SetMinorStopStep
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelScope
from machine_data_query.utils.speeds import create_counter_speeds_from_document
from performance_analytics.fast_api.other.document_processor import DocumentProcessor
from performance_analytics.models.shared_models import DEFAULT_NOMINAL_SPEED
from performance_analytics.utility.utils import accumulate_units

from common.constants import DOWNTIMES
from common.kpi_client import KpiClient

LOGGER = Logger()
TRACER = Tracer()


# pylint: disable-msg=too-many-locals
def get_trend_kpis(
    days: list[dict[str, any]], kpi_model: KpiModel, line_settings, line_kpi: int
) -> list[dict[str, any]]:
    results = []
    for day in days:
        machine_id = day["equipment_id"]

        if kpi_model.kpi_model_id != "insight":
            minor_stop_duration = line_settings.settings.minor_stop_config * 60 * 1000
            if minor_stop_duration > 0:
                minor_stop_step = SetMinorStopStep(minor_stop_duration)
                document_processor = DocumentProcessor()
                day[DOWNTIMES] = list(
                    document_processor.preprocess_data([minor_stop_step], day[DOWNTIMES])
                )

        speeds = create_counter_speeds_from_document(day)

        accumulated_speeds = accumulate_units(speeds)
        final_day = deepcopy(day)
        final_day["units_produced"] = accumulated_speeds.units_produced
        final_day["units_defect"] = accumulated_speeds.units_defect
        final_day["units_total"] = accumulated_speeds.units_total

        try:
            machine_setting = line_settings.settings.machine_settings[machine_id]
            nominal_speed = machine_setting.nominal_speed
        except (KeyError, TypeError, AttributeError):
            LOGGER.warning(
                "No nominalspeed configured for",
                extra={
                    "account": line_settings.account,
                    "line_id": line_settings.line_id,
                    "machine_id": machine_id,
                    "default_nominal_speed": DEFAULT_NOMINAL_SPEED,
                },
            )
            nominal_speed = DEFAULT_NOMINAL_SPEED

        kpi_client = KpiClient(final_day, nominal_speed)
        kpi_result = kpi_client.calculate_kpis(
            kpi_model=kpi_model,
            kpi_model_scope=KpiModelScope.LINE if line_kpi else KpiModelScope.MACHINE,
            account=line_settings.account,
        )
        final_day["kpi_result"], final_day["reduced_downtimes"] = (
            kpi_result["kpi_result"],
            kpi_result["reduced_downtimes"],
        )

        results.append(final_day)
    return results
