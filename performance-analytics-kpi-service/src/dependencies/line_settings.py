from typing import Annotated

from aws_lambda_powertools import Logger
from fastapi import Depends, Request

from performance_analytics.fast_api.dependencies.s2a_properties import get_share2act_properties
from performance_analytics.models.shared_models import LineSettings
from performance_analytics.models.trend_analysis_models import Body

from common.line_settings import get_line_settings

LOGGER = Logger()


def get_line_settings_from_body(
    request: Request,
    body: Body,
) -> LineSettings:
    properties = get_share2act_properties(request)
    return get_line_settings(properties.account, body.line)


def get_line_settings_from_parameters(request: Request, line_id: str) -> LineSettings:
    properties = get_share2act_properties(request)
    return get_line_settings(properties.account, line_id)


LineSettingsFromBody = Annotated[LineSettings, Depends(get_line_settings_from_body)]

LineSettingsFromParameters = Annotated[LineSettings, Depends(get_line_settings_from_parameters)]
