from fastapi import Request

from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.api.api_client import KpiConfigApiClient
from lib_kpi_config_client.models.api_models import ApiKpiModelTimeValidity

from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties


def get_validated_kpi_model(
    request: Request, line_id: str, kpi_model_id: str, start_time: int, end_time: int | None
) -> KpiModel | None:
    """Get the valid KPI model for the specified time range."""
    #properties = CommonShare2ActProperties(request.scope.get("aws.event", {}).get("requestContext"))
    kpi_config_api_client = KpiConfigApiClient()
    assigned_kpi_models_in_timerange: list[ApiKpiModelTimeValidity] = (
        kpi_config_api_client.get_kpi_models_assigned_in_timerange(
            account="ssl-gbr-knaresborough", line_id=line_id, start=start_time, end=end_time
        )
    )
    for model in assigned_kpi_models_in_timerange:
        if model.kpi_model_id == kpi_model_id:
            return KpiModel(kpi_model_id=model.kpi_model_id, kpi_model_type=model.kpi_model_type)
    return None
