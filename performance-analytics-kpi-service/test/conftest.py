# System import
import json
import os
import site

import boto3

# Library import
import pytest
from fastapi import Request
from lib_dtm_common.models.api_models import GetDowntimeResponse
from unittest.mock import patch
from moto import mock_aws

# we set our site dir to src to have proper package names
MODULE_DIR_PATH = os.path.dirname(os.path.realpath(__file__))
source_dir = os.path.join(MODULE_DIR_PATH, "..", "src")
site.addsitedir(source_dir)


@pytest.fixture
def aws_credentials():
    """
    Mocked AWS Credentials for moto.
    """
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "eu-central-1"
    os.environ["SQS_QUEUENAME"] = "testing"
    os.environ["SQS_URL"] = "https://eu-central-1.queue.amazonaws.com/testing"
    os.environ["AWS_STAGE"] = "testing"
    os.environ["AWS_XRAY_CONTEXT_MISSING"] = "LOG_ERROR"
    os.environ["AWS_XRAY_DEBUG_MODE"] = "TRUE"
    os.environ["POWERTOOLS_TRACE_DISABLED"] = "TRUE"
    os.environ["RK_AWS_STAGE"] = "testing"


_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["scope-1", "scope-2"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps(
            {
                "userId": "some-user-id",
                "username": "some-user-name",
                "login": "some-login",
                "groups": ["group-1", "site-manager"],
            }
        ),
        "account": json.dumps({"accountId": "readykit-replay", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture
def aws_credentials_inadequate_mock():
    from performance_analytics.utility.s2a_request_wrapper import (
        CommonShare2ActProperties,
    )

    with patch(
        "performance_analytics.utility.s2a_request_wrapper.CommonShare2ActProperties"
    ) as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        properties.user_groups = ["group-1"]
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def aws_request_mock(aws_credentials):
    event = {
        "aws.event": {"requestContext": _request_context},
        "type": "http",
        "headers": "some headears",
    }
    request = Request(scope=event)
    yield request


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import (
        CommonShare2ActProperties,
    )

    with patch(
        "performance_analytics.utility.s2a_request_wrapper.CommonShare2ActProperties"
    ) as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def dynamo_db_tables_mock(aws_credentials):
    with mock_aws():
        db_client = boto3.resource("dynamodb", region_name="eu-central-1")
        performance_meda_cache = db_client.create_table(
            TableName="performance-meda-cache",
            KeySchema=[
                {"AttributeName": "PK", "KeyType": "HASH"},
                {
                    "AttributeName": "SK",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "PK", "AttributeType": "S"},
                {"AttributeName": "SK", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        performance_equipment_cache = db_client.create_table(
            TableName="performance-equipment-cache",
            KeySchema=[
                {"AttributeName": "account", "KeyType": "HASH"},
                {
                    "AttributeName": "equipmentId",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "account", "AttributeType": "S"},
                {"AttributeName": "equipmentId", "AttributeType": "S"},
                {"AttributeName": "lineId", "AttributeType": "S"},
            ],
            GlobalSecondaryIndexes=[
                {
                    "IndexName": "lineIdIndex",
                    "KeySchema": [
                        {"AttributeName": "lineId", "KeyType": "HASH"},
                        {"AttributeName": "equipmentId", "KeyType": "RANGE"},
                    ],
                    "Projection": {
                        "ProjectionType": "ALL"  # You can use "KEYS_ONLY" or "INCLUDE" with specific attributes
                    },
                    "ProvisionedThroughput": {"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
                }
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        rk_performance_customer_settings = db_client.create_table(
            TableName="rk-performance-customer-settings",
            KeySchema=[
                {"AttributeName": "account", "KeyType": "HASH"},
                {
                    "AttributeName": "line_id",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "account", "AttributeType": "S"},
                {"AttributeName": "line_id", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        performance_kpi_model = db_client.create_table(
            TableName="performance-kpi-model",
            KeySchema=[
                {"AttributeName": "pk", "KeyType": "HASH"},
                {
                    "AttributeName": "sk",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "pk", "AttributeType": "S"},
                {"AttributeName": "sk", "AttributeType": "S"},
                {"AttributeName": "config_type", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
            GlobalSecondaryIndexes=[
                {
                    "IndexName": "config_type",
                    "KeySchema": [
                        {"AttributeName": "config_type", "KeyType": "HASH"},
                        {"AttributeName": "pk", "KeyType": "RANGE"},
                    ],
                    "Projection": {
                        "ProjectionType": "ALL",
                    },
                }
            ],
        )
        performance_data = db_client.create_table(
            TableName="performance-data",
            KeySchema=[
                {"AttributeName": "rk_eq_id", "KeyType": "HASH"},
                {
                    "AttributeName": "time_from",
                    "KeyType": "RANGE",
                },
            ],
            AttributeDefinitions=[
                {"AttributeName": "rk_eq_id", "AttributeType": "S"},
                {"AttributeName": "time_from", "AttributeType": "S"},
            ],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        yield {
            "performance_meda_cache": performance_meda_cache,
            "performance_equipment_cache": performance_equipment_cache,
            "rk_performance_customer_settings": rk_performance_customer_settings,
            "performance_kpi_model": performance_kpi_model,
            "performance_data": performance_data,
        }


@pytest.fixture
def sqs_client(aws_credentials):
    with mock_aws():
        sqs_client = boto3.client("sqs", region_name="eu-central-1")
        sqs_client.create_queue(QueueName=os.environ["SQS_QUEUENAME"])
        yield sqs_client


@pytest.fixture
def s3_client(aws_credentials):
    with mock_aws():
        s3_client = boto3.client("s3", region_name="us-east-1")
        s3_client.create_bucket(Bucket="rk-zait-testing")
        yield s3_client


@pytest.fixture
def sample_arguments():
    return {
        "account": "test-account",
        "machine_id": "test-machine-id",
        "time_from": 1,
        "time_to": 5,
    }


@pytest.fixture
def downtime_client_mock():
    with patch("lib_dtm_client.clients.query_handler._get_downtimes") as dtm_client_mock:
        dtm_client_mock.return_value = GetDowntimeResponse.model_validate(
            {
                "account_id": "readykit-replay",
                "equipment_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a",
                "kpi_model": "insight",
                "downtimes": [],
            }
        )
        yield dtm_client_mock


@pytest.fixture(autouse=True)
def compression_minimum_size_mock(mocker):
    mocker.patch(
        "common.constants.COMPRESSION_MINIMUM_SIZE", 10
    )  # 10 bytes -> to trigger compression middleware
