# Application import
from lib_cloud_sdk.util.file_io import read_json_file


def test_join_time_series():
    """
    Test timeseries data is joined correctly.
    """
    # Arrange
    from common.join_time_series import JoinTimeSeries

    input_data = read_json_file("test/common_test/res/join_time_series_input.json")
    expected_result = read_json_file("test/common_test/res/join_time_series_expected_result.json")

    # Act
    time_series = JoinTimeSeries()
    joined_time_series = time_series.join(input_data)

    # Assert
    assert joined_time_series == expected_result


def test_join_time_series_missing_key():
    """
    Test timeseries data is joined correctly even if some keys within an item are missing.
    """
    # Arrange
    from common.join_time_series import JoinTimeSeries

    input_data = {"losses_detailed": [{"program": 1, "start": 0, "end": 17}]}

    config = {"losses_detailed": {"program": "program", "message": "message"}}

    expected_result = [{"program": 1, "start": 0, "end": 17, "message": None}]

    # Act
    time_series = JoinTimeSeries(config=config)
    joined_time_series = time_series.join(input_data)

    # Assert
    assert joined_time_series == expected_result


def test_join_time_series_missing_input_array():
    """
    Test timeseries data is joined correctly even if some input data is missing completely.
    """
    # Arrange
    from common.join_time_series import JoinTimeSeries

    input_data = {"losses_detailed": [{"program": 1, "start": 0, "end": 17}]}

    config = {
        "losses_detailed": {"program": "program"},
        "speeds": {"current_speed": "current_speed"},
    }

    expected_result = [{"program": 1, "start": 0, "end": 17, "current_speed": None}]

    # Act
    time_series = JoinTimeSeries(config=config)
    joined_time_series = time_series.join(input_data)

    # Assert
    assert joined_time_series == expected_result
