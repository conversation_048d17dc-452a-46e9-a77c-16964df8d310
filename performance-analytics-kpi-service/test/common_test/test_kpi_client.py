import pytest
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelScope, KpiModelType

from common.kpi_client import KpiClient


def test_calculate_kpi_for_multiple_documents_is_called(mocker, some_kpi_model, aws_credentials):
    """
    Test that if document contains multiple design speeds, we split the document based on the design speed.
    Both splitted documents are fed to the KPI calculation library which returns one merged KPI result.
    """
    # Arrange
    mocker.patch(
        "common.kpi_client.KpiClient._split_document_based_on_design_speed",
        return_value=(
            [
                {"equipment_id": "some-eq-id", "time_from": 1, "time_to": 5},
                {"equipment_id": "some-eq-id", "time_from": 5, "time_to": 10},
            ],
            [45000, 55000],
        ),
    )
    mocker.patch(
        "common.kpi_client.calculate_kpi_for_multiple_documents",
        return_value={"eq_id": "some-eq-id", "time_from": 1, "time_to": 10},
    )
    document = {
        "eq_id": "some-eq-id",
        "time_from": 1,
        "time_to": 10,
        "design_speeds": [45000, 55000],
    }
    default_nominal_speed = 25000
    expected_result = {"eq_id": "some-eq-id", "time_from": 1, "time_to": 10}

    # Act
    kpi_client = KpiClient(document, default_nominal_speed)
    kpi_result = kpi_client.calculate_kpis(
        KpiModel(kpi_model_id="some_kpi_model", kpi_model_type=KpiModelType.GLOBAL),
        KpiModelScope.MACHINE,
        "account",
    )

    # Assert
    assert kpi_result == expected_result


def test_calculate_kpi_is_called(mocker, some_kpi_model, aws_credentials):
    """
    Test that if document contains one design speed, we do not split the document.
    The document is fed to the KPI calculation library which returns a KPI result.
    """

    # Arrange
    mocker.patch(
        "common.kpi_client.KpiClient._split_document_based_on_design_speed",
        return_value=([{"eq_id": "some-eq-id", "time_from": 1, "time_to": 10}], [45000]),
    )
    mocker.patch(
        "common.kpi_client.calculate_kpi",
        return_value={"eq_id": "some-eq-id", "time_from": 1, "time_to": 10},
    )

    document = {"eq_id": "some-eq-id", "time_from": 1, "time_to": 10, "design_speeds": [45000]}
    default_nominal_speed = 25000
    expected_result = {"eq_id": "some-eq-id", "time_from": 1, "time_to": 10}

    # Act
    kpi_client = KpiClient(document, default_nominal_speed)
    kpi_result = kpi_client.calculate_kpis(
        KpiModel(kpi_model_id="some_kpi_model", kpi_model_type=KpiModelType.GLOBAL),
        KpiModelScope.MACHINE,
        "account",
    )

    # Assert
    assert kpi_result == expected_result


def test_calculate_kpi_not_valid_equipment_id_key(mocker, some_kpi_model, aws_credentials):
    from common.kpi_client import KpiClient

    mocker.patch(
        "common.kpi_client.KpiClient._split_document_based_on_design_speed",
        return_value=(
            [
                {"invalid_equipment_id": "some-eq-id", "time_from": 1, "time_to": 5},
                {"invalid_equipment_id": "some-eq-id", "time_from": 5, "time_to": 10},
            ],
            [45000, 55000],
        ),
    )
    mocker.patch(
        "common.kpi_client.calculate_kpi_for_multiple_documents",
        return_value={"eq_id": "some-eq-id", "time_from": 1, "time_to": 10},
    )
    document = {
        "eq_id": "some-eq-id",
        "time_from": 1,
        "time_to": 10,
        "design_speeds": [45000, 55000],
    }
    default_nominal_speed = 25000

    # Act
    kpi_client = KpiClient(document, default_nominal_speed)
    with pytest.raises(KeyError):
        kpi_client.calculate_kpis(
            KpiModel(kpi_model_id="some_kpi_model", kpi_model_type=KpiModelType.GLOBAL),
            KpiModelScope.MACHINE,
            "account",
        )


@pytest.mark.parametrize(
    "document, expected_output",
    [
        (
            {
                "customer": "foo",
                "design_speeds": [
                    {"speed": 222, "start": 0, "end": 10},
                    {"speed": 666, "start": 10, "end": 1000},
                    {"speed": 10000, "start": 1000, "end": 5000},
                    {"speed": 22000, "start": 5000, "end": 10000},
                ],
                "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
                "rk_eq_id": "readykit-replay_65ea1a06-2698-45eb-baad-3a3e5fc25b73_speeds",
                "time_from": 10,
                "time_to": 10000,
            },
            (
                [
                    {
                        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
                        "customer": "foo",
                        "time_from": 0,
                        "time_to": 10,
                        "design_speeds": [{"speed": 222, "start": 0, "end": 10}],
                    },
                    {
                        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
                        "customer": "foo",
                        "time_from": 10,
                        "time_to": 1000,
                        "design_speeds": [{"speed": 666, "start": 10, "end": 1000}],
                    },
                    {
                        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
                        "customer": "foo",
                        "time_from": 1000,
                        "time_to": 5000,
                        "design_speeds": [{"speed": 10000, "start": 1000, "end": 5000}],
                    },
                    {
                        "equipment_id": "65ea1a06-2698-45eb-baad-3a3e5fc25b73",
                        "customer": "foo",
                        "time_from": 5000,
                        "time_to": 10000,
                        "design_speeds": [{"speed": 22000, "start": 5000, "end": 10000}],
                    },
                ],
                [222, 666, 10000, 22000],
            ),
        )
    ],
)
def test_split_document_based_on_design_speed(document, expected_output, aws_credentials):
    default_nominal_speed = 100
    kpi_client = KpiClient(document, default_nominal_speed)

    documents, design_speeds = kpi_client._split_document_based_on_design_speed(
        default_nominal_speed
    )
    assert documents == expected_output[0]
    assert design_speeds == expected_output[1]
