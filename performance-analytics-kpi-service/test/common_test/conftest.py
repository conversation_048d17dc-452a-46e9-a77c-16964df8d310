import pytest

from lib_kpi_config_client.models.config_container import KpiModelConfigContainer
from lib_kpi_config_client.models.config_types import KpiModelKpiDefinitionConfig
from lib_kpi_config_client.models.enums import KpiModelType, KpiModelConfigType


@pytest.fixture
def some_kpi_model(mocker):
    mocker.patch(
        "lib_kpi_config_client.database.config_type_db_client.KpiModelConfigDatabaseClient.get_kpi_model_kpi_definition",
        return_value=KpiModelConfigContainer(
            pk="pk",
            sk="sk",
            config=KpiModelKpiDefinitionConfig(
                kpi_model_id="some_kpi_model", kpi_model_type=KpiModelType.GLOBAL
            ),
            config_type=KpiModelConfigType.KPI_DEFINITION,
        ),
    )
