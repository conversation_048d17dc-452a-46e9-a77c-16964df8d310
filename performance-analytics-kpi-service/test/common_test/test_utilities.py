import pytest
import pytz
from iso8601 import parse_date
from lib_dtm_common.models.api_models import ResponseDowntime
from performance_analytics.models.machine_report_models import ReportDowntime

from common.utilities import create_report_downtimes_from_downtimes


def test_create_report_downtimes_from_downtimes():
    mock_downtimes = [
        ResponseDowntime(
            start=1,
            end=2,
            duration=1,
            classification="class1",
            category="category1",
        ).model_dump(exclude_none=True),
        ResponseDowntime(
            start=2,
            end=4,
            duration=2,
            classification="class2",
            category="category1",
            reason_code_id="reason1",
        ).model_dump(exclude_none=True),
    ]

    expected = [
        ReportDowntime.model_validate(
            {
                "category": "category1",
                "classification": "class1",
                "duration": 1,
                "end": 2,
                "start": 1,
            }
        ),
        ReportDowntime.model_validate(
            {
                "category": "category1",
                "classification": "class2",
                "duration": 2,
                "end": 4,
                "reason_code_id": "reason1",
                "start": 2,
            }
        ),
    ]

    response = create_report_downtimes_from_downtimes(mock_downtimes)

    assert response == expected
