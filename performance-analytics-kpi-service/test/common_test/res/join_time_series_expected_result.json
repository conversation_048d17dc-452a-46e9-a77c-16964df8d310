[{"classification": "equipment_failure", "category": "equipment_failure", "aggregated_state": 1024, "mode": 4, "program": 1, "start": -10, "end": -2, "message": {}, "current_speed": null, "design_speed": null, "set_speed": null, "state": 1024}, {"classification": "prepared", "category": "prepared", "aggregated_state": 4, "mode": 8, "program": 1, "start": -2, "end": 0, "message": {}, "current_speed": null, "design_speed": null, "set_speed": null, "state": 4}, {"classification": "productive", "category": "productive", "aggregated_state": 128, "mode": 8, "program": 1, "start": 0, "end": 17, "message": {}, "current_speed": 50563, "design_speed": 54960, "set_speed": 48000, "state": 128}, {"classification": "minor_stop", "category": "minor_stop", "aggregated_state": 8, "message": {"id": 366, "message_nr": 366, "info": "Lack of containers in infeed section", "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF", "keyword": "", "keyword_id": "~Msg_Txt_Empty", "subsystem": "MMA", "subsystem_nr": 2000}, "mode": 8, "program": 1, "start": 17, "end": 23, "current_speed": 50563, "design_speed": 54960, "set_speed": 48000, "state": 8}, {"classification": "minor_stop", "category": "minor_stop", "aggregated_state": 8, "message": {"id": 366, "message_nr": 366, "info": "Lack of containers in infeed section", "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF", "keyword": "", "keyword_id": "~Msg_Txt_Empty", "subsystem": "MMA", "subsystem_nr": 2000}, "mode": 8, "program": 1, "start": 23, "end": 34, "current_speed": 0, "design_speed": 54960, "set_speed": 48000, "state": 8}, {"classification": "productive", "category": "productive", "aggregated_state": 128, "mode": 8, "program": 1, "message": {}, "start": 34, "end": 41, "current_speed": 0, "design_speed": 54960, "set_speed": 48000, "state": 128}, {"classification": "productive", "category": "productive", "aggregated_state": 128, "mode": 8, "program": 1, "message": {}, "start": 41, "end": 46, "current_speed": 0, "design_speed": 54960, "set_speed": 55000, "state": 128}, {"classification": "productive", "category": "productive", "aggregated_state": 128, "mode": 8, "program": 1, "message": {}, "start": 46, "end": 51, "current_speed": 10992, "design_speed": 54960, "set_speed": 55000, "state": 128}, {"classification": "equipment_failure", "category": "equipment_failure", "aggregated_state": 1024, "mode": 8, "program": 1, "message": {}, "start": 51, "end": 69, "current_speed": 10992, "design_speed": 54960, "set_speed": 55000, "state": 1024}, {"classification": null, "category": null, "aggregated_state": null, "mode": null, "program": null, "message": null, "start": 69, "end": 92, "current_speed": 19786, "design_speed": 54960, "set_speed": 55000, "state": null}, {"classification": null, "category": null, "aggregated_state": null, "mode": null, "program": null, "message": null, "start": 92, "end": 95, "current_speed": null, "design_speed": 54960, "set_speed": 55000, "state": null}, {"classification": null, "category": null, "aggregated_state": null, "mode": null, "program": null, "message": null, "start": 95, "end": 100, "current_speed": null, "design_speed": 54960, "set_speed": null, "state": null}]