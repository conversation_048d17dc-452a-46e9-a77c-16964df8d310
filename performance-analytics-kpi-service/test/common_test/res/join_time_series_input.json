{"customer": "readykit-replay", "eq_id": "c5cbe18b-be13-4554-b9b4-defd62e671c0", "kpi_model_key": "opi", "time_from": 0, "time_to": 100, "downtimes": [{"classification": "equipment_failure", "category": "equipment_failure", "raw_values": {"aggregated_state": 1024, "mode": 4, "program": 1, "state": 1024}, "start": -10, "end": -2, "message": {}}, {"classification": "prepared", "category": "prepared", "raw_values": {"aggregated_state": 4, "mode": 8, "program": 1, "state": 4}, "start": -2, "end": 0, "message": {}}, {"classification": "productive", "category": "productive", "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": 128}, "start": 0, "end": 17, "message": {}}, {"classification": "minor_stop", "category": "minor_stop", "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": 8}, "message": {"id": 366, "message_nr": 366, "info": "Lack of containers in infeed section", "description": "The following causes are possible:\n- Jammed containers block the infeed.\n- The upstream machine has malfunctioned or operates at low speed.\n- The container conveyor is OFF", "keyword": "", "keyword_id": "~Msg_Txt_Empty", "subsystem": "MMA", "subsystem_nr": 2000}, "start": 17, "end": 34}, {"classification": "productive", "category": "productive", "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": 128}, "start": 34, "end": 51, "message": {}}, {"classification": "equipment_failure", "category": "equipment_failure", "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": 1024}, "start": 51, "end": 69, "message": {}}], "speeds": [{"current_speed": 50563, "start": 0, "units_defect": 0, "units_total": 0, "end": 23}, {"current_speed": 0, "start": 23, "units_defect": 0, "units_total": 2336, "end": 46}, {"current_speed": 10992, "start": 46, "units_defect": 0, "units_total": 1500, "end": 69}, {"current_speed": 19786, "start": 69, "units_defect": 0, "units_total": 0, "end": 92}], "product_type": [{"product_id": 1, "start": 0, "end": 43}, {"product_id": 2, "start": 43, "end": 92}], "design_speeds": [{"speed": 54960, "start": 0, "end": 100}], "set_speeds": [{"speed": 48000, "start": 0, "end": 41}, {"speed": 55000, "start": 41, "end": 95}]}