import json

from fastapi import status
from fastapi.testclient import TestClient
from lib_cloud_sdk.util.file_io import read_json_file
from lib_dtm_common.models.api_models import GetDowntimeResponse, ResponseDowntime
import pytest

PATH = "v1/performance-analytics/machine-report/v2"


def test_machine_report_endpoint_din_8743_case_1(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    aws_credentials,
    downtime_client_mock,
):
    """
    DIN + No Line KPIs + Minor stops case + DMM
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=0&time_to=1621368000000&kpi_model_id=din_8743"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_din_8743_case_1.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_customer_settings_item["settings"][
        "minor_stop_config"
    ] = 5  # set minor stops to 5 minutes
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_din_8743.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(Item=performance_customer_settings_item)
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)

    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_din_8743_case_2(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    aws_credentials,
    downtime_client_mock,
):
    """
    DIN + Line KPIs + No Minor stops case + DMM
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=1&time_to=1621368000000&kpi_model_id=din_8743"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_din_8743_case_2.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_din_8743.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_for_block_counter_success_case_din_8743_case_2(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    aws_credentials,
    downtime_client_mock,
):
    """
    DIN + Line KPIs + No Minor stops case + DMM
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=1&time_to=1621368000000&kpi_model_id=din_8743"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_block_counter_success_din_8743_case_2.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_din_8743.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_block_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_block_speeds.json"
    )
    performance_data_meda_items = read_json_file(
        "test/integration_test/res/input_data/performance_meda_cache.json"
    )
    performance_data_equipment_cache_items = read_json_file(
        "test/integration_test/res/input_data/performance_equipment_cache.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    performance_meda_cache = dynamo_db_tables_mock.get("performance_meda_cache")
    performance_equipment_cache = dynamo_db_tables_mock.get("performance_equipment_cache")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_equipment_cache.put_item(
        TableName="performance-equipment-cache", Item=performance_data_equipment_cache_items
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_block_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)
    for item_id, items in performance_data_meda_items.items():
        for item_data in items:
            performance_meda_cache.put_item(TableName="performance-meda-cache", Item=item_data)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_insight_case_1(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Insight + No Line KPIs + No Minor stops case + DMM.
    If kpi model is insight then we should not apply minor stops.
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=0&time_to=1621368000000&kpi_model_id=insight"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_insight_case1.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "insight"
    ) + performance_kpi_model_data.get("din_8743")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_insight.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_insight_case_2(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Insight + Line KPIs + No Minor stops case + DMM.
    If kpi model is insight then we should not apply minor stops.
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=1&time_to=1621368000000&kpi_model_id=insight"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_insight_case2.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "insight"
    ) + performance_kpi_model_data.get("din_8743")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_insight.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_insight_only_case(
    aws_credentials,
    aws_credentials_inadequate_mock,
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Insight + No Line KPIs + No Minor stops case + DMM.
    If kpi model is insight then we should not apply minor stops.
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = (
        "time_from=1621339200000&language=en&line_kpi=0&time_to=1621368000000&kpi_model_id=insight"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_insight_case1.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "insight"
    ) + performance_kpi_model_data.get("din_8743")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/dmm_downtimes_insight.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_dmm_set_speeds.json"
    )
    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_get_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=1710028800000&timeTo=1710892800000"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}


def test_machine_report_endpoint_handles_incomplete_request(
    aws_request_mock, dynamo_db_tables_mock
):
    """
    Test that if a mandatory key is missing in the query parameters, the machine-report endpoint handles that correctly.
    """
    # Arrange
    base_path = PATH
    line_id = "1d4db519-e9c3-4e83-b64d-dc52148382ed"
    machine_id = "c5cbe18b-be13-4554-b9b4-defd62e671c0"
    query_params = (
        "language=en&line_kpi=1"  # removed mandatory keys: time_from, time_to kpi_model_id
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"
    expected_result = {
        "detail": [
            {
                "type": "missing",
                "loc": ["query", "time_from"],
                "msg": "Field required",
                "input": None,
            },
            {
                "type": "missing",
                "loc": ["query", "time_to"],
                "msg": "Field required",
                "input": None,
            },
            {
                "type": "missing",
                "loc": ["query", "kpi_model_id"],
                "msg": "Field required",
                "input": None,
            },
        ]
    }
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )

    from lambda_function import app

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result == expected_result


def test_machine_report_endpoint_din_8743_case_no_message(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Tests that machines do not fail to load when no messages are found for machine
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "8182eada-ade3-44c1-a768-5e4bb806d1da"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = (
        "time_from=1637042400000&language=en&line_kpi=0&time_to=1637043300000&kpi_model_id=din_8743"
    )
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_din_8743_case_no_messages.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_customer_settings_item["settings"][
        "minor_stop_config"
    ] = 5  # set minor stops to 5 minutes
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/downtimes_no_message.json"
    )

    # Create Mock DynamoDB tables and fill them with data if needed
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_din_8743_case_no_message_no_children(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Test to query machine report without children
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "8182eada-ade3-44c1-a768-5e4bb806d1da"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "time_from=1637042400000&language=en&line_kpi=0&time_to=1637043300000&kpi_model_id=din_8743&with_children=0"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_din_8743_case_no_messages_no_children.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_customer_settings_item["settings"][
        "minor_stop_config"
    ] = 5  # set minor stops to 5 seconds
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/downtimes_no_message_no_children.json"
    )

    # Create Mock DynamoDB tables and fill them with data if needed
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]
    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_endpoint_din_8743__case_validation_errors(
    dynamo_db_tables_mock,
    sqs_client,
    s3_client,
    downtime_client_mock,
):
    """
    Test that corrupted documents should be handled correctly and the machine report should not throw Validation Errors.
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"
    query_params = "time_from=1622520000000&checkmat_curve=false&language=en&line_kpi=1&time_to=1622548800000&kpi_model_id=din_8743"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_machine_report_din_8743_case_validation_errors.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_data_use_case_validation_errors_downtimes.json"
    )
    performance_data_item_product_type = read_json_file(
        "test/integration_test/res/input_data/performance_data_use_case_validation_errors_product_type.json"
    )
    performance_data_item_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_use_case_validation_errors_speeds.json"
    )
    performance_data_item_design_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_use_case_validation_errors_design_speeds.json"
    )
    performance_data_item_set_speeds = read_json_file(
        "test/integration_test/res/input_data/performance_data_use_case_validation_errors_set_speeds.json"
    )

    touch_messages = read_json_file("test/integration_test/res/input_data/messages_dmm.json")

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_product_type)
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds)

    # "Store" messages in S3
    bucket_name = "rk-zait-testing"
    key_name = (
        "account=readykit-replay/machine=5fcd1cee-73b9-4fd2-804a-1a04aa27e48a/messages_dmm.json"
    )
    s3_client.put_object(Bucket=bucket_name, Key=key_name, Body=json.dumps(touch_messages))
    response_downtimes = [ResponseDowntime(**downtime) for downtime in downtime_items["downtimes"]]
    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["account_id"],
        equipment_id=downtime_items["equipment_id"],
        kpi_model=downtime_items["kpi_model"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    actual_result = json.loads(machine_report_response.text)

    # Assert
    assert actual_result["timeFrom"] == expected_result["timeFrom"]
    assert actual_result["timeTo"] == expected_result["timeTo"]
    assert actual_result["unitsProduced"] == expected_result["unitsProduced"]
    assert actual_result["unitsDefect"] == expected_result["unitsDefect"]
    assert actual_result["speeds"] == expected_result["speeds"]
    assert actual_result["kpiResult"] == expected_result["kpiResult"]
    assert actual_result["reducedMachineState"] == expected_result["reducedMachineState"]


def test_machine_report_get_gt_week_raises_error():
    from fastapi.testclient import TestClient

    from lambda_function import app

    # Prepare URL
    base_path = PATH
    line_id = "65ea1a06-2698-45eb-baad-3a3e5fc25b73"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "timeFrom=1710028800000&timeTo=1710892800000"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"

    client = TestClient(app)
    response = client.get(url)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Wrong input: Requested duration is bigger than 7 days."}


@pytest.mark.parametrize(
    ("speeds_file", "design_speeds_file", "set_speeds_file", "downtimes_file"),
    [("speeds.json", "design_speeds.json", "set_speeds.json", "downtimes.json")],
)
def test_machine_report_endpoint_returns_consistent_values_for_counters(
    speeds_file,
    design_speeds_file,
    set_speeds_file,
    downtimes_file,
    dynamo_db_tables_mock,
    mocker,
):
    from lambda_function import app
    from machine_data_query.models.speeds import Speeds
    from lib_dtm_common.models.api_models import GetDowntimeResponse

    file_prefix = "test/integration_test/res/input_data/"

    with open(f"{file_prefix}{speeds_file}") as file:
        mock_speeds = json.load(file)
    with open(f"{file_prefix}{design_speeds_file}") as file:
        mock_design_speeds = json.load(file)
    with open(f"{file_prefix}{set_speeds_file}") as file:
        mock_set_speeds = json.load(file)
    mocker.patch(
        "lib_dtm_client.clients.query_handler.query_speeds",
        return_value=(
            Speeds.model_validate(mock_speeds),
            Speeds.model_validate(mock_design_speeds),
            Speeds.model_validate(mock_set_speeds),
        ),
    )

    with open(f"{file_prefix}{downtimes_file}") as file:
        mock_downtimes = GetDowntimeResponse.model_validate(json.load(file))
    mocker.patch(
        "lib_dtm_client.clients.query_handler._get_downtimes",
        return_value=mock_downtimes,
    )

    base_path = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    machine_id = "19a67401-f12f-43e2-9ef6-177c09fe9ffc"
    query_params = "time_from=1710828300000&language=en&line_kpi=1&time_to=1710914700000&kpi_model_id=din_8743&with_children=0"
    url = f"{base_path}/{line_id}/{machine_id}?{query_params}"
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_kpi_model_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model",
        Item=performance_kpi_model_item_line_assignment,
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_item_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )

    # Act
    client = TestClient(app)
    machine_report_response = client.get(url)
    result = json.loads(machine_report_response.text)

    # Assert
    for kpi_units in result["kpiResult"]["units"]:
        if kpi_units["name"] == "units_produced":
            assert kpi_units["number"] == result["unitsProduced"]
    for kpi_units in result["kpiResult"]["units"]:
        if kpi_units["name"] == "units_defect":
            assert kpi_units["number"] == result["unitsDefect"]
