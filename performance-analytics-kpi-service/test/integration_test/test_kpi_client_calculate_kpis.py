import pytest

from lib_cloud_sdk.util.file_io import read_json_file
from lib_kpi_config_client.models.config_types import KpiModel
from lib_kpi_config_client.models.enums import KpiModelScope, KpiModelType


@pytest.fixture
def kpi_model_insight(dynamo_db_tables_mock):
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_kpi_model_items = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    ).get("insight")
    for performance_kpi_model_item in performance_kpi_model_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item
        )


def test_multiple_design_speed_and_start_before_query_period(kpi_model_insight):
    """
    Test that if document contains multiple design speeds and the first design speed
    started before the actual calculation interval the actual calculation
    only takes the calculation interval and not the design speed start_time.
    The data is taken from a real customer support request
    https://krones-digital.atlassian.net/browse/S232002-59
    """

    from common.kpi_client import KpiClient
    from lib_kpi_config_client.models.api_models import KpiResult

    document = read_json_file(
        "test/integration_test/res/input_data/machine_report_data_with_design_speed_change_before_query.json"
    )
    default_nominal_speed = 45000

    kpi_client = KpiClient(document, default_nominal_speed)
    result = kpi_client.calculate_kpis(
        KpiModel(kpi_model_id="insight", kpi_model_type=KpiModelType.GLOBAL),
        KpiModelScope.LINE,
        "account",
    )
    kpi_result = KpiResult.model_validate(result["kpi_result"])

    for loss in kpi_result.waterfall_losses:
        if loss.name == "productive":
            assert loss.percent == 0.75
        if loss.name == "idle_time":
            assert loss.percent == 0.25


def test_multiple_design_speed_and_start_before_first_design_speed(kpi_model_insight):
    """
    Test that if document contains multiple design speeds and the first design speed
    started before the actual calculation interval the actual calculation
    only takes the calculation interval and not the design speed start_time.
    The data is taken from a real customer support request
    https://krones-digital.atlassian.net/browse/S232002-59
    """

    from common.kpi_client import KpiClient
    from lib_kpi_config_client.models.api_models import KpiResult

    document = read_json_file(
        "test/integration_test/res/input_data/machine_report_data_with_start_before_first_design_speed.json"
    )
    default_nominal_speed = 45000

    kpi_client = KpiClient(document, default_nominal_speed)
    result = kpi_client.calculate_kpis(
        KpiModel(kpi_model_id="insight", kpi_model_type=KpiModelType.GLOBAL),
        KpiModelScope.LINE,
        "account",
    )
    kpi_result = KpiResult.model_validate(result["kpi_result"])

    for loss in kpi_result.waterfall_bases:
        if loss.name == "theoretical_available_time":
            assert loss.duration == 200
