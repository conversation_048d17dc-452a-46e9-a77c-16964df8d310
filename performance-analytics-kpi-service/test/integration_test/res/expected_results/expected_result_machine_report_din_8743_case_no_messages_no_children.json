{"account": "readykit-replay", "equipmentId": "19a67401-f12f-43e2-9ef6-177c09fe9ffc", "kpiModelId": "din_8743", "timeFrom": *************, "timeTo": *************, "unitsProduced": 0, "unitsDefect": 0, "kpiResult": {"waterfallLosses": [{"name": "idle_time", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "scheduled_down_time", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "productive", "percent": 0.855855, "duration": 724015.0, "number": 4.0}, {"name": "external_failure", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "internal_failure", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "performance_loss_time", "percent": 0.855855, "duration": 724015.0}, {"name": "scrap_time", "percent": 0.0, "duration": 0.0}, {"name": "quality_time", "percent": 0.0, "duration": 0.0}, {"name": "unplanned_down_time", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "system_related_unplanned_down_time", "percent": 0.0, "duration": 0.0, "number": 0.0}, {"name": "not_system_related_unplanned_down_time", "percent": 0.0, "duration": 0.0, "number": 0.0}], "waterfallBases": [{"name": "theoretical_available_time", "percent": 1.0, "duration": 845955.0}, {"name": "machine_working_time", "percent": 1.0, "duration": 845955.0}, {"name": "operating_time", "percent": 1.0, "duration": 845955.0}, {"name": "running_time", "percent": 1.0, "duration": 845955.0}], "meanTimes": [{"name": "mtbfs", "duration": 845955.0}, {"name": "mttrs", "duration": 0.0}, {"name": "mtbf", "duration": 845955.0}, {"name": "mttr", "duration": 0.0}], "quotients": [{"name": "quality", "percent": 0.0}, {"name": "performance", "percent": 0.0}, {"name": "availability", "percent": 1.0}, {"name": "oee", "percent": 0.0}, {"name": "runtime_factor", "percent": 1.0}, {"name": "technical_oee", "percent": 1.0}, {"name": "efficiency", "percent": 0.0}, {"name": "technical_efficiency", "percent": 0.0}, {"name": "scrap_rate", "percent": 0.0}], "speeds": [{"name": "average_speed", "number": 0.0}], "units": [{"name": "units_defect", "number": 0.0}, {"name": "units_produced", "number": 0.0}, {"name": "units_total", "number": 0.0}], "unitTypes": [{"name": "performance_losses", "number": 11313.0}, {"name": "system_related_scheduled_output", "number": 11313.0}], "additionalKpis": [{"name": "manufactured_output", "number": 0.0}, {"name": "scheduled_output", "number": 11313.0}, {"name": "performance_losses", "percent": 1.0}, {"name": "technical_availability", "percent": 1.0}], "weightedNominals": [{"name": "nominal_speed_excluding_non_operational_time", "number": 56250.0}, {"name": "nominal_speed", "number": 56250.0}]}, "reducedMachineState": {"duration": {"categories": {"minor_stop": 121940, "productive": 724015}, "classifications": {"minor_stop": 121940, "productive": 724015}}, "overallDuration": 845955, "ratio": {"categories": {"minor_stop": 0.144145, "productive": 0.855855}, "classifications": {"minor_stop": 0.144145, "productive": 0.855855}}, "count": {"categories": {"minor_stop": 5, "productive": 4}, "classifications": {"minor_stop": 5, "productive": 4}}}, "downtimes": [{"category": "minor_stop", "classification": "minor_stop", "start": 1637042454045, "end": 1637042486034, "duration": 31989, "rawValues": {"state": 8, "aggregatedState": 8, "mode": 8, "program": 1}, "message": {"configId": "AM_lack", "messageType": "artificial"}}, {"category": "productive", "classification": "productive", "start": 1637042486034, "end": 1637042932047, "duration": 446013, "rawValues": {"state": 128, "aggregatedState": 128, "mode": 8, "program": 1}}, {"category": "minor_stop", "classification": "minor_stop", "start": 1637042932047, "end": 1637042986056, "duration": 54009, "rawValues": {"state": 32768, "aggregatedState": 32768, "mode": 8, "program": 1}, "message": {"configId": "AM_idle", "messageType": "artificial"}}, {"category": "productive", "classification": "productive", "start": 1637042986056, "end": 1637043014047, "duration": 27991, "rawValues": {"state": 128, "aggregatedState": 128, "mode": 8, "program": 1}}, {"category": "minor_stop", "classification": "minor_stop", "start": 1637043014047, "end": 1637043028066, "duration": 14019, "rawValues": {"state": 1024, "aggregatedState": 1024, "mode": 8, "program": 1}, "message": {"configId": "AM_equipment_failure", "messageType": "artificial"}}, {"category": "productive", "classification": "productive", "start": 1637043028066, "end": 1637043154068, "duration": 126002, "rawValues": {"state": 128, "aggregatedState": 128, "mode": 8, "program": 1}}, {"category": "minor_stop", "classification": "minor_stop", "start": 1637043154068, "end": 1637043174058, "duration": 19990, "rawValues": {"state": 8, "aggregatedState": 8, "mode": 8, "program": 1}, "message": {"configId": "AM_lack", "messageType": "artificial"}}, {"category": "productive", "classification": "productive", "start": 1637043174058, "end": 1637043298067, "duration": 124009, "rawValues": {"state": 128, "aggregatedState": 128, "mode": 8, "program": 1}}, {"category": "minor_stop", "classification": "minor_stop", "start": 1637043298067, "end": 1637043348068, "duration": 1933, "rawValues": {"state": 8, "aggregatedState": 8, "mode": 8, "program": 1}, "message": {"configId": "AM_lack", "messageType": "artificial"}}], "speeds": []}