{"account_id": "readykit-replay", "equipment_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "kpi_model": "insight", "downtimes": [{"classification": "productive", "category": "productive", "start": *************, "uncut_start": *************, "end": *************, "duration": 1058188, "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": *************, "end": *************, "duration": 1058188, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "stopped", "category": "stopped", "start": *************, "end": *************, "duration": 33065, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": ***********03, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "manual_changes": {"classification": "stopped", "category": "stopped"}, "children": [{"start": *************, "end": 1621340262215, "duration": 4027, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "stopped", "classification": "stopped"}, {"start": 1621340262215, "end": 1621340285239, "duration": 23024, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}, {"start": 1621340285239, "end": *************, "duration": 6014, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": *************, "end": 1621341787723, "duration": 1496470, "last_update_at": 1661333523548, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": *************, "end": 1621341787723, "duration": 1496470, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621341787723, "end": 1621341846835, "duration": 59112, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333523555, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621341787723, "end": 1621341797759, "duration": 10036, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621341797759, "end": 1621341799783, "duration": 2024, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621341799783, "end": 1621341839811, "duration": 40028, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621341839811, "end": 1621341846835, "duration": 7024, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621341846835, "end": 1621347534537, "duration": 5687702, "last_update_at": 1661333524060, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621341846835, "end": 1621347534537, "duration": 5687702, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack_branch", "category": "lack_branch", "start": 1621347534537, "end": 1621347598663, "duration": 64126, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "last_update_at": 1661333524069, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621347534537, "end": 1621347598663, "duration": 64126, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "category": "lack_branch", "classification": "lack_branch"}]}, {"classification": "clean", "category": "clean", "start": 1621347598663, "end": 1621348013270, "duration": 414607, "last_update_at": 1661333525065, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 8, "state": -1}, "children": [{"start": 1621347598663, "end": 1621347598664, "duration": 1, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621347598664, "end": 1621347598665, "duration": 1, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621347598665, "end": 1621347840035, "duration": 241370, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621347840035, "end": 1621347921153, "duration": 81118, "message": {"configId": "8801", "messageNr": "8099", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "The container stop has been locked by the foam cleaning selection"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621347921153, "end": 1621348013270, "duration": 92117, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}]}, {"classification": "changeover", "category": "changeover", "start": 1621348013270, "end": 1621350256992, "duration": 2243722, "last_update_at": 1661333526586, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "children": [{"start": 1621348013270, "end": 1621348678048, "duration": 664778, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621348678048, "end": 1621349484060, "duration": 806012, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621349484060, "end": 1621350256992, "duration": 772932, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}]}, {"classification": "break", "category": "break", "start": 1621350256992, "end": 1621350437422, "duration": 180430, "last_update_at": 1661333527157, "message": {"configId": "AM_break", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 64, "state": -1}, "children": [{"start": 1621350256992, "end": 1621350256993, "duration": 1, "message": {"configId": "AM_break", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 64, "state": -1}, "category": "break", "classification": "break"}, {"start": 1621350256993, "end": 1621350437422, "duration": 180429, "message": {"configId": "AM_break", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 64, "state": -1}, "category": "break", "classification": "break"}]}, {"classification": "clean", "category": "clean", "start": 1621350437422, "end": 1621350699954, "duration": 262532, "last_update_at": 1661333527688, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 8, "state": -1}, "children": [{"start": 1621350437422, "end": 1621350699954, "duration": 262532, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}]}, {"classification": "changeover", "category": "changeover", "start": 1621350699954, "end": 1621351407004, "duration": 707050, "last_update_at": 1661333528177, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "children": [{"start": 1621350699954, "end": 1621351407003, "duration": 707049, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621351407003, "end": 1621351407004, "duration": 1, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}]}, {"classification": "productive", "category": "productive", "start": 1621351407004, "end": 1621352983243, "duration": 1576239, "last_update_at": 1661333528765, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "children": [{"start": 1621351407004, "end": 1621351453121, "duration": 46117, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "category": "productive", "classification": "productive"}, {"start": 1621351453121, "end": 1621352983243, "duration": 1530122, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "prepared", "category": "prepared", "start": 1621352983243, "end": 1621352989255, "duration": 6012, "last_update_at": 1661333528773, "message": {"configId": "AM_prepared", "messageType": "artificial"}, "raw_values": {"aggregated_state": 4, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621352983243, "end": 1621352989255, "duration": 6012, "message": {"configId": "AM_prepared", "messageType": "artificial"}, "raw_values": {"aggregated_state": 4, "mode": 8, "program": 1, "state": -1}, "category": "prepared", "classification": "prepared"}]}, {"classification": "productive", "category": "productive", "start": 1621352989255, "end": 1621352994290, "duration": 5035, "last_update_at": 1661333528785, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621352989255, "end": 1621352994290, "duration": 5035, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621352994290, "end": 1621353085605, "duration": 91315, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333529311, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621352994290, "end": 1621353078601, "duration": 84311, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621353078601, "end": 1621353085605, "duration": 7004, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621353085605, "end": 1621353099649, "duration": 14044, "last_update_at": 1661333529325, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353085605, "end": 1621353099649, "duration": 14044, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621353099649, "end": 1621353378992, "duration": 279343, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333529880, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353099649, "end": 1621353372983, "duration": 273334, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621353372983, "end": 1621353377986, "duration": 5003, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621353377986, "end": 1621353378992, "duration": 1006, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621353378992, "end": 1621353383994, "duration": 5002, "last_update_at": 1661333529889, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353378992, "end": 1621353383994, "duration": 5002, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621353383994, "end": 1621353507152, "duration": 123158, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1652264060812, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353383994, "end": 1621353501140, "duration": 117146, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621353501140, "end": 1621353507152, "duration": 6012, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621353507152, "end": 1621353778849, "duration": 271697, "last_update_at": 1661333530397, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353507152, "end": 1621353778849, "duration": 271697, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621353778849, "end": 1621354080246, "duration": 301397, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333530998, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621353778849, "end": 1621354074243, "duration": 295394, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621354074243, "end": 1621354080246, "duration": 6003, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621354080246, "end": 1621354120344, "duration": 40098, "last_update_at": 1661333531010, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621354080246, "end": 1621354120344, "duration": 40098, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621354120344, "end": 1621354154402, "duration": 34058, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333530425, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621354120344, "end": 1621354125360, "duration": 5016, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621354125360, "end": 1621354154402, "duration": 29042, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621354154402, "end": 1621355785064, "duration": 1630662, "last_update_at": 1661333531587, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621354154402, "end": 1621355785064, "duration": 1630662, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621355785064, "end": 1621356199596, "duration": 414532, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333532109, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621355785064, "end": 1621356193592, "duration": 408528, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621356193592, "end": 1621356199596, "duration": 6004, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621356199596, "end": 1621357362300, "duration": 1162704, "last_update_at": 1661333532593, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621356199596, "end": 1621357362300, "duration": 1162704, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack_branch", "category": "lack_branch", "start": 1621357362300, "end": 1621357419397, "duration": 57097, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "last_update_at": 1661333532601, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621357362300, "end": 1621357417389, "duration": 55089, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "category": "lack_branch", "classification": "lack_branch"}, {"start": 1621357417389, "end": 1621357419397, "duration": 2008, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "category": "lack_branch", "classification": "lack_branch"}]}, {"classification": "run_down", "category": "run_down", "start": 1621357419397, "end": 1621357423405, "duration": 4008, "last_update_at": 1652264064339, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 4, "state": -1}, "children": [{"start": 1621357419397, "end": 1621357419398, "duration": 1, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 4, "state": -1}, "category": "run_down", "classification": "run_down"}, {"start": 1621357419398, "end": 1621357423405, "duration": 4007, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 4, "program": 4, "state": -1}, "category": "run_down", "classification": "run_down"}]}, {"classification": "productive", "category": "productive", "start": 1621357423405, "end": 1621357440471, "duration": 17066, "last_update_at": 1652264064760, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}, "children": [{"start": 1621357423405, "end": 1621357440471, "duration": 17066, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "run_down", "category": "run_down", "start": 1621357440471, "end": 1621357477590, "duration": 37119, "last_update_at": 1652264065460, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}, "children": [{"start": 1621357440471, "end": 1621357443478, "duration": 3007, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}, "category": "run_down", "classification": "run_down"}, {"start": 1621357443478, "end": 1621357477590, "duration": 34112, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 4, "state": -1}, "category": "run_down", "classification": "run_down"}]}, {"classification": "clean", "category": "clean", "start": 1621357477590, "end": 1621357616800, "duration": 139210, "last_update_at": 1661333533197, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 8, "state": -1}, "children": [{"start": 1621357477590, "end": 1621357484588, "duration": 6998, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621357484588, "end": 1621357616800, "duration": 132212, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}]}, {"classification": "changeover", "category": "changeover", "start": 1621357616800, "end": 1621357884197, "duration": 267397, "last_update_at": 1661333533737, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 16, "state": -1}, "children": [{"start": 1621357616800, "end": 1621357620806, "duration": 4006, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621357620806, "end": 1621357884197, "duration": 263391, "message": {"configId": "4825", "messageNr": "2101", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Activate height adjustment system, new required height(s), not yet positioned"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}]}, {"classification": "clean", "category": "clean", "start": 1621357884197, "end": 1621359290927, "duration": 1406730, "last_update_at": 1661333534815, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "children": [{"start": 1621357884197, "end": 1621358805093, "duration": 920896, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621358805093, "end": 1621358807104, "duration": 2011, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621358807104, "end": 1621358889297, "duration": 82193, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621358889297, "end": 1621359251874, "duration": 362577, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621359251874, "end": 1621359283915, "duration": 32041, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}, {"start": 1621359283915, "end": 1621359290927, "duration": 7012, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 8, "state": -1}, "category": "clean", "classification": "clean"}]}, {"classification": "start_up", "category": "start_up", "start": 1621359290927, "end": 1621359290928, "duration": 1, "last_update_at": 1661333534822, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 2, "state": -1}, "children": [{"start": 1621359290927, "end": 1621359290928, "duration": 1, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 2, "state": -1}, "category": "start_up", "classification": "start_up"}]}, {"classification": "productive", "category": "productive", "start": 1621359290928, "end": 1621359844174, "duration": 553246, "last_update_at": 1661333535807, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "children": [{"start": 1621359290928, "end": 1621359337005, "duration": 46077, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "category": "productive", "classification": "productive"}, {"start": 1621359337005, "end": 1621359844174, "duration": 507169, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621359844174, "end": 1621360095486, "duration": 251312, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333536325, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621359844174, "end": 1621360088472, "duration": 244298, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621360088472, "end": 1621360095486, "duration": 7014, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621360095486, "end": 1621360116543, "duration": 21057, "last_update_at": 1661333536332, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621360095486, "end": 1621360116543, "duration": 21057, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621360116543, "end": 1621360150606, "duration": 34063, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333535828, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621360116543, "end": 1621360130585, "duration": 14042, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621360130585, "end": 1621360150606, "duration": 20021, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621360150606, "end": 1621361089929, "duration": 939323, "last_update_at": 1661333536862, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621360150606, "end": 1621361089929, "duration": 939323, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "changeover", "category": "changeover", "start": 1621361089929, "end": 1621362907601, "duration": 1817672, "last_update_at": 1661333538560, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 16, "state": -1}, "children": [{"start": 1621361089929, "end": 1621361093932, "duration": 4003, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621361093932, "end": 1621361125010, "duration": 31078, "message": {"configId": "4825", "messageNr": "2101", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Activate height adjustment system, new required height(s), not yet positioned"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621361125010, "end": 1621361303307, "duration": 178297, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621361303307, "end": 1621362292908, "duration": 989601, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}, {"start": 1621362292908, "end": 1621362907601, "duration": 614693, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}, "category": "changeover", "classification": "changeover"}]}, {"classification": "start_up", "category": "start_up", "start": 1621362907601, "end": 1621362907602, "duration": 1, "last_update_at": 1661333538569, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 2, "state": -1}, "children": [{"start": 1621362907601, "end": 1621362907602, "duration": 1, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 2, "state": -1}, "category": "start_up", "classification": "start_up"}]}, {"classification": "productive", "category": "productive", "start": 1621362907602, "end": 1621363333542, "duration": 425940, "last_update_at": 1661333539067, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "children": [{"start": 1621362907602, "end": 1621362953729, "duration": 46127, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "category": "productive", "classification": "productive"}, {"start": 1621362953729, "end": 1621363333542, "duration": 379813, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "lack", "start": 1621363333542, "end": 1621363370601, "duration": 37059, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1661333539074, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363333542, "end": 1621363349573, "duration": 16031, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}, {"start": 1621363349573, "end": 1621363370601, "duration": 21028, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1621363370601, "end": 1621363497881, "duration": 127280, "last_update_at": 1652264072297, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363370601, "end": 1621363497881, "duration": 127280, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack", "category": "lack", "start": 1621363497881, "end": 1621363534957, "duration": 37076, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "last_update_at": 1661333539565, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363497881, "end": 1621363525949, "duration": 28068, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}, {"start": 1621363525949, "end": 1621363534957, "duration": 9008, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "category": "lack", "classification": "lack"}]}, {"classification": "productive", "category": "productive", "start": 1621363534957, "end": 1621363558015, "duration": 23058, "last_update_at": 1661333539574, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363534957, "end": 1621363558015, "duration": 23058, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621363558015, "end": 1621363692200, "duration": 134185, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1652264073974, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363558015, "end": 1621363686191, "duration": 128176, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621363686191, "end": 1621363692200, "duration": 6009, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621363692200, "end": 1621364028963, "duration": 336763, "last_update_at": 1661333540141, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621363692200, "end": 1621364028963, "duration": 336763, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621364028963, "end": 1621364341351, "duration": 312388, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333540652, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621364028963, "end": 1621364335341, "duration": 306378, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621364335341, "end": 1621364341351, "duration": 6010, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621364341351, "end": 1621364718264, "duration": 376913, "last_update_at": 1661333541139, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621364341351, "end": 1621364718264, "duration": 376913, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621364718264, "end": 1621364817381, "duration": 99117, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333541146, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621364718264, "end": 1621364811375, "duration": 93111, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621364811375, "end": 1621364817381, "duration": 6006, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621364817381, "end": 1621365064950, "duration": 247569, "last_update_at": 1661333541675, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621364817381, "end": 1621365064950, "duration": 247569, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "tailback", "category": "tailback", "start": 1621365064950, "end": 1621366991205, "duration": 1926255, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "last_update_at": 1661333542196, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621365064950, "end": 1621366985197, "duration": 1920247, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}, {"start": 1621366985197, "end": 1621366991205, "duration": 6008, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "category": "tailback", "classification": "tailback"}]}, {"classification": "productive", "category": "productive", "start": 1621366991205, "end": 1621367418117, "duration": 426912, "last_update_at": 1661333542819, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621366991205, "end": 1621367418117, "duration": 426912, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}, {"classification": "lack_branch", "category": "lack_branch", "start": 1621367418117, "end": 1621367862644, "duration": 444527, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "last_update_at": 1661333543296, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621367418117, "end": 1621367856636, "duration": 438519, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "category": "lack_branch", "classification": "lack_branch"}, {"start": 1621367856636, "end": 1621367862644, "duration": 6008, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "category": "lack_branch", "classification": "lack_branch"}]}, {"classification": "productive", "category": "productive", "start": 1621367862644, "end": 1621368000000, "uncut_end": 1621368740712, "duration": 137356, "last_update_at": 1661333544045, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"start": 1621367862644, "end": 1621368000000, "duration": 137356, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "category": "productive", "classification": "productive"}]}]}