{"5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22": [{"PK": "ACCOUNT#readykit-replay#LINE#5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "SK": "DATAKEY#BLOCK#MACHINE#5fcd1cee-73b9-4fd2-804a-1a04aa27e48a#GEN_Block_Infeed", "accountId": "readykit-replay", "childDatapointIds": null, "created_at": *************, "dataKey": "GEN_Block_Infeed", "datapointType": "Logical", "description": {"de": "Einlaufzähler Block", "en": "Block Infeed"}, "enumeration": null, "id": "ef206db3-147c-4dfa-910e-1c444e181feb", "lineId": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "machineKnumber": "K136779 - very long text blablablabla", "machine_uuid": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "parentDatapointId": null, "path": null, "rootDatapointId": null, "rootPath": null, "serviceLabel": [], "sourceId": "548bff14-d0d3-4331-bf1e-e6886a675997", "tag": "K136779 - very long text blablablabla/TestInfeedlogical", "techDesc": "GEN_Block_Infeed", "uom": "71e8ceae-c336-466e-a390-cae5535df3c5"}, {"PK": "ACCOUNT#readykit-replay#LINE#5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "SK": "DATAKEY#BLOCK#MACHINE#5fcd1cee-73b9-4fd2-804a-1a04aa27e48a#GEN_Block_Outfeed", "accountId": "readykit-replay", "childDatapointIds": null, "created_at": *************, "dataKey": "GEN_Block_Outfeed", "datapointType": "Physical", "description": {"de": "Auslaufzähler Block", "en": "Block outfeed"}, "enumeration": null, "id": "641a0077-1cbd-46ad-ae62-a517852b9d2e", "lineId": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "machineKnumber": "K136779 - very long text blablablabla", "machine_uuid": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "parentDatapointId": null, "path": null, "rootDatapointId": null, "rootPath": null, "serviceLabel": [], "sourceId": "548bff14-d0d3-4331-bf1e-e6886a675997", "tag": "K136779 - very long text blablablabla/GEN_Total_Outfeed", "techDesc": "GEN_Block_Outfeed", "uom": "71e8ceae-c336-466e-a390-cae5535df3c5"}, {"PK": "ACCOUNT#readykit-replay#LINE#5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "SK": "DATAKEY#BLOCK#MACHINE#5fcd1cee-73b9-4fd2-804a-1a04aa27e48a#GEN_Block_Reject", "accountId": "readykit-replay", "childDatapointIds": null, "created_at": *************, "dataKey": "GEN_Block_Reject", "datapointType": "Logical", "description": {"de": "Ausschusszähler Block", "en": "Block reject"}, "enumeration": null, "id": "2923475e-b759-4d45-b5bd-85b499659ef8", "lineId": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "machineKnumber": "SomeFancyKNumber", "machine_uuid": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "parentDatapointId": null, "path": null, "rootDatapointId": null, "rootPath": null, "serviceLabel": [], "sourceId": null, "tag": "SomeFancyKNumber/gen-block-rej", "techDesc": "GEN_Block_Reject", "uom": "71e8ceae-c336-466e-a390-cae5535df3c5"}]}