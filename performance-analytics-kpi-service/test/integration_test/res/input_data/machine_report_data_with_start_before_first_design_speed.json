{"time_from": 0, "time_to": 200, "account_id": "test-account", "equipment_id": "equipmentId", "downtimes": [{"classification": "productive", "category": "productive", "start": 0, "end": 100, "duration": 100, "updated_at": 120}, {"classification": "productive", "category": "productive", "start": 100, "end": 120, "duration": 120, "updated_at": 120}, {"classification": "unused_time", "category": "unused_time", "start": 120, "end": 145, "duration": 25, "message": {"config_id": "AM_unused_time", "message_type": "artificial"}, "updated_at": 170}, {"classification": "productive", "category": "productive", "start": 145, "end": 200, "duration": 30, "updated_at": *************}], "design_speeds": [{"start": 100, "end": 110, "ongoing": null, "speed": 45000}, {"start": 110, "end": 150, "ongoing": null, "speed": 36000}, {"start": 150, "end": 200, "ongoing": null, "speed": 45000}]}