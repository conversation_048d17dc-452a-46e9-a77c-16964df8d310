{"5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22": {"pk": "A#readykit-replay", "sk": "assigned#L#5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22#S#*************", "account": "readykit-replay", "config": {"config_type": "time_validity", "kpi_model_id": "din_8743", "kpi_model_type": "global", "start": *************}, "config_type": "time_validity", "created_at": *************, "created_by": "config_generator", "line_id": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22", "updated_at": *************, "updated_by": "config_generator"}, "1d4db519-e9c3-4e83-b64d-dc52148382ed": {"pk": "A#readykit-replay", "sk": "assigned#L#1d4db519-e9c3-4e83-b64d-dc52148382ed#S#*************", "account": "readykit-replay", "config": {"config_type": "time_validity", "kpi_model_id": "opi", "kpi_model_type": "global", "start": *************}, "config_type": "time_validity", "created_at": *************, "created_by": "config_generator", "line_id": "1d4db519-e9c3-4e83-b64d-dc52148382ed", "updated_at": *************, "updated_by": "config_generator"}, "8182eada-ade3-44c1-a768-5e4bb806d1da": {"pk": "A#readykit-replay", "sk": "assigned#L#8182eada-ade3-44c1-a768-5e4bb806d1da#S#*************", "account": "readykit-replay", "config": {"config_type": "time_validity", "kpi_model_id": "din_8743", "kpi_model_type": "global", "start": *************}, "config_type": "time_validity", "created_at": *************, "created_by": "config_generator", "line_id": "8182eada-ade3-44c1-a768-5e4bb806d1da", "updated_at": *************, "updated_by": "config_generator"}, "insight": [{"pk": "global", "sk": "config#K#insight#T#kpi_definition", "config": {"additional_kpis": [{"kpi_result_type": "number", "name": "manufactured_output", "scope": "BOTH", "value": "scrap_units + quality_output", "variables": [{"name": "scrap_units", "ref": "units.units_defect.number"}, {"name": "quality_output", "ref": "units.units_produced.number"}]}, {"kpi_result_type": "number", "name": "scheduled_output", "scope": "BOTH", "value": "manufactured_output + performance_losses", "variables": [{"name": "manufactured_output", "ref": "additional_kpis.manufactured_output.number"}, {"name": "performance_losses", "ref": "unit_types.performance_losses.number"}]}, {"kpi_result_type": "percent", "name": "performance_losses", "scope": "BOTH", "value": "performance_losses / scheduled_output", "variables": [{"name": "performance_losses", "ref": "unit_types.performance_losses.number"}, {"name": "scheduled_output", "ref": "additional_kpis.scheduled_output.number"}]}, {"fallback_value": 1, "kpi_result_type": "percent", "name": "technical_availability", "scope": "BOTH", "value": "running_time / (operating_time - not_system_related_unplanned_down_time)", "variables": [{"name": "running_time", "ref": "waterfall_bases.running_time.percent"}, {"name": "operating_time", "ref": "waterfall_bases.operating_time.percent"}, {"name": "not_system_related_unplanned_down_time", "ref": "waterfall_losses.not_system_related_unplanned_down_time.percent"}]}], "config_type": "kpi_definition", "kpi_model_id": "insight", "kpi_model_type": "global", "mappings": [{"items": [{"classifications": ["equipment_failure", "prepared", "external_failure", "stopped", "intermediate", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "top_downtimes", "scope": "BOTH"}, {"items": [{"categories": ["lack"]}], "name": "lack", "scope": "BOTH"}, {"items": [{"categories": ["tailback"]}], "name": "tailback", "scope": "BOTH"}, {"items": [{"categories": ["lack_branch"]}], "name": "lack_branch", "scope": "BOTH"}, {"items": [{"categories": ["tailback_branch"]}], "name": "tailback_branch", "scope": "BOTH"}, {"items": [{"categories": ["productive"]}], "name": "productive", "scope": "BOTH"}, {"items": [{"categories": ["stopped"]}], "name": "stopped", "scope": "BOTH"}, {"items": [{"categories": ["prepared"]}], "name": "prepared", "scope": "BOTH"}, {"items": [{"categories": ["emergency_stop"]}], "name": "emergency_stop", "scope": "BOTH"}, {"items": [{"categories": ["equipment_failure"]}], "name": "equipment_failure", "scope": "BOTH"}, {"items": [{"categories": ["external_failure"]}], "name": "external_failure", "scope": "BOTH"}, {"items": [{"categories": ["off"]}], "name": "off", "scope": "BOTH"}, {"items": [{"categories": ["manual"]}], "name": "manual", "scope": "BOTH"}, {"items": [{"categories": ["held"]}], "name": "held", "scope": "BOTH"}, {"items": [{"categories": ["idle"]}], "name": "idle", "scope": "BOTH"}, {"items": [{"categories": ["start_up"]}], "name": "start_up", "scope": "BOTH"}, {"items": [{"categories": ["run_down"]}], "name": "run_down", "scope": "BOTH"}, {"items": [{"categories": ["clean"]}], "name": "clean", "scope": "BOTH"}, {"items": [{"categories": ["changeover"]}], "name": "changeover", "scope": "BOTH"}, {"items": [{"categories": ["waiting"]}], "name": "waiting", "scope": "BOTH"}, {"items": [{"categories": ["break"]}], "name": "break", "scope": "BOTH"}, {"items": [{"categories": ["intermediate"]}], "name": "intermediate", "scope": "BOTH"}, {"items": [{"categories": ["undefined"]}], "name": "undefined", "scope": "BOTH"}, {"items": [{"categories": ["productive"], "classifications": ["productive"]}], "name": "performance_loss_time", "scope": "MACHINE"}, {"items": [{"categories": ["productive"]}], "name": "performance_loss_time", "scope": "LINE"}, {"items": [{"categories": ["undefined", "unused_time"]}], "name": "idle_time", "scope": "BOTH"}, {"items": [{"categories": ["equipment_failure", "external_failure", "stopped", "intermediate", "prepared", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "unplanned_down_time", "scope": "BOTH"}, {"items": [{"categories": ["clean", "run_down", "start_up", "changeover", "waiting", "break"]}], "name": "scheduled_down_time", "scope": "BOTH"}, {"items": [{"classifications": ["prepared", "equipment_failure"]}], "name": "system_related_unplanned_down_time", "scope": "MACHINE"}, {"items": [{"classifications": ["prepared", "equipment_failure", "lack", "tailback", "lack_branch", "tailback_branch", "external_failure"]}], "name": "system_related_unplanned_down_time", "scope": "LINE"}, {"items": [{"classifications": ["external_failure", "stopped", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "not_system_related_unplanned_down_time", "scope": "MACHINE"}, {"items": [{"classifications": ["held", "emergency_stop", "idle", "stopped", "off", "manual"]}], "name": "not_system_related_unplanned_down_time", "scope": "LINE"}, {"items": [{"classifications": ["productive"]}], "name": "mean_time_mtbfs_time_dividend", "scope": "BOTH"}, {"items": [{"classifications": ["prepared", "equipment_failure"]}], "name": "mean_time_mtbfs_count_divisor", "scope": "BOTH"}, {"items": [{"classifications": ["prepared", "equipment_failure"]}], "name": "mean_time_mttrs_time_dividend", "scope": "BOTH"}, {"items": [{"classifications": ["prepared", "equipment_failure"]}], "name": "mean_time_mttrs_count_divisor", "scope": "BOTH"}, {"items": [{"classifications": ["productive"]}], "name": "mean_time_mtbf_time_dividend", "scope": "BOTH"}, {"items": [{"classifications": ["equipment_failure", "external_failure", "stopped", "intermediate", "prepared", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "mean_time_mtbf_count_divisor", "scope": "BOTH"}, {"items": [{"classifications": ["equipment_failure", "external_failure", "stopped", "intermediate", "prepared", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "mean_time_mttr_time_dividend", "scope": "BOTH"}, {"items": [{"classifications": ["equipment_failure", "external_failure", "stopped", "intermediate", "prepared", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "mean_time_mttr_count_divisor", "scope": "BOTH"}, {"items": [{"classifications": ["clean", "run_down", "start_up", "changeover", "waiting", "break"]}], "name": "planned_downtime", "scope": "BOTH"}], "mean_times": [{"count_divisor": "mappings.mean_time_mtbfs_count_divisor", "fallback_kpi": "overall_duration", "name": "mtbfs", "scope": "BOTH", "time_dividend": "mappings.mean_time_mtbfs_time_dividend"}, {"count_divisor": "mappings.mean_time_mttrs_count_divisor", "name": "mttrs", "scope": "BOTH", "time_dividend": "mappings.mean_time_mttrs_time_dividend"}, {"count_divisor": "mappings.mean_time_mtbf_count_divisor", "fallback_kpi": "overall_duration", "name": "mtbf", "scope": "BOTH", "time_dividend": "mappings.mean_time_mtbf_time_dividend"}, {"count_divisor": "mappings.mean_time_mttr_count_divisor", "name": "mttr", "scope": "BOTH", "time_dividend": "mappings.mean_time_mttr_time_dividend"}], "quotients": [{"color": "#A9CAE0", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "name": "quality", "scope": "BOTH"}, {"color": "#052A68", "dividend": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "performance", "scope": "BOTH"}, {"color": "#059FDC", "dividend": ["waterfall_bases.operating_time.percent"], "divisor": ["waterfall_bases.machine_working_time.percent"], "name": "availability", "scope": "BOTH"}, {"color": "#9E0008", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_bases.machine_working_time.percent"], "name": "oee", "scope": "BOTH"}, {"dividend": ["mean_times.mtbfs.duration"], "divisor": ["mean_times.mtbfs.duration", "mean_times.mttrs.duration"], "name": "runtime_factor", "scope": "BOTH"}, {"color": "#059FDC", "dividend": ["waterfall_bases.running_time.percent"], "divisor": ["waterfall_bases.theoretical_available_time.percent"], "name": "technical_oee", "scope": "BOTH"}, {"color": "#6AB0D8", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "efficiency", "scope": "BOTH"}, {"color": "#1EA1EC", "dividend": ["units.units_produced.number"], "divisor": ["unit_types.system_related_scheduled_output.number"], "name": "technical_efficiency", "scope": "BOTH"}, {"dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_losses.scrap_time.percent", "waterfall_losses.quality_time.percent"], "name": "quality_factor", "scope": "LINE"}, {"dividend": ["waterfall_losses.scrap_time.percent"], "divisor": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "name": "scrap_rate", "scope": "BOTH"}, {"color": "#A9CAE0", "dividend": ["waterfall_losses.unplanned_down_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "technical_fault", "scope": "BOTH"}, {"color": "#052A68", "dividend": ["waterfall_bases.running_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "insight_availability", "scope": "BOTH"}], "speeds": [{"name": "average_speed", "source": "average_speed"}], "units": [{"name": "units_defect", "source": "units_defect"}, {"name": "units_produced", "source": "units_produced"}], "unit_types": [{"name": "theoretical_production_count", "refs": ["waterfall_losses.productive.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "LINE"}, {"name": "performance_losses", "refs": ["waterfall_losses.performance_loss_time.duration", "waterfall_losses.unplanned_down_time.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "BOTH"}, {"name": "system_related_scheduled_output", "refs": ["waterfall_losses.quality_time.duration", "waterfall_losses.scrap_time.duration", "waterfall_losses.performance_loss_time.duration", "waterfall_losses.system_related_unplanned_down_time.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "BOTH"}], "waterfall_bases": [{"addends": [1], "color": "#4F5966", "name": "theoretical_available_time", "scope": "BOTH"}, {"color": "#6A7788", "name": "machine_working_time", "scope": "BOTH", "subtract": ["waterfall_losses.idle_time.percent"]}, {"color": "#8495AA", "name": "operating_time", "scope": "BOTH", "subtract": ["waterfall_losses.scheduled_down_time.percent"]}, {"color": "#92D050", "name": "running_time", "scope": "BOTH", "subtract": ["waterfall_losses.unplanned_down_time.percent"]}], "waterfall_losses": [{"color": "#66B0E2", "name": "lack", "refs": ["mappings.lack"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FF00FF", "name": "tailback", "refs": ["mappings.tailback"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#8CB5F9", "name": "lack_branch", "refs": ["mappings.lack_branch"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FF8FCC", "name": "tailback_branch", "refs": ["mappings.tailback_branch"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#92D050", "name": "productive", "refs": ["mappings.productive"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC8E03", "name": "stopped", "refs": ["mappings.stopped"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC8E03", "name": "prepared", "refs": ["mappings.prepared"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "emergency_stop", "refs": ["mappings.emergency_stop"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#B3282E", "name": "equipment_failure", "refs": ["mappings.equipment_failure"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#E34805", "name": "external_failure", "refs": ["mappings.external_failure"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "off", "refs": ["mappings.off"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FDD868", "name": "manual", "refs": ["mappings.manual"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "held", "refs": ["mappings.held"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "idle", "refs": ["mappings.idle"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#34A853", "name": "start_up", "refs": ["mappings.start_up"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#34A853", "name": "run_down", "refs": ["mappings.run_down"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FFFFB4", "name": "clean", "refs": ["mappings.clean"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FFFF96", "name": "changeover", "refs": ["mappings.changeover"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#FFFF00", "name": "waiting", "refs": ["mappings.waiting"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#8787ED", "name": "break", "refs": ["mappings.break"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "intermediate", "refs": ["mappings.intermediate"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#C2BBBB", "name": "undefined", "refs": ["mappings.undefined"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#E54800", "name": "scrap_time", "scope": "BOTH", "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "waterfall_loss_type": "QUALITY_LOSS"}, {"color": "#98C218", "name": "quality_time", "scope": "BOTH", "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "waterfall_loss_type": "THEORETICAL_PRODUCTION_TIME"}, {"color": "#E50000", "name": "performance_loss_time", "refs": ["mappings.performance_loss_time"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "BOTH", "waterfall_loss_type": "SPEED_LOSS"}, {"color": "#B3CEFB", "name": "idle_time", "refs": ["mappings.idle_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#B3282E", "name": "unplanned_down_time", "refs": ["mappings.unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#446EA2", "name": "scheduled_down_time", "refs": ["mappings.scheduled_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC0000", "name": "system_related_unplanned_down_time", "refs": ["mappings.system_related_unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC0000", "name": "not_system_related_unplanned_down_time", "refs": ["mappings.not_system_related_unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}], "weighted_nominals": [{"name": "nominal_speed_excluding_non_operational_time", "scope": "BOTH", "excluded": ["mappings.scheduled_down_time", "mappings.idle_time"], "color": "nominal_speed_excluding_non_operational_time"}, {"name": "nominal_speed", "scope": "BOTH", "color": "nominal_speed"}]}, "config_type": "kpi_definition", "created_at": 1721719666806, "created_by": "bootstrap", "updated_at": 1721723810372, "updated_by": "bootstrap"}, {"pk": "global", "sk": "config#K#insight#T#categories", "config": {"categories": [{"classifications": [{"color": "external_failure", "name": "external_failure"}], "color": "external_failure", "name": "external_failure", "show_messages": true}, {"classifications": [{"color": "stopped", "name": "stopped"}], "color": "stopped", "name": "stopped", "show_messages": true}, {"classifications": [{"color": "start_up", "name": "start_up"}], "color": "start_up", "name": "start_up", "show_messages": false}, {"classifications": [{"color": "waiting", "name": "waiting"}], "color": "waiting", "name": "waiting", "show_messages": false}, {"classifications": [{"color": "break", "name": "break"}], "color": "break", "name": "break", "show_messages": false}, {"classifications": [{"color": "held", "name": "held"}], "color": "held", "name": "held", "show_messages": true}, {"classifications": [{"color": "idle", "name": "idle"}], "color": "idle", "name": "idle", "show_messages": true}, {"classifications": [{"color": "prepared", "name": "prepared"}], "color": "prepared", "name": "prepared", "show_messages": true}, {"classifications": [{"color": "tailback", "name": "tailback"}], "color": "tailback", "name": "tailback", "show_messages": true}, {"classifications": [{"color": "lack_branch", "name": "lack_branch"}], "color": "lack_branch", "name": "lack_branch", "show_messages": true}, {"classifications": [{"color": "run_down", "name": "run_down"}], "color": "run_down", "name": "run_down", "show_messages": false}, {"classifications": [{"color": "emergency_stop", "name": "emergency_stop"}], "color": "emergency_stop", "name": "emergency_stop", "show_messages": true}, {"classifications": [{"color": "clean", "name": "clean"}], "color": "clean", "name": "clean", "show_messages": false}, {"classifications": [{"color": "manual", "name": "manual"}], "color": "manual", "name": "manual", "show_messages": true}, {"classifications": [{"color": "equipment_failure", "name": "equipment_failure"}], "color": "equipment_failure", "name": "equipment_failure", "show_messages": true}, {"classifications": [{"color": "off", "name": "off"}], "color": "off", "name": "off", "show_messages": true}, {"classifications": [{"color": "lack", "name": "lack"}], "color": "lack", "name": "lack", "show_messages": true}, {"classifications": [{"color": "undefined", "name": "undefined"}], "color": "undefined", "name": "undefined", "show_messages": false}, {"classifications": [{"color": "tailback_branch", "name": "tailback_branch"}], "color": "tailback_branch", "name": "tailback_branch", "show_messages": true}, {"classifications": [{"color": "productive", "name": "productive", "is_productive": true}], "color": "productive", "name": "productive", "show_messages": false, "is_productive": true}, {"classifications": [{"color": "intermediate", "name": "intermediate"}], "color": "intermediate", "name": "intermediate", "show_messages": false}, {"classifications": [{"color": "unused_time", "name": "unused_time"}], "color": "unused_time", "name": "unused_time", "show_messages": false}, {"classifications": [{"color": "changeover", "name": "changeover"}], "color": "changeover", "name": "changeover", "show_messages": false}], "config_type": "categories", "kpi_model_id": "insight", "kpi_model_type": "global"}, "config_type": "categories", "created_at": 1725012790104, "created_by": "bootstrap", "updated_at": 1726051160281, "updated_by": "bootstrap"}, {"pk": "global", "sk": "config#K#insight#T#name", "config": {"config_type": "name", "kpi_model_id": "insight", "kpi_model_name": "Insight", "kpi_model_type": "global"}, "config_type": "name", "created_at": 1725012789972, "created_by": "bootstrap", "updated_at": 1726051160143, "updated_by": "bootstrap"}, {"pk": "global", "sk": "status#K#insight", "config": {"config_type": "status", "kpi_model_id": "insight", "kpi_model_status": "active", "kpi_model_type": "global"}, "config_type": "status", "created_at": 1725012789929, "created_by": "bootstrap", "updated_at": 1726051160097, "updated_by": "bootstrap"}], "din_8743": [{"pk": "global", "sk": "config#K#din_8743#T#kpi_definition", "config": {"additional_kpis": [{"kpi_result_type": "number", "name": "manufactured_output", "scope": "BOTH", "value": "scrap_units + quality_output", "variables": [{"name": "scrap_units", "ref": "units.units_defect.number"}, {"name": "quality_output", "ref": "units.units_produced.number"}]}, {"kpi_result_type": "number", "name": "scheduled_output", "scope": "BOTH", "value": "manufactured_output + performance_losses", "variables": [{"name": "manufactured_output", "ref": "additional_kpis.manufactured_output.number"}, {"name": "performance_losses", "ref": "unit_types.performance_losses.number"}]}, {"kpi_result_type": "percent", "name": "performance_losses", "scope": "BOTH", "value": "performance_losses / scheduled_output", "variables": [{"name": "performance_losses", "ref": "unit_types.performance_losses.number"}, {"name": "scheduled_output", "ref": "additional_kpis.scheduled_output.number"}]}, {"fallback_value": 1, "kpi_result_type": "percent", "name": "technical_availability", "scope": "BOTH", "value": "running_time / (operating_time - not_system_related_unplanned_down_time)", "variables": [{"name": "running_time", "ref": "waterfall_bases.running_time.percent"}, {"name": "operating_time", "ref": "waterfall_bases.operating_time.percent"}, {"name": "not_system_related_unplanned_down_time", "ref": "waterfall_losses.not_system_related_unplanned_down_time.percent"}]}], "config_type": "kpi_definition", "kpi_model_id": "din_8743", "kpi_model_type": "global", "mappings": [{"items": [{"classifications": ["equipment_failure", "prepared", "external_failure", "stopped", "intermediate", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "top_downtimes", "scope": "BOTH"}, {"items": [{"categories": ["idle_time"]}], "name": "idle_time", "scope": "BOTH"}, {"items": [{"categories": ["scheduled_down_time"]}], "name": "scheduled_down_time", "scope": "BOTH"}, {"items": [{"categories": ["productive"]}], "name": "productive", "scope": "BOTH"}, {"items": [{"categories": ["external_failure"]}], "name": "external_failure", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure"]}], "name": "internal_failure", "scope": "BOTH"}, {"items": [{"categories": ["productive"], "classifications": ["productive"]}], "name": "performance_loss_time", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure", "external_failure"]}], "name": "unplanned_down_time", "scope": "BOTH"}, {"items": [{"classifications": ["prepared", "equipment_failure"]}], "name": "system_related_unplanned_down_time", "scope": "MACHINE"}, {"items": [{"classifications": ["prepared", "equipment_failure", "lack", "tailback", "lack_branch", "tailback_branch", "external_failure"]}], "name": "system_related_unplanned_down_time", "scope": "LINE"}, {"items": [{"classifications": ["external_failure", "stopped", "lack", "tailback", "lack_branch", "tailback_branch", "emergency_stop", "off", "manual", "held", "idle"]}], "name": "not_system_related_unplanned_down_time", "scope": "MACHINE"}, {"items": [{"classifications": ["held", "emergency_stop", "idle", "stopped", "off", "manual"]}], "name": "not_system_related_unplanned_down_time", "scope": "LINE"}, {"items": [{"categories": ["productive"]}], "name": "mean_time_mtbfs_time_dividend", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure"]}], "name": "mean_time_mtbfs_count_divisor", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure"]}], "name": "mean_time_mttrs_time_dividend", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure"]}], "name": "mean_time_mttrs_count_divisor", "scope": "BOTH"}, {"items": [{"categories": ["productive"]}], "name": "mean_time_mtbf_time_dividend", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure", "external_failure"]}], "name": "mean_time_mtbf_count_divisor", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure", "external_failure"]}], "name": "mean_time_mttr_time_dividend", "scope": "BOTH"}, {"items": [{"categories": ["internal_failure", "external_failure"]}], "name": "mean_time_mttr_count_divisor", "scope": "BOTH"}, {"items": [{"classifications": ["clean", "run_down", "start_up", "changeover", "waiting", "break"]}], "name": "planned_downtime", "scope": "BOTH"}], "mean_times": [{"count_divisor": "mappings.mean_time_mtbfs_count_divisor", "fallback_kpi": "overall_duration", "name": "mtbfs", "scope": "BOTH", "time_dividend": "mappings.mean_time_mtbfs_time_dividend"}, {"count_divisor": "mappings.mean_time_mttrs_count_divisor", "name": "mttrs", "scope": "BOTH", "time_dividend": "mappings.mean_time_mttrs_time_dividend"}, {"count_divisor": "mappings.mean_time_mtbf_count_divisor", "fallback_kpi": "overall_duration", "name": "mtbf", "scope": "BOTH", "time_dividend": "mappings.mean_time_mtbf_time_dividend"}, {"count_divisor": "mappings.mean_time_mttr_count_divisor", "name": "mttr", "scope": "BOTH", "time_dividend": "mappings.mean_time_mttr_time_dividend"}], "quotients": [{"color": "#A9CAE0", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "name": "quality", "scope": "BOTH"}, {"color": "#052A68", "dividend": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "performance", "scope": "BOTH"}, {"color": "#059FDC", "dividend": ["waterfall_bases.operating_time.percent"], "divisor": ["waterfall_bases.machine_working_time.percent"], "name": "availability", "scope": "BOTH"}, {"color": "#9E0008", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_bases.machine_working_time.percent"], "name": "oee", "scope": "BOTH"}, {"dividend": ["mean_times.mtbfs.duration"], "divisor": ["mean_times.mtbfs.duration", "mean_times.mttrs.duration"], "name": "runtime_factor", "scope": "BOTH"}, {"color": "#059FDC", "dividend": ["waterfall_bases.running_time.percent"], "divisor": ["waterfall_bases.theoretical_available_time.percent"], "name": "technical_oee", "scope": "BOTH"}, {"color": "#6AB0D8", "dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_bases.operating_time.percent"], "name": "efficiency", "scope": "BOTH"}, {"color": "#1EA1EC", "dividend": ["units.units_produced.number"], "divisor": ["unit_types.system_related_scheduled_output.number"], "name": "technical_efficiency", "scope": "BOTH"}, {"dividend": ["waterfall_losses.quality_time.percent"], "divisor": ["waterfall_losses.scrap_time.percent", "waterfall_losses.quality_time.percent"], "name": "quality_factor", "scope": "LINE"}, {"dividend": ["waterfall_losses.scrap_time.percent"], "divisor": ["waterfall_losses.quality_time.percent", "waterfall_losses.scrap_time.percent"], "name": "scrap_rate", "scope": "BOTH"}], "speeds": [{"name": "average_speed", "source": "average_speed"}], "units": [{"name": "units_defect", "source": "units_defect"}, {"name": "units_produced", "source": "units_produced"}], "unit_types": [{"name": "theoretical_production_count", "refs": ["waterfall_losses.productive.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "LINE"}, {"name": "performance_losses", "refs": ["waterfall_losses.performance_loss_time.duration", "waterfall_losses.unplanned_down_time.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "BOTH"}, {"name": "system_related_scheduled_output", "refs": ["waterfall_losses.quality_time.duration", "waterfall_losses.scrap_time.duration", "waterfall_losses.performance_loss_time.duration", "waterfall_losses.system_related_unplanned_down_time.duration"], "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "scope": "BOTH"}], "waterfall_bases": [{"addends": [1], "color": "#4F5966", "name": "theoretical_available_time", "scope": "BOTH"}, {"color": "#6A7788", "name": "machine_working_time", "scope": "BOTH", "subtract": ["waterfall_losses.idle_time.percent"]}, {"color": "#8495AA", "name": "operating_time", "scope": "BOTH", "subtract": ["waterfall_losses.scheduled_down_time.percent"]}, {"color": "#92D050", "name": "running_time", "scope": "BOTH", "subtract": ["waterfall_losses.unplanned_down_time.percent"]}], "waterfall_losses": [{"color": "#B3CEFB", "name": "idle_time", "refs": ["mappings.idle_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#446EA2", "name": "scheduled_down_time", "refs": ["mappings.scheduled_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#92D050", "name": "productive", "refs": ["mappings.productive"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#E34805", "name": "external_failure", "refs": ["mappings.external_failure"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#B3282E", "name": "internal_failure", "refs": ["mappings.internal_failure"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#E50000", "name": "performance_loss_time", "refs": ["mappings.performance_loss_time"], "scope": "BOTH", "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "waterfall_loss_type": "SPEED_LOSS"}, {"color": "#E54800", "name": "scrap_time", "scope": "BOTH", "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "waterfall_loss_type": "QUALITY_LOSS"}, {"color": "#98C218", "name": "quality_time", "scope": "BOTH", "nominal_speed_ref": ["weighted_nominals.nominal_speed_excluding_non_operational_time.number"], "waterfall_loss_type": "THEORETICAL_PRODUCTION_TIME"}, {"color": "#B3282E", "name": "unplanned_down_time", "refs": ["mappings.unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC0000", "name": "system_related_unplanned_down_time", "refs": ["mappings.system_related_unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}, {"color": "#BC0000", "name": "not_system_related_unplanned_down_time", "refs": ["mappings.not_system_related_unplanned_down_time"], "scope": "BOTH", "waterfall_loss_type": "LOSS"}], "weighted_nominals": [{"name": "nominal_speed_excluding_non_operational_time", "scope": "BOTH", "excluded": ["mappings.scheduled_down_time", "mappings.idle_time"], "color": "nominal_speed_excluding_non_operational_time"}, {"name": "nominal_speed", "scope": "BOTH", "color": "nominal_speed"}]}, "config_type": "kpi_definition", "created_at": 1721719670880, "created_by": "bootstrap", "updated_at": 1721723814345, "updated_by": "bootstrap"}, {"pk": "global", "sk": "config#K#din_8743#T#categories", "config": {"categories": [{"classifications": [{"color": "external_failure", "name": "external_failure"}, {"color": "stopped", "name": "stopped"}, {"color": "lack", "name": "lack"}, {"color": "lack_branch", "name": "lack_branch"}, {"color": "tailback", "name": "tailback"}, {"color": "tailback_branch", "name": "tailback_branch"}, {"color": "emergency_stop", "name": "emergency_stop"}, {"color": "off", "name": "off"}, {"color": "manual", "name": "manual"}, {"color": "held", "name": "held"}, {"color": "idle", "name": "idle"}, {"color": "intermediate", "name": "intermediate"}], "color": "external_failure", "name": "external_failure", "show_messages": true}, {"classifications": [{"color": "undefined", "name": "undefined"}, {"color": "unused_time", "name": "unused_time"}], "color": "idle_time", "name": "idle_time", "show_messages": false}, {"classifications": [{"color": "prepared", "name": "prepared"}, {"color": "equipment_failure", "name": "equipment_failure"}], "color": "internal_failure", "name": "internal_failure", "show_messages": true}, {"classifications": [{"color": "clean", "name": "clean"}, {"color": "run_down", "name": "run_down"}, {"color": "start_up", "name": "start_up"}, {"color": "changeover", "name": "changeover"}, {"color": "waiting", "name": "waiting"}, {"color": "break", "name": "break"}], "color": "scheduled_down_time", "name": "scheduled_down_time", "show_messages": false}, {"classifications": [{"color": "productive", "name": "productive", "is_productive": true}], "color": "productive", "name": "productive", "show_messages": false, "is_productive": true}], "config_type": "categories", "kpi_model_id": "din_8743", "kpi_model_type": "global"}, "config_type": "categories", "created_at": 1725012797313, "created_by": "bootstrap", "updated_at": 1726051167634, "updated_by": "bootstrap"}, {"pk": "global", "sk": "config#K#din_8743#T#name", "config": {"config_type": "name", "kpi_model_id": "din_8743", "kpi_model_name": "DIN_8743", "kpi_model_type": "global"}, "config_type": "name", "created_at": 1725012797167, "created_by": "bootstrap", "updated_at": 1726051167488, "updated_by": "bootstrap"}, {"pk": "global", "sk": "status#K#din_8743", "config": {"config_type": "status", "kpi_model_id": "din_8743", "kpi_model_status": "active", "kpi_model_type": "global"}, "config_type": "status", "created_at": 1725012797124, "created_by": "bootstrap", "updated_at": 1726051167447, "updated_by": "bootstrap"}]}