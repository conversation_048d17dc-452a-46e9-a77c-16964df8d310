{"account_id": "readykit-replay", "equipment_id": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "kpi_model": "din_8743", "downtimes": [{"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 266507, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 266507, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "prepared", "category": "internal_failure", "start": *************, "end": *************, "duration": 6018, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_prepared", "messageType": "artificial"}, "raw_values": {"aggregated_state": 4, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6018, "classification": "prepared", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_prepared", "messageType": "artificial"}, "raw_values": {"aggregated_state": 4, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 537296, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 537296, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack_branch", "category": "external_failure", "start": *************, "end": *************, "duration": 107177, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 101172, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6005, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_lack_branch", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 434947, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 434947, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack_branch", "category": "external_failure", "start": *************, "end": *************, "duration": 50076, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 44074, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6002, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_lack_branch", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 912072, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 912072, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack_branch", "category": "external_failure", "start": *************, "end": *************, "duration": 124171, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 118146, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6025, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_lack_branch", "messageType": "artificial"}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 144291, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 144291, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack", "category": "external_failure", "start": *************, "end": *************, "duration": 106121, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 106120, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_lack", "messageType": "artificial"}, "raw_values": {"aggregated_state": 8, "mode": 4, "program": 1, "state": -1}}]}, {"classification": "run_down", "category": "scheduled_down_time", "start": *************, "end": *************, "duration": 102122, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 8, "mode": 4, "program": 4, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 96115, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 8, "mode": 4, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6007, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 8, "mode": 4, "program": 4, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 18049, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 18049, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}}]}, {"classification": "run_down", "category": "scheduled_down_time", "start": *************, "end": *************, "duration": 35086, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3007, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 16050, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 16029, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 4, "program": 4, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 28091, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 28091, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 4, "program": 4, "state": -1}}]}, {"classification": "run_down", "category": "scheduled_down_time", "start": *************, "end": *************, "duration": 116213, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3022, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 128, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 8008, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_run_down", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 8012, "message": {"configId": "5703", "messageNr": "5018", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of caps at screw capperLeft chute"}, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 73151, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 2, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3997, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 4, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 20023, "classification": "run_down", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 2, "program": 4, "state": -1}}]}, {"classification": "undefined", "category": "idle_time", "start": *************, "end": *************, "duration": 671818, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 2, "program": 0}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 2002, "classification": "undefined", "category": "idle_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 2, "program": 0}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 5009, "classification": "undefined", "category": "idle_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 1, "program": 0}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 66089, "classification": "undefined", "category": "idle_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 2, "program": 0}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 598717, "classification": "undefined", "category": "idle_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 1, "program": 0}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1, "classification": "undefined", "category": "idle_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_undefined", "messageType": "artificial"}, "raw_values": {"state": -1, "aggregated_state": 1024, "mode": 1, "program": 0}}]}, {"classification": "clean", "category": "scheduled_down_time", "start": *************, "end": *************, "duration": 3723056, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 218308, "classification": "clean", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 572938, "classification": "clean", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_clean", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 8, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 5017, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 179180, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1385416, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 10021, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 4016, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 4, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1000, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 8015, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 13019, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 28046, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 1, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1295065, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1001, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 2012, "classification": "changeover", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_changeover", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 16, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1, "classification": "start_up", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 2, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 3000, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3000, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}}]}, {"classification": "start_up", "category": "scheduled_down_time", "start": *************, "end": *************, "duration": 34068, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 8, "program": 2, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 28064, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "classification": "start_up", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16384, "mode": 8, "program": 2, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6004, "classification": "start_up", "category": "scheduled_down_time", "updated_at": *************, "manual_attributes": {}, "message": {"configId": "AM_start_up", "messageType": "artificial"}, "raw_values": {"aggregated_state": 16384, "mode": 8, "program": 2, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 416036, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 24047, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 2, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 391989, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "tailback", "category": "external_failure", "start": *************, "end": *************, "duration": 719878, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 713869, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6009, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 212481, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 212481, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack", "category": "external_failure", "start": *************, "end": *************, "duration": 93148, "message": {"configId": "4150", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Block synchronisation"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 87137, "message": {"configId": "4150", "messageNr": "364", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container stop has been closed by:Block synchronisation"}, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6011, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 1141596, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1141596, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "tailback", "category": "external_failure", "start": *************, "end": *************, "duration": 153190, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 146178, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 7012, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 410976, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 410976, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "equipment_failure", "category": "internal_failure", "start": *************, "end": *************, "duration": 37088, "message": {"configId": "10609", "messageNr": "1372", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Broken bottle detection by short-circuit probe - filling unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 34092, "message": {"configId": "10609", "messageNr": "1372", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Broken bottle detection by short-circuit probe - filling unit"}, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 2996, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 27079, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 27079, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "equipment_failure", "category": "internal_failure", "start": *************, "end": *************, "duration": 130179, "message": {"configId": "9246", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Checkmat fill level inspection"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": 1652*********, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 127179, "message": {"configId": "9246", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Checkmat fill level inspection"}, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3000, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 152362, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 152362, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "tailback", "category": "external_failure", "start": *************, "end": *************, "duration": 348469, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 301400, "message": {"configId": "4184", "messageNr": "368", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container back-up at the discharge"}, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 41058, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6011, "classification": "tailback", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 5021, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 5021, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "equipment_failure", "category": "internal_failure", "start": *************, "end": *************, "duration": 2003, "message": {"configId": "4803", "messageNr": "1713", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container present monitoring system tripped:Filler"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 2003, "message": {"configId": "4803", "messageNr": "1713", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container present monitoring system tripped:Filler"}, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 1999, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1999, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "equipment_failure", "category": "internal_failure", "start": *************, "end": *************, "duration": 32097, "message": {"configId": "4803", "messageNr": "1713", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container present monitoring system tripped:Filler"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 9061, "message": {"configId": "4803", "messageNr": "1713", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Container present monitoring system tripped:Filler"}, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 23036, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 419967, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 419967, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack", "category": "external_failure", "start": *************, "end": *************, "duration": 68108, "message": {"configId": "5232", "messageNr": "3911", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Infeed monitoring unit tripped:Filler"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 62103, "message": {"configId": "5232", "messageNr": "3911", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Infeed monitoring unit tripped:Filler"}, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6005, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 248567, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 248567, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack_branch", "category": "external_failure", "start": *************, "end": *************, "duration": 53096, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 47088, "message": {"configId": "9260", "messageNr": "9718", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "External line / machine not ready:Cap feed unit"}, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6008, "classification": "lack_branch", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 32, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 406980, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 406980, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack", "category": "external_failure", "start": *************, "end": *************, "duration": 36066, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 8034, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 22026, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 6006, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 12031, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 12031, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "lack", "category": "external_failure", "start": *************, "end": *************, "duration": 3006, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3006, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "lack", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 8, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 1025, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 1025, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "held", "category": "external_failure", "start": *************, "end": *************, "duration": 33048, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 16384, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 33048, "message": {"configId": "4182", "messageNr": "366", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Lack of containers in infeed section"}, "classification": "held", "category": "external_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 16384, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 508183, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 508183, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "equipment_failure", "category": "internal_failure", "start": *************, "end": *************, "duration": 29065, "message": {"configId": "10609", "messageNr": "1372", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Broken bottle detection by short-circuit probe - filling unit"}, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 26051, "message": {"configId": "10609", "messageNr": "1372", "equipmentId": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "messageClass": "Hint", "messageType": "dmm", "lastChange": *************, "messageText": "Broken bottle detection by short-circuit probe - filling unit"}, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}, {"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 3014, "classification": "equipment_failure", "category": "internal_failure", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 1024, "mode": 8, "program": 1, "state": -1}}]}, {"classification": "productive", "category": "productive", "start": *************, "end": *************, "duration": 2122923, "id": "readykit-replay_din_8743_5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "last_update_at": *************, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}, "children": [{"kpi_model": "din_8743", "account": "readykit-replay", "equipment": "5fcd1cee-73b9-4fd2-804a-1a04aa27e48a", "start": *************, "end": *************, "duration": 2122923, "classification": "productive", "category": "productive", "updated_at": *************, "manual_attributes": {}, "raw_values": {"aggregated_state": 128, "mode": 8, "program": 1, "state": -1}}]}]}