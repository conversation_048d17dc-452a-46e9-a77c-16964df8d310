import json

from fastapi.testclient import TestClient
from lib_kpi_config_client.models.api_models import ApiKpiModelTimeValidity
from lib_kpi_config_client.models.enums import KpiModelType
from machine_data_query.models.speeds import DESIGN_SPEEDS, SET_SPEEDS, SPEEDS
from performance_analytics.models.shared_models import LineSettings, MachineSetting, Settings
import pytest


@pytest.fixture
def mock_kpi_model_assigned_in_timerange(mocker):
    mocker.patch(
        "lib_kpi_config_client.api.api_client.KpiConfigApiClient.get_kpi_models_assigned_in_timerange",
        return_value=[
            ApiKpiModelTimeValidity(
                start=*************,
                kpi_model_id="din_8743_v2",
                kpi_model_name="din_8743_v2",
                kpi_model_type=KpiModelType.GLOBAL,
            )
        ],
    )


@pytest.fixture
def mock_downtimes_and_speeds(mocker):
    mocker.patch(
        "lib_dtm_client.clients.query_handler.QueryHandler.get_downtimes_with_speeds_for_machine",
        return_value={
            "time_from": *************,
            "time_to": *************,
            "account_id": "readykit-replay",
            "equipment_id": "c7ae813a-e1ed-4cb9-9052-b02424f8f612",
            "downtimes": [],
            "kpi_model": "din_8743_v2",
            "units_produced": 123456,
            "units_defect": 3,
            "units_total": 123468,
            SPEEDS: [],
            SET_SPEEDS: [],
            DESIGN_SPEEDS: [],
        },
    )


@pytest.fixture
def mock_line_settings(mocker):
    mocker.patch(
        "common.line_settings.get_line_settings",
        return_value=LineSettings(
            account="readykit-replay",
            created_at=*************,
            updated_at=*************,
            line_id="45bad699-99b1-44ee-8208-e3b2275f5f78",
            settings=Settings(
                minor_stop_config=0,
                nominal_speed=25000,
                line_status_config="websocket",
                machine_settings={
                    "94ca1ada-f71d-4c31-86e7-0ed3e798b270": MachineSetting(nominal_speed=45000),
                },
            ),
        ),
    )


@pytest.fixture
def mock_kpi_result(mocker):
    mocker.patch(
        "common.kpi_client.KpiClient.calculate_kpis",
        return_value={
            "kpi_result": {
                "waterfallLosses": [
                    {"name": "idle_time", "percent": 0.0, "duration": 0.0, "number": 0.0}
                ],
                "waterfallBases": [
                    {"name": "theoretical_available_time", "percent": 1.0, "duration": ********.0}
                ],
                "meanTimes": [{"name": "mtbfs", "duration": ********.0}],
                "quotients": [{"name": "quality", "percent": 0.981073}],
                "speeds": [{"name": "average_speed", "number": 24608.0}],
                "units": [
                    {"name": "units_defect", "number": 3554.0},
                    {"name": "units_produced", "number": 184220.0},
                    {"name": "units_total", "number": 187774.0},
                ],
                "unitTypes": [
                    {"name": "performance_losses", "number": 61969.0},
                    {"name": "system_related_scheduled_output", "number": 207254.0},
                ],
            },
            "reduced_downtimes": {
                "duration": {
                    "categories": {
                        "productive": 16580324,
                    },
                    "classifications": {
                        "productive": 16580324,
                    },
                },
                "overallDuration": ********,
            },
        },
    )


@pytest.mark.usefixtures(
    "mock_kpi_model_assigned_in_timerange",
    "mock_downtimes_and_speeds",
    "mock_line_settings",
    "mock_kpi_result",
)
def test_machine_report_public_api():
    from api.public.lambda_function import app

    client = TestClient(app)

    base_path = "v1/performance-public-api-mvp/machine-reports"
    line_id = "45bad699-99b1-44ee-8208-e3b2275f5f78"
    equipment_id = "c7ae813a-e1ed-4cb9-9052-b02424f8f612"
    account = "readykit-replay"
    kpi_model_id = "din_8743_v2"
    time_from = *************
    time_to = *************
    line_kpi = 1

    url = f"{base_path}/{line_id}/{equipment_id}?customer={account}&kpi_model_id={kpi_model_id}&{time_from=}&{time_to=}&{line_kpi=}"

    machine_report_response = client.get(url)

    assert machine_report_response.status_code == 200
    result = json.loads(machine_report_response.content)
    assert all(
        key in result and result[key] is not None
        for key in [
            "account",
            "equipmentId",
            "kpiModelId",
            "timeFrom",
            "timeTo",
            "unitsProduced",
            "unitsDefect",
            "kpiResult",
            "reducedMachineState",
        ]
    )
