import json
import logging

from fastapi.testclient import TestClient
from lib_cloud_sdk.util.file_io import read_json_file
from lib_dtm_common.models.api_models import GetDowntimeResponse, ResponseDowntime

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(logging.DEBUG)

PATH = "v1/performance-analytics/trend-analysis/v2"


def test_trend_analysis_endpoint_din_8743(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Get Trend Analysis for 1 day (3 shifts, Monday 05.24.2021)
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_din_8743.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_trend_analysis_downtimes_din_8743.json"
    )

    performance_data_item_product_type_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_product_type.json"
    )
    performance_data_item_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_speeds.json"
    )
    performance_data_item_design_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_design_speeds.json"
    )
    performance_data_item_set_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_set_speeds.json"
    )

    performance_data_item_product_type_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_product_type.json"
    )
    performance_data_item_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_speeds.json"
    )
    performance_data_item_design_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_design_speeds.json"
    )
    performance_data_item_set_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_set_speeds.json"
    )

    performance_data_item_product_type_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_product_type.json"
    )
    performance_data_item_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_speeds.json"
    )
    performance_data_item_design_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_design_speeds.json"
    )
    performance_data_item_set_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_set_speeds.json"
    )

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")

    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_1)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_1)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_2)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_2)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_3)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_3)

    response_downtimes = [
        ResponseDowntime(
            classification=downtime["classification"],
            category=downtime["category"],
            start=downtime["start"],
            end=downtime["end"],
            duration=downtime["duration"],
            lastUpdateAt=downtime["lastUpdateAt"],
        )
        for downtime in downtime_items["downtimes"]
    ]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["accountId"],
        equipment_id=downtime_items["equipmentId"],
        kpi_model=downtime_items["kpiModel"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(
        url,
        json=body,
    )
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_din_8743_line(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Get Trend Analysis for 1 day (3 shifts, Monday 05.24.2021)
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "line_kpi": "1",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_din_8743_line.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_trend_analysis_downtimes_din_8743.json"
    )

    performance_data_item_product_type_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_product_type.json"
    )
    performance_data_item_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_speeds.json"
    )
    performance_data_item_design_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_design_speeds.json"
    )
    performance_data_item_set_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_set_speeds.json"
    )

    performance_data_item_product_type_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_product_type.json"
    )
    performance_data_item_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_speeds.json"
    )
    performance_data_item_design_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_design_speeds.json"
    )
    performance_data_item_set_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_set_speeds.json"
    )

    performance_data_item_product_type_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_product_type.json"
    )
    performance_data_item_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_speeds.json"
    )
    performance_data_item_design_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_design_speeds.json"
    )
    performance_data_item_set_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_set_speeds.json"
    )
    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")

    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_1)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_1)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_2)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_2)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_3)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_3)
    response_downtimes = [
        ResponseDowntime(
            classification=downtime["classification"],
            category=downtime["category"],
            start=downtime["start"],
            end=downtime["end"],
            duration=downtime["duration"],
            lastUpdateAt=downtime["lastUpdateAt"],
        )
        for downtime in downtime_items["downtimes"]
    ]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["accountId"],
        equipment_id=downtime_items["equipmentId"],
        kpi_model=downtime_items["kpiModel"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(
        url,
        json=body,
    )
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_insight(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Get Trend Analysis for 1 day (3 shifts, Monday 05.24.2021)
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "insight",
    }

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_insight.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_item_line_assignment["config"][
        "kpi_model_id"
    ] = "insight"  # set KPI model to insight
    performance_kpi_model_config_status_items = performance_kpi_model_data.get("insight")

    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_trend_analysis_downtimes_insight.json"
    )

    performance_data_item_product_type_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_product_type.json"
    )
    performance_data_item_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_speeds.json"
    )
    performance_data_item_design_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_design_speeds.json"
    )
    performance_data_item_set_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_set_speeds.json"
    )

    performance_data_item_product_type_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_product_type.json"
    )
    performance_data_item_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_speeds.json"
    )
    performance_data_item_design_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_design_speeds.json"
    )
    performance_data_item_set_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_set_speeds.json"
    )

    performance_data_item_product_type_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_product_type.json"
    )
    performance_data_item_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_speeds.json"
    )
    performance_data_item_design_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_design_speeds.json"
    )
    performance_data_item_set_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_set_speeds.json"
    )
    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")

    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:

        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_1)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_1)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_2)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_2)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_3)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_3)
    response_downtimes = [
        ResponseDowntime(
            classification=downtime["classification"],
            category=downtime["category"],
            start=downtime["start"],
            end=downtime["end"],
            duration=downtime["duration"],
            lastUpdateAt=downtime["lastUpdateAt"],
        )
        for downtime in downtime_items["downtimes"]
    ]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["accountId"],
        equipment_id=downtime_items["equipmentId"],
        kpi_model=downtime_items["kpiModel"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(url, json=body)
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_insight_line(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Get Trend Analysis for 1 day (3 shifts, Monday 05.24.2021)
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "line_kpi": "1",
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "insight",
    }

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_insight_line.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get("insight")

    performance_kpi_model_item_line_assignment["config"][
        "kpi_model_id"
    ] = "insight"  # set KPI model to insight
    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_trend_analysis_downtimes_insight.json"
    )

    performance_data_item_product_type_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_product_type.json"
    )
    performance_data_item_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_speeds.json"
    )
    performance_data_item_design_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_design_speeds.json"
    )
    performance_data_item_set_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_set_speeds.json"
    )

    performance_data_item_product_type_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_product_type.json"
    )
    performance_data_item_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_speeds.json"
    )
    performance_data_item_design_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_design_speeds.json"
    )
    performance_data_item_set_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_set_speeds.json"
    )

    performance_data_item_product_type_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_product_type.json"
    )
    performance_data_item_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_speeds.json"
    )
    performance_data_item_design_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_design_speeds.json"
    )
    performance_data_item_set_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_set_speeds.json"
    )
    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")

    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_1)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_1)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_2)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_2)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_3)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_3)

    response_downtimes = [
        ResponseDowntime(
            classification=downtime["classification"],
            category=downtime["category"],
            start=downtime["start"],
            end=downtime["end"],
            duration=downtime["duration"],
            lastUpdateAt=downtime["lastUpdateAt"],
        )
        for downtime in downtime_items["downtimes"]
    ]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["accountId"],
        equipment_id=downtime_items["equipmentId"],
        kpi_model=downtime_items["kpiModel"],
        downtimes=response_downtimes,
    )
    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(url, json=body)
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_with_minor_stops(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    DIN (does not really matter which KPI model) + Minor stops case
    Get Trend Analysis for 1 day (3 shifts, Monday 05.24.2021)
    """
    # Arrange
    from lambda_function import app

    # Prepare URL
    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }

    # Read input data
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_with_minor_stops.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_customer_settings_item["settings"][
        "minor_stop_config"
    ] = 5  # set minor stops to 0 minutes

    downtime_items = read_json_file(
        "test/integration_test/res/input_data/performance_trend_analysis_downtimes_din_8743.json"
    )

    performance_data_item_product_type_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_product_type.json"
    )
    performance_data_item_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_speeds.json"
    )
    performance_data_item_design_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_design_speeds.json"
    )
    performance_data_item_set_speeds_1 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_1_set_speeds.json"
    )

    performance_data_item_product_type_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_product_type.json"
    )
    performance_data_item_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_speeds.json"
    )
    performance_data_item_design_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_design_speeds.json"
    )
    performance_data_item_set_speeds_2 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_2_set_speeds.json"
    )

    performance_data_item_product_type_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_product_type.json"
    )
    performance_data_item_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_speeds.json"
    )
    performance_data_item_design_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_design_speeds.json"
    )
    performance_data_item_set_speeds_3 = read_json_file(
        "test/integration_test/res/input_data/performance_data_trend_analysis_3_set_speeds.json"
    )
    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_data = dynamo_db_tables_mock.get("performance_data")

    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:

        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_1)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_1
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_1)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_2)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_2
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_2)

    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_product_type_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_speeds_3)
    performance_data.put_item(
        TableName="performance-data", Item=performance_data_item_design_speeds_3
    )
    performance_data.put_item(TableName="performance-data", Item=performance_data_item_set_speeds_3)

    response_downtimes = [
        ResponseDowntime(
            classification=downtime["classification"],
            category=downtime["category"],
            start=downtime["start"],
            end=downtime["end"],
            duration=downtime["duration"],
            lastUpdateAt=downtime["lastUpdateAt"],
        )
        for downtime in downtime_items["downtimes"]
    ]

    downtime_client_mock.return_value = GetDowntimeResponse(
        account_id=downtime_items["accountId"],
        equipment_id=downtime_items["equipmentId"],
        kpi_model=downtime_items["kpiModel"],
        downtimes=response_downtimes,
    )

    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(url, json=body)
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_handles_incomplete_request(
    aws_request_mock, dynamo_db_tables_mock
):
    """
    Test that if a mandatory key is missing in the body, the trend-analysis endpoint handles that correctly.
    """
    # Arrange
    from lambda_function import app

    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        # removed the machine_ids mandatory key
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }
    expected_result = {
        "detail": [
            {
                "input": {
                    "language": "en",
                    "line": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                    "time": {
                        "end_time": "2021-05-24T23:59:00.000Z",
                        "start_time": "2021-05-24T00:00:00.000Z",
                    },
                    "timezone": "America/Asuncion",
                    "kpi_model_id": "din_8743",
                },
                "loc": ["body", "machines"],
                "msg": "Field required",
                "type": "missing",
            },
            {
                "input": {
                    "language": "en",
                    "line": "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22",
                    "time": {
                        "end_time": "2021-05-24T23:59:00.000Z",
                        "start_time": "2021-05-24T00:00:00.000Z",
                    },
                    "timezone": "America/Asuncion",
                    "kpi_model_id": "din_8743",
                },
                "loc": ["body", "machines"],
                "msg": "Field required",
                "type": "missing",
            },
        ]
    }

    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(url, json=body)
    actual_result = json.loads(trend_analysis_response.text)

    # Asserts
    assert actual_result == expected_result


def test_trend_analysis_endpoint_handles_no_data(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Test that if a mandatory key is missing in the body, the trend-analysis endpoint handles that correctly.
    """
    # Arrange
    from lambda_function import app

    url = PATH
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }
    expected_result = read_json_file(
        "test/integration_test/res/expected_results/expected_result_trend_analysis_no_data.json"
    )
    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_customer_settings_item["settings"][
        "minor_stop_config"
    ] = 5  # set minor stops to 0 minutes

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:

        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    # Act
    client = TestClient(app)
    trend_analysis_response = client.post(url, json=body)
    actual_result = json.loads(trend_analysis_response.text)

    # Assert
    assert actual_result == expected_result


def test_trend_analysis_endpoint_compressed_response(
    dynamo_db_tables_mock,
    downtime_client_mock,
):
    """
    Test that the trend-analysis endpoint returns a compressed response.
    """
    # Arrange
    from lambda_function import app

    url = "v1/performance-analytics/trend-analysis/v2"
    line_id = "5b4a4db0-92e8-4cc0-aaf9-ef124b6b3c22"
    body = {
        "line": line_id,
        "machines": ["5fcd1cee-73b9-4fd2-804a-1a04aa27e48a"],
        "time": {
            "start_time": "2021-05-24T00:00:00.000Z",
            "end_time": "2021-05-24T23:59:00.000Z",
        },
        "language": "en",
        "timezone": "America/Asuncion",
        "kpi_model_id": "din_8743",
    }

    performance_customer_settings_item = read_json_file(
        "test/integration_test/res/input_data/performance_customer_settings.json"
    ).get(line_id)

    # "Store" data into respective DynamoDB tables
    rk_performance_customer_settings = dynamo_db_tables_mock.get("rk_performance_customer_settings")
    rk_performance_customer_settings.put_item(
        TableName="rk-performance-customer-settings",
        Item=performance_customer_settings_item,
    )
    performance_kpi_model_data = read_json_file(
        "test/integration_test/res/input_data/performance_kpi_model.json"
    )
    performance_kpi_model_item_line_assignment = performance_kpi_model_data.get(line_id)
    performance_kpi_model_config_status_items = performance_kpi_model_data.get(
        "din_8743"
    ) + performance_kpi_model_data.get("insight")
    performance_kpi_model = dynamo_db_tables_mock.get("performance_kpi_model")
    performance_kpi_model.put_item(
        TableName="performance-kpi-model", Item=performance_kpi_model_item_line_assignment
    )
    for performance_kpi_model_item_kpi_model in performance_kpi_model_config_status_items:
        performance_kpi_model.put_item(
            TableName="performance-kpi-model", Item=performance_kpi_model_item_kpi_model
        )
    # Act
    client = TestClient(app)
    response = client.post(url, json=body)

    # Assert
    assert response.status_code == 200
    assert response.headers["Content-Encoding"] == "gzip"
