line-length = 119
show-fixes = true

[format]
quote-style = "double"
indent-style = "space"
docstring-code-format = true

[lint]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
select = [
  "E",      # pycodestyle errors
  "W",      # pycodestyle warnings
  "D",      # pydocstyle
  "F",      # Pyflakes
  "UP",     # pyupgrade
  "S",      # flake8-bandit
  "B",      # flake8-bugbear
  "A",      # flake8-builtins
  "COM",    # flake8-commas
  "C4",     # flake8-comprehensions
  "T10",    # flake8-debugger
  "ISC",    # flake8-implicit-string-concat
  "G",      # flake8-logging-format
  "PIE",    # flake8-pie
  "T20",    # flake8-print
  "Q",      # flake8-quotes
  "SIM",    # flake8-simplify
  "ARG",    # flake8-unused-arguments
  "I",      # isort
  "N",      # PEP 8
  "PERF",   # <PERSON><PERSON>lint
  "PGH",    # pygrep-hooks
  "PL",     # Pylint
  "PT",     # pytest
  "RUF",    # Ruff
  "TRY203", # useless-try-except
  "C90",    # mccabe
]

ignore = [
  "B008",    # No function call in argument defaults
  "D10",     # Missing docstring in public *
  "W191",    # indentation contains tabs
  "E111",    # indentation is not a multiple of four
  "E114",    # indentation is not a multiple of four (comment)
  "E117",    # over-indented
  "E203",    # whitespace before ':'
  "E501",    # line too long
  "D206",    # Docstring should be indented with spaces, not tabs
  "D300",    # Use """triple double quotes"""
  "ISC001",  # single-line-implicit-string-concatenation
  "Q000",    # Remove bad quotes from inline string
  "Q001",    # Remove bad quotes from multiline string
  "Q002",    # Remove bad quotes from docstring
  "Q003",    # Avoidable escaped quotes
  "COM812",  # missing trailing comma
  "COM819",  # prohibited trailing comma
  "UP004",   # useless-object-inheritance
  "PLR09",   # too-many-return-statements, too-many-branches, too-many-arguments, too-many-statements
  "PLR1702", # too-many-nested-blocks
  "PLR2004", # magic-value-comparison
  "PLW0120", # useless-else-on-loop
  "PLW0406", # import-self
  "PLW0603", # global-statement
  "PT011",   # Too broad exception
  "S603",    # `subprocess` call: check for execution of untrusted input
  "S607",    # Starting a process with a partial executable path
]

[lint.flake8-pytest-style]
mark-parentheses = false
fixture-parentheses = false
raises-require-match-for = ["*"]
parametrize-names-type = "tuple"

[lint.flake8-quotes]
inline-quotes = "double"
multiline-quotes = "double"
docstring-quotes = "double"

[lint.isort]
detect-same-package = false       # src is configured
force-sort-within-sections = true

[lint.mccabe]
max-complexity = 14

[lint.per-file-ignores]
# library projects
"../test*/**/*.py" = [
  "ARG001", # Unused function argument
  "S101",   # Use of assert detected
  "S105",   # Use of hardcoded credentials detected
  "D",      # pydocstyle
]
# application projects
"../**/test*/**/*.py" = [
  "ARG001", # Unused function argument
  "S101",   # Use of assert detected
  "S105",   # Use of hardcoded credentials detected
  "D",      # pydocstyle
]
"../cdk_test*/**/*.py" = [
  "ARG001", # Unused function argument
  "S101",   # Use of assert detected
  "S105",   # Use of hardcoded credentials detected
  "D",      # pydocstyle
]

[lint.pycodestyle]
max-doc-length = 119

[lint.pydocstyle]
convention = "google"
