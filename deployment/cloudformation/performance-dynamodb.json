{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Resources for the new data storage solution for performance.", "Resources": {"DynamoPerformanceDataTable": {"Type": "AWS::DynamoDB::Table", "Properties": {"TableName": "performance-data", "AttributeDefinitions": [{"AttributeName": "rk_eq_id", "AttributeType": "S"}, {"AttributeName": "time_from", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "rk_eq_id", "KeyType": "HASH"}, {"AttributeName": "time_from", "KeyType": "RANGE"}], "BillingMode": "PAY_PER_REQUEST", "SSESpecification": {"KMSMasterKeyId": {"Fn::ImportValue": "performance-downtime-service-dynamodb-kms-key-alias"}, "SSEEnabled": true, "SSEType": "KMS"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}}}}, "Outputs": {"DynamoPerformanceDataTableArn": {"Description": "DynamodDB table arn for stored performance data", "Value": {"Fn::GetAtt": ["DynamoPerformanceDataTable", "<PERSON><PERSON>"]}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}:dynamodb-performance-data-arn"}}}}}