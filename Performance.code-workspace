{
  "folders": [
    {
      "name": "🚢 deployment",
      "path": "deployment"
    },
    {
      "name": "categories-service",
      "path": "categories-service"
    },
    {
      "name": "checkmat-curve-service",
      "path": "checkmat-curve-service"
    },
    {
      "name": "comments-service",
      "path": "comments-service"
    },
    {
      "name": "custom-database-user-creator",
      "path": "custom-database-user-creator"
    },
    {
      "name": "customer-settings-service",
      "path": "customer-settings-service"
    },
    {
      "name": "error-list-service",
      "path": "error-list-service"
    },
    {
      "name": "performance-analytics-service",
      "path": "performance-analytics-service"
    },
    {
      "name": "performance-analytics-kpi-service",
      "path": "performance-analytics-kpi-service"
    },
    {
      "name": "performance-analytics-service-config-generator",
      "path": "performance-analytics-service-config-generator"
    },
    {
      "name": "performance-analytics-units-service",
      "path": "performance-analytics-units-service"
    },
    {
      "name": "products-speeds-service",
      "path": "products-speeds-service"
    },
    {
      "name": "threshold-service",
      "path": "threshold-service"
    },
    {
      "name": "🏠 workspace",
      "path": "."
    }
  ],
  "settings": {
    "cSpell.words": [
      "boto",
      "dynamodb",
      "GREENGRASS",
      "Syskron",
      "greengrass",
      "greengrasssdk",
      "parametrize",
      "pylint",
      "pytest",
      "uncategorized",
      "zait"
    ],
    "files.associations": {
      ".cfnlintrc": "yaml"
    },
    "files.trimFinalNewlines": true,
    "ruff.importStrategy": "fromEnvironment",
    "ruff.interpreter": ["${workspaceFolder}/.venv/bin/python"],
    "python.testing.unittestEnabled": false,
    "mypy-type-checker.cwd": "${workspaceFolder}",
    "mypy-type-checker.interpreter": ["${workspaceFolder}/.venv/bin/python"],
    "python.testing.nosetestsEnabled": false,
    "python.linting.banditEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.flake8Args": [
      "--max-line-length=120",
      "--ignore=E203, W503",
      "--verbose"
    ],
    "python.analysis.useImportHeuristic": true, // This is necessary to detect absolute imports
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
      "--skip-string-normalization",
      "--line-length",
      "120"
    ],
    "editor.formatOnSave": true
  },
  "extensions": {
    "recommendations": [
      "charliermarsh.ruff",
      "ms-python.python",
      "ms-python.mypy-type-checker"
    ]
  }
}
