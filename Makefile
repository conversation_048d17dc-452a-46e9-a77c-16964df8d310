SRC_DIR := src
BUNDLE_DIR := dist
CDK_DIR :=
CDK_TEST_DIR :=
_TEST_FILES_OR_DIRS := $(strip $(subst ",,$(TEST_FILES_OR_DIRS)))
# Set a PLATFORM variable to build for a specific platform
PLATFORM :=

ZIP_FILE :=

# Path definitions for lambda-layer
## Install path of Poetry dependencies relative to repository root-dir
LAMBDA_LAYER_PYTHON_LIBS := build/libs/python
## Location of shared sources relative to repository root-dir
LAMBDA_LAYER_SHARED_LIBS := $(LAMBDA_LAYER_PYTHON_LIBS)/shared
## Location of shared sources relative to repository root-dir
SHARED_SRC_PATH := $(SRC_DIR)/shared

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) |  awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

###############################
#######    Install    #########
###############################

.PHONY: install
install: install-poetry install-pre-commit

.PHONY: install-ci
install-ci: install-poetry configure-ssh

.PHONY: configure-ssh
configure-ssh:
	@echo "███ Configuring ssh..."
	git config url."ssh://****************************".insteadOf "https://pd.bitbucket.syskron.com/scm"
	git fetch --all --tags
	@echo

.PHONY: install-poetry
install-poetry:
	@echo "███ Running poetry install..."
	poetry config virtualenvs.in-project true
	@poetry config http-basic.syskron-jfrog $(PYPI_USER) $(PYPI_PASS)
	poetry install --no-root
	@echo

.PHONY: install-pre-commit
install-pre-commit:
	@echo "███ Running pre-commit sync..."
	poetry run pre-commit install --install-hooks
	@echo

.PHONY: update
update:
	@echo "███ Running poetry update"
	poetry update
	@echo

.PHONY: lock
lock:
	@echo "███ Running poetry lock"
	poetry lock
	@echo

################################################
#########    Update shared configs    ##########
################################################

.PHONY: pull-configs
pull-configs:
	@echo "███ Pulling configs..."
	@$(eval OUTPUT_WITH_RC := $(shell git submodule update --remote --init --recursive --rebase; echo $$?))
	@$(eval RETURN_CODE := $(lastword $(OUTPUT_WITH_RC)))

	@if [ $(RETURN_CODE) -ne 0 ]; then \
		echo "Updating submodules failed. Please update the submodule manually."; \
		exit 1; \
	fi

	@if [ "$(OUTPUT_WITH_RC)" = "0" ]; then \
		echo "Developer configs are already up to date."; \
	else \
		echo "########################## Pulling developer configs ########################"; \
		echo "The latest changes of developer configs have been pulled. Please commit them."; \
		echo "#############################################################################"; \
		exit 1; \
	fi

################################################
#######    Bootstrap shared configs    #########
################################################

.PHONY: link-configs
link-configs:
	@echo "███ Creating symlinks ..."
	@cd $(shell git rev-parse --show-toplevel) && \
	rm -f .editorconfig .pre-commit-config.yaml commitlint.config.js && \
	ln -s .configs/.editorconfig . && \
	ln -s .configs/.pre-commit-config.yaml . && \
	ln -s .configs/commitlint.config.js . && \
	echo "Success: Created symbolic links." || \
	echo "Failure: Unable to create symbolic links.";
	@echo

#################################
#########    Format    ##########
#################################

.PHONY: format
format:
	@echo "███ Running format..."
	poetry run ruff format
	@echo

###############################
#########    Lint    ##########
###############################

.PHONY: lint
lint: typecheck
	@echo "███ Running lint..."
	poetry run ruff check
	@echo

.PHONY: lint-fix
lint-fix:
	@echo "███ Running lint with fixes..."
	poetry run ruff check --fix
	@echo

.PHONY: lint-unsafe-fix
lint-unsafe-fix:
	@echo "███ Running lint with unsafe fixes..."
	poetry run ruff check --fix --unsafe-fixes
	@echo

####################################
#########    Typecheck    ##########
####################################

.PHONY: typecheck
typecheck:
	@echo "███ Running typecheck ..."
	poetry run python -c "import sys; import toml; import re; config = toml.load('pyproject.toml'); exclude = tuple(config.get('tool', {}).get('mypy', {}).get('exclude', [])); files = [f for f in sys.argv[1:] if not any(re.match(pattern, f) for pattern in exclude)]; print(' '.join(files));" $$(git ls-files -- '*.py') | xargs poetry run mypy;\
	RESULT=$$?;\
	echo;\
	exit $$RESULT

###############################
#########    Test    ##########
###############################
.PHONY: test
test:
	@echo "███ Running test..."
	poetry run pytest --cov-report term-missing --cov=. ${_TEST_FILES_OR_DIRS}
	poetry run coverage xml
	@echo

################################
#########   Clean    ###########
################################
.PHONY: clean
clean:
	@echo "███ Running clean..."
	rm -rf ${BUNDLE_DIR}
	rm -rf cdk.out/
	rm -f requirements.txt
	@echo

################################
#########   Bundle   ###########
################################

.PHONY: bundle
bundle: clean
	@echo "███ Running bundle ..."
ifneq ($(PLATFORM),)
	mkdir -p ${BUNDLE_DIR}
	poetry export --without-hashes -f requirements.txt --output requirements.txt --with-credentials
	poetry run pip install --platform $(PLATFORM) --only-binary=:all: -r requirements.txt -t ${BUNDLE_DIR}
	cp -a ${SRC_DIR}/. ${BUNDLE_DIR}
	rm requirements.txt
else
	poetry build --output $(BUNDLE_DIR)
endif
	@echo

.PHONY: bundle-zip
bundle-zip: clean
	@echo "███ Running bundle-zip ..."
	mkdir -p $(BUNDLE_DIR)/libs/ $(BUNDLE_DIR)/distributions/
	poetry export --output requirements.txt --with-credentials
ifneq ($(PLATFORM),)
	poetry run pip install --platform $(PLATFORM) --no-deps -r requirements.txt -t $(BUNDLE_DIR)/libs
else
	poetry run pip install --no-deps -r requirements.txt -t $(BUNDLE_DIR)/libs
endif
	cd $(BUNDLE_DIR)/libs/; zip -qr ../../$(BUNDLE_DIR)/distributions/$(ZIP_FILE) . ; cd ../../
	cd $(SRC_DIR)/; zip -qr ../$(BUNDLE_DIR)/distributions/$(ZIP_FILE) . ; cd ../
	@echo

.PHONY: bundle-lambda-layers
bundle-lambda-layers: clean
	@echo "███ Running bundle-lambda-layers ..."
	mkdir -p ${LAMBDA_LAYER_SHARED_LIBS}
	poetry export --output requirements.txt --with-credentials
ifneq ($(PLATFORM),)
	poetry run pip install --no-deps -r requirements.txt -t ${LAMBDA_LAYER_PYTHON_LIBS} --platform $(PLATFORM)
else
	poetry run pip install --no-deps -r requirements.txt -t ${LAMBDA_LAYER_PYTHON_LIBS}
endif
	cp -R ${SHARED_SRC_PATH}/* ${LAMBDA_LAYER_SHARED_LIBS}
	rm requirements.txt
	@echo

.PHONY: bundle-lambda-layers-as-binary
bundle-lambda-layers-as-binary: clean
	@echo "███ Running bundle-lambda-layers-as-binary ..."
	poetry export --output requirements.txt --with-credentials
ifneq ($(PLATFORM),)
	poetry run pip install --no-deps -r requirements.txt -t ${LAMBDA_LAYER_PYTHON_LIBS} --platform $(PLATFORM)
else
	poetry run pip install --no-deps -r requirements.txt -t ${LAMBDA_LAYER_PYTHON_LIBS}
endif
	rm requirements.txt
	@echo

################################
#########   Publish   ##########
################################

.PHONY: publish
publish:
	@echo "███ Running publish ..."
	poetry config repositories.syskron-jfrog-upload https://syskronx.jfrog.io/artifactory/api/pypi/pypi-local
	@poetry publish -r syskron-jfrog-upload -u $(PYPI_USER) -p $(PYPI_PASS)
	@echo

###############################
#####    Miscellaneous    #####
###############################

.PHONY: print-env
print-env:
	@echo "███ Printing environment variables..."
	printenv | sort
	@echo
