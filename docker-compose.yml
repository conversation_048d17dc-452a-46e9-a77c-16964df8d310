services:
  performance-analytics-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}
    environment:
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
      - AWS_SESSION_TOKEN
      - AWS_REGION
      - AWS_DEFAULT_REGION
      - AWS_PROFILE
      - AWS_CONFIG_FILE
      - PYPI_USER
      - PYPI_PASS
      - AWS_XRAY_SDK_ENABLED=false
    stdin_open: true
    links:
      - db
    working_dir: /app
    volumes:
      - ./:/app/

  db:
    image: postgres:15.2-alpine
    restart: always
    environment:
      POSTGRES_PASSWORD: example
      POSTGRES_DB: performance
      POSTGRES_HOST_AUTH_METHOD: "trust"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  test-checkmat-curve-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-checkmat-curve-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-comments-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-comments-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./.configs:/app/.configs

  test-custom-database-user-creator:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-custom-database-user-creator
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}
    depends_on:
      db:
        condition: service_healthy

  test-customer-settings-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-customer-settings-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-performance-analytics-kpi-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-performance-analytics-kpi-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-performance-analytics-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-performance-analytics-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-performance-analytics-service-config-generator:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-performance-analytics-service-config-generator
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-performance-analytics-units-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-performance-analytics-units-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-products-speeds-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-products-speeds-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}

  test-threshold-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: test-threshold-service
      args:
        PYPI_USER: ${PYPI_USER}
        PYPI_PASS: ${PYPI_PASS}
